{"res": {"input_path": "logs\\debug_uuid_screenshot_20250817_140551.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[116, 85], [220, 81], [221, 106], [117, 110]], [[252, 87], [329, 87], [329, 106], [252, 106]], [[362, 86], [420, 86], [420, 108], [362, 108]], [[471, 86], [529, 86], [529, 108], [471, 108]], [[579, 89], [641, 89], [641, 105], [579, 105]], [[6, 109], [67, 113], [66, 136], [5, 132]], [[116, 110], [177, 113], [176, 136], [115, 134]], [[227, 109], [288, 113], [286, 136], [225, 132]], [[336, 109], [397, 113], [395, 136], [334, 132]], [[447, 111], [639, 113], [639, 135], [447, 133]], [[32, 229], [89, 229], [89, 248], [32, 248]], [[144, 231], [198, 231], [198, 247], [144, 247]], [[254, 231], [308, 231], [308, 247], [254, 247]], [[362, 229], [420, 229], [420, 248], [362, 248]], [[472, 229], [528, 229], [528, 248], [472, 248]], [[583, 229], [638, 229], [638, 248], [583, 248]], [[6, 252], [51, 255], [49, 278], [5, 275]], [[116, 251], [155, 255], [153, 280], [113, 276]], [[225, 252], [265, 255], [263, 280], [223, 276]], [[335, 252], [375, 255], [373, 280], [333, 276]], [[445, 252], [485, 255], [483, 280], [443, 276]], [[556, 252], [595, 255], [593, 279], [554, 275]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图1.jpg", "主图4.jpg", "主图3.jpg", "主图2.jpg", "c2bfd6e5-36cd.. 800x1200.jpg", "900x900", "900x900", "900x900", "900x900", "900×900", "900x900", "10.jpg", "9.jpg", "8.jpg", "7.jpg", "6.jpg", "5.jpg"], "rec_scores": [0.9677809476852417, 0.8605974316596985, 0.9511216878890991, 0.9571443200111389, 0.9398478269577026, 0.9866586923599243, 0.9797891974449158, 0.9388708472251892, 0.9832853674888611, 0.9792566895484924, 0.9503191113471985, 0.9310354590415955, 0.9635047316551208, 0.9594841599464417, 0.9314823746681213, 0.9267443418502808, 0.9370981454849243, 0.9459753036499023, 0.9907647371292114, 0.9969016313552856, 0.990766167640686, 0.9963321685791016, 0.9944041967391968], "rec_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[116, 85], [220, 81], [221, 106], [117, 110]], [[252, 87], [329, 87], [329, 106], [252, 106]], [[362, 86], [420, 86], [420, 108], [362, 108]], [[471, 86], [529, 86], [529, 108], [471, 108]], [[579, 89], [641, 89], [641, 105], [579, 105]], [[6, 109], [67, 113], [66, 136], [5, 132]], [[116, 110], [177, 113], [176, 136], [115, 134]], [[227, 109], [288, 113], [286, 136], [225, 132]], [[336, 109], [397, 113], [395, 136], [334, 132]], [[447, 111], [639, 113], [639, 135], [447, 133]], [[32, 229], [89, 229], [89, 248], [32, 248]], [[144, 231], [198, 231], [198, 247], [144, 247]], [[254, 231], [308, 231], [308, 247], [254, 247]], [[362, 229], [420, 229], [420, 248], [362, 248]], [[472, 229], [528, 229], [528, 248], [472, 248]], [[583, 229], [638, 229], [638, 248], [583, 248]], [[6, 252], [51, 255], [49, 278], [5, 275]], [[116, 251], [155, 255], [153, 280], [113, 276]], [[225, 252], [265, 255], [263, 280], [223, 276]], [[335, 252], [375, 255], [373, 280], [333, 276]], [[445, 252], [485, 255], [483, 280], [443, 276]], [[556, 252], [595, 255], [593, 279], [554, 275]]], "rec_boxes": [[32, 87, 89, 106], [116, 81, 221, 110], [252, 87, 329, 106], [362, 86, 420, 108], [471, 86, 529, 108], [579, 89, 641, 105], [5, 109, 67, 136], [115, 110, 177, 136], [225, 109, 288, 136], [334, 109, 397, 136], [447, 111, 639, 135], [32, 229, 89, 248], [144, 231, 198, 247], [254, 231, 308, 247], [362, 229, 420, 248], [472, 229, 528, 248], [583, 229, 638, 248], [5, 252, 51, 278], [113, 251, 155, 280], [223, 252, 265, 280], [333, 252, 375, 280], [443, 252, 485, 280], [554, 252, 595, 279]]}}