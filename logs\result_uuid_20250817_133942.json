{"res": {"input_path": "logs\\debug_uuid_screenshot_20250817_133942.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[37, 44], [96, 44], [96, 82], [37, 82]], [[32, 87], [89, 87], [89, 106], [32, 106]], [[140, 86], [200, 86], [200, 108], [140, 108]], [[251, 86], [311, 86], [311, 108], [251, 108]], [[359, 83], [420, 86], [419, 109], [358, 106]], [[469, 84], [531, 84], [531, 109], [469, 109]], [[576, 84], [645, 84], [645, 109], [576, 109]], [[5, 109], [69, 112], [68, 137], [4, 135]], [[115, 108], [179, 111], [178, 137], [114, 135]], [[225, 108], [289, 111], [288, 137], [224, 135]], [[335, 108], [399, 111], [398, 137], [333, 135]], [[446, 111], [640, 112], [640, 136], [446, 135]], [[32, 230], [90, 230], [90, 249], [32, 249]], [[142, 229], [200, 229], [200, 248], [142, 248]], [[251, 228], [310, 228], [310, 250], [251, 250]], [[362, 229], [420, 229], [420, 248], [362, 248]], [[471, 228], [530, 228], [530, 250], [471, 250]], [[583, 230], [638, 230], [638, 249], [583, 249]], [[641, 236], [659, 236], [659, 247], [641, 247]], [[5, 252], [45, 255], [43, 280], [3, 276]], [[114, 250], [156, 253], [154, 281], [112, 277]], [[224, 250], [266, 253], [264, 281], [222, 277]], [[334, 250], [376, 253], [374, 281], [332, 277]], [[445, 251], [485, 255], [483, 280], [443, 276]], [[555, 251], [595, 254], [593, 280], [553, 276]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图2.jpg", "主图3.jpg", "主图1.jpg", "主图4.jpg", "dac5c718-0dd3...800x1200.jpg", "900x900", "900x900", "900x900", "900x900", "900x900", "900x900", "5.jpg", "6.jpg", "4.jpg", "2.jpg", "3.jpg", "1.jpg"], "rec_scores": [0.9789878726005554, 0.925812304019928, 0.9229245185852051, 0.928439199924469, 0.8470565676689148, 0.9744932055473328, 0.9690300226211548, 0.9310798645019531, 0.9172719120979309, 0.9500246047973633, 0.8700465559959412, 0.944041907787323, 0.9655447006225586, 0.9359835386276245, 0.9583325982093811, 0.9245800375938416, 0.9365041851997375, 0.9966920614242554, 0.9798328280448914, 0.9863120317459106, 0.9931166768074036, 0.9931371808052063, 0.899014949798584], "rec_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[140, 86], [200, 86], [200, 108], [140, 108]], [[251, 86], [311, 86], [311, 108], [251, 108]], [[359, 83], [420, 86], [419, 109], [358, 106]], [[469, 84], [531, 84], [531, 109], [469, 109]], [[576, 84], [645, 84], [645, 109], [576, 109]], [[5, 109], [69, 112], [68, 137], [4, 135]], [[115, 108], [179, 111], [178, 137], [114, 135]], [[225, 108], [289, 111], [288, 137], [224, 135]], [[335, 108], [399, 111], [398, 137], [333, 135]], [[446, 111], [640, 112], [640, 136], [446, 135]], [[32, 230], [90, 230], [90, 249], [32, 249]], [[142, 229], [200, 229], [200, 248], [142, 248]], [[251, 228], [310, 228], [310, 250], [251, 250]], [[362, 229], [420, 229], [420, 248], [362, 248]], [[471, 228], [530, 228], [530, 250], [471, 250]], [[583, 230], [638, 230], [638, 249], [583, 249]], [[5, 252], [45, 255], [43, 280], [3, 276]], [[114, 250], [156, 253], [154, 281], [112, 277]], [[224, 250], [266, 253], [264, 281], [222, 277]], [[334, 250], [376, 253], [374, 281], [332, 277]], [[445, 251], [485, 255], [483, 280], [443, 276]], [[555, 251], [595, 254], [593, 280], [553, 276]]], "rec_boxes": [[32, 87, 89, 106], [140, 86, 200, 108], [251, 86, 311, 108], [358, 83, 420, 109], [469, 84, 531, 109], [576, 84, 645, 109], [4, 109, 69, 137], [114, 108, 179, 137], [224, 108, 289, 137], [333, 108, 399, 137], [446, 111, 640, 136], [32, 230, 90, 249], [142, 229, 200, 248], [251, 228, 310, 250], [362, 229, 420, 248], [471, 228, 530, 250], [583, 230, 638, 249], [3, 252, 45, 280], [112, 250, 156, 281], [222, 250, 266, 281], [332, 250, 376, 281], [443, 251, 485, 280], [553, 251, 595, 280]]}}