{"res": {"input_path": "logs\\debug_800x1200_screenshot_20250826_173959.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[34, 91], [91, 91], [91, 110], [34, 110]], [[144, 93], [201, 93], [201, 109], [144, 109]], [[254, 91], [313, 91], [313, 110], [254, 110]], [[364, 91], [422, 91], [422, 110], [364, 110]], [[474, 91], [530, 91], [530, 110], [474, 110]], [[579, 91], [645, 91], [645, 110], [579, 110]], [[8, 114], [70, 118], [69, 140], [7, 136]], [[118, 113], [180, 117], [179, 140], [117, 136]], [[228, 113], [290, 117], [288, 140], [226, 136]], [[338, 113], [400, 117], [398, 140], [336, 136]], [[448, 115], [643, 117], [642, 139], [448, 137]], [[34, 233], [91, 233], [91, 252], [34, 252]], [[144, 233], [201, 233], [201, 252], [144, 252]], [[255, 233], [311, 233], [311, 252], [255, 252]], [[365, 233], [421, 233], [421, 252], [365, 252]], [[475, 233], [530, 233], [530, 252], [475, 252]], [[584, 233], [640, 233], [640, 252], [584, 252]], [[9, 255], [53, 259], [51, 284], [6, 279]], [[117, 256], [157, 259], [155, 284], [115, 280]], [[227, 256], [267, 259], [265, 284], [225, 280]], [[337, 256], [377, 259], [375, 284], [335, 280]], [[448, 256], [487, 259], [485, 284], [446, 280]], [[558, 256], [597, 259], [595, 284], [556, 280]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图3.jpg", "主图2.jpg", "主图4jpg", "主图1.jpg", "159406e5-d175.. 800x1200.jpg", "900x900", "900x900", "900x900", "900x900", "900x900", "900×900", "10.jpg", "9.jpg", "8.jpg", "7.jpg", "6.jpg", "5.jpg"], "rec_scores": [0.9684497117996216, 0.9447264075279236, 0.9756380319595337, 0.9732646942138672, 0.9776328802108765, 0.9870017766952515, 0.9249511361122131, 0.9850023984909058, 0.9879143238067627, 0.9499945640563965, 0.895440399646759, 0.9599581360816956, 0.9592412710189819, 0.9552739262580872, 0.9769031405448914, 0.9307412505149841, 0.9300256967544556, 0.9402090907096863, 0.9945552945137024, 0.9969016313552856, 0.990766167640686, 0.9968382120132446, 0.995775580406189], "rec_polys": [[[34, 91], [91, 91], [91, 110], [34, 110]], [[144, 93], [201, 93], [201, 109], [144, 109]], [[254, 91], [313, 91], [313, 110], [254, 110]], [[364, 91], [422, 91], [422, 110], [364, 110]], [[474, 91], [530, 91], [530, 110], [474, 110]], [[579, 91], [645, 91], [645, 110], [579, 110]], [[8, 114], [70, 118], [69, 140], [7, 136]], [[118, 113], [180, 117], [179, 140], [117, 136]], [[228, 113], [290, 117], [288, 140], [226, 136]], [[338, 113], [400, 117], [398, 140], [336, 136]], [[448, 115], [643, 117], [642, 139], [448, 137]], [[34, 233], [91, 233], [91, 252], [34, 252]], [[144, 233], [201, 233], [201, 252], [144, 252]], [[255, 233], [311, 233], [311, 252], [255, 252]], [[365, 233], [421, 233], [421, 252], [365, 252]], [[475, 233], [530, 233], [530, 252], [475, 252]], [[584, 233], [640, 233], [640, 252], [584, 252]], [[9, 255], [53, 259], [51, 284], [6, 279]], [[117, 256], [157, 259], [155, 284], [115, 280]], [[227, 256], [267, 259], [265, 284], [225, 280]], [[337, 256], [377, 259], [375, 284], [335, 280]], [[448, 256], [487, 259], [485, 284], [446, 280]], [[558, 256], [597, 259], [595, 284], [556, 280]]], "rec_boxes": [[34, 91, 91, 110], [144, 93, 201, 109], [254, 91, 313, 110], [364, 91, 422, 110], [474, 91, 530, 110], [579, 91, 645, 110], [7, 114, 70, 140], [117, 113, 180, 140], [226, 113, 290, 140], [336, 113, 400, 140], [448, 115, 643, 139], [34, 233, 91, 252], [144, 233, 201, 252], [255, 233, 311, 252], [365, 233, 421, 252], [475, 233, 530, 252], [584, 233, 640, 252], [6, 255, 53, 284], [115, 256, 157, 284], [225, 256, 267, 284], [335, 256, 377, 284], [446, 256, 487, 284], [556, 256, 597, 284]]}}