{"res": {"input_path": "logs\\debug_uuid_screenshot_20250820_003640.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[33, 89], [88, 89], [88, 105], [33, 105]], [[142, 87], [200, 87], [200, 106], [142, 106]], [[252, 87], [310, 87], [310, 106], [252, 106]], [[362, 87], [420, 87], [420, 106], [362, 106]], [[473, 89], [529, 89], [529, 105], [473, 105]], [[577, 86], [644, 86], [644, 108], [577, 108]], [[6, 109], [68, 113], [67, 136], [5, 132]], [[116, 110], [177, 113], [176, 136], [115, 134]], [[226, 110], [288, 113], [287, 136], [225, 134]], [[335, 110], [398, 113], [397, 136], [334, 134]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[32, 230], [90, 230], [90, 246], [32, 246]], [[141, 225], [201, 228], [200, 250], [140, 247]], [[252, 230], [309, 230], [309, 249], [252, 249]], [[361, 227], [421, 227], [421, 249], [361, 249]], [[473, 229], [528, 229], [528, 248], [473, 248]], [[582, 231], [636, 231], [636, 247], [582, 247]], [[6, 251], [51, 257], [49, 280], [3, 275]], [[116, 251], [161, 255], [159, 280], [113, 275]], [[227, 251], [272, 255], [270, 280], [224, 275]], [[335, 252], [375, 255], [373, 280], [333, 276]], [[446, 251], [492, 255], [490, 280], [444, 275]], [[555, 252], [595, 255], [593, 280], [553, 276]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图3.jpg", "主图4.jpg", "主图2.jpg", "主图1.jpg", "316813f8-9d6d.. 800x1200.jpg", "900x900", "900×900", "900x900", "900×900", "900x900", "900x900", "13,jpg", "12.jpg", "11,jpg", "9.jpg", "10.jpg", "8.jpg"], "rec_scores": [0.9733399748802185, 0.9641619920730591, 0.9681664705276489, 0.9530478715896606, 0.9773179292678833, 0.9758001565933228, 0.9683977365493774, 0.9388708472251892, 0.9805187582969666, 0.9557353258132935, 0.8599898219108582, 0.9619762301445007, 0.869364857673645, 0.9709164500236511, 0.9129514694213867, 0.943508505821228, 0.9452887773513794, 0.9218440651893616, 0.9878075122833252, 0.9678359031677246, 0.9945552945137024, 0.958449125289917, 0.9969016313552856], "rec_polys": [[[33, 89], [88, 89], [88, 105], [33, 105]], [[142, 87], [200, 87], [200, 106], [142, 106]], [[252, 87], [310, 87], [310, 106], [252, 106]], [[362, 87], [420, 87], [420, 106], [362, 106]], [[473, 89], [529, 89], [529, 105], [473, 105]], [[577, 86], [644, 86], [644, 108], [577, 108]], [[6, 109], [68, 113], [67, 136], [5, 132]], [[116, 110], [177, 113], [176, 136], [115, 134]], [[226, 110], [288, 113], [287, 136], [225, 134]], [[335, 110], [398, 113], [397, 136], [334, 134]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[32, 230], [90, 230], [90, 246], [32, 246]], [[141, 225], [201, 228], [200, 250], [140, 247]], [[252, 230], [309, 230], [309, 249], [252, 249]], [[361, 227], [421, 227], [421, 249], [361, 249]], [[473, 229], [528, 229], [528, 248], [473, 248]], [[582, 231], [636, 231], [636, 247], [582, 247]], [[6, 251], [51, 257], [49, 280], [3, 275]], [[116, 251], [161, 255], [159, 280], [113, 275]], [[227, 251], [272, 255], [270, 280], [224, 275]], [[335, 252], [375, 255], [373, 280], [333, 276]], [[446, 251], [492, 255], [490, 280], [444, 275]], [[555, 252], [595, 255], [593, 280], [553, 276]]], "rec_boxes": [[33, 89, 88, 105], [142, 87, 200, 106], [252, 87, 310, 106], [362, 87, 420, 106], [473, 89, 529, 105], [577, 86, 644, 108], [5, 109, 68, 136], [115, 110, 177, 136], [225, 110, 288, 136], [334, 110, 398, 136], [446, 111, 641, 135], [32, 230, 90, 246], [140, 225, 201, 250], [252, 230, 309, 249], [361, 227, 421, 249], [473, 229, 528, 248], [582, 231, 636, 247], [3, 251, 51, 280], [113, 251, 161, 280], [224, 251, 272, 280], [333, 252, 375, 280], [444, 251, 492, 280], [553, 252, 595, 280]]}}