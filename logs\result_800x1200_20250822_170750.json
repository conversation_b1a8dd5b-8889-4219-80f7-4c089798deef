{"res": {"input_path": "logs\\debug_800x1200_screenshot_20250822_170750.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[36, 93], [90, 93], [90, 109], [36, 109]], [[145, 93], [199, 93], [199, 109], [145, 109]], [[253, 90], [311, 90], [311, 109], [253, 109]], [[366, 93], [419, 93], [419, 109], [366, 109]], [[476, 93], [529, 93], [529, 109], [476, 109]], [[580, 91], [645, 91], [645, 110], [580, 110]], [[8, 114], [69, 118], [68, 140], [7, 136]], [[118, 114], [179, 118], [178, 140], [116, 136]], [[228, 113], [290, 118], [288, 142], [226, 136]], [[338, 113], [399, 117], [397, 140], [336, 136]], [[449, 116], [643, 118], [642, 139], [449, 137]], [[35, 235], [90, 235], [90, 251], [35, 251]], [[145, 235], [200, 235], [200, 251], [145, 251]], [[255, 235], [310, 235], [310, 251], [255, 251]], [[364, 233], [421, 233], [421, 252], [364, 252]], [[475, 235], [529, 235], [529, 251], [475, 251]], [[586, 235], [639, 235], [639, 251], [586, 251]], [[8, 259], [46, 259], [46, 282], [8, 282]], [[117, 256], [157, 259], [155, 284], [115, 280]], [[227, 256], [267, 259], [265, 284], [225, 280]], [[337, 258], [376, 258], [376, 282], [337, 282]], [[448, 256], [510, 260], [508, 282], [446, 278]], [[557, 256], [619, 259], [618, 281], [556, 279]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图1.jpg", "主图3.jpg", "主图4.jpg", "主图2.jpg", "ee05e106-45e9...800x1200.jpg", "900x898", "900x898", "900x898", "900x898", "800x800", "800x800", "5.jpg", "4.jpg", "3.jpg", "2.jpg", "主图3.jpg", "主图4.jpg"], "rec_scores": [0.9637836217880249, 0.9642896056175232, 0.9750577807426453, 0.9686668515205383, 0.9736557006835938, 0.984310507774353, 0.9697061777114868, 0.9256677031517029, 0.958002507686615, 0.9792531728744507, 0.8925743103027344, 0.9753166437149048, 0.9896138310432434, 0.9857474565505981, 0.9841718077659607, 0.9832406044006348, 0.9803920984268188, 0.9829872250556946, 0.9904114007949829, 0.9970253705978394, 0.9661633372306824, 0.935663104057312, 0.9531570076942444], "rec_polys": [[[36, 93], [90, 93], [90, 109], [36, 109]], [[145, 93], [199, 93], [199, 109], [145, 109]], [[253, 90], [311, 90], [311, 109], [253, 109]], [[366, 93], [419, 93], [419, 109], [366, 109]], [[476, 93], [529, 93], [529, 109], [476, 109]], [[580, 91], [645, 91], [645, 110], [580, 110]], [[8, 114], [69, 118], [68, 140], [7, 136]], [[118, 114], [179, 118], [178, 140], [116, 136]], [[228, 113], [290, 118], [288, 142], [226, 136]], [[338, 113], [399, 117], [397, 140], [336, 136]], [[449, 116], [643, 118], [642, 139], [449, 137]], [[35, 235], [90, 235], [90, 251], [35, 251]], [[145, 235], [200, 235], [200, 251], [145, 251]], [[255, 235], [310, 235], [310, 251], [255, 251]], [[364, 233], [421, 233], [421, 252], [364, 252]], [[475, 235], [529, 235], [529, 251], [475, 251]], [[586, 235], [639, 235], [639, 251], [586, 251]], [[8, 259], [46, 259], [46, 282], [8, 282]], [[117, 256], [157, 259], [155, 284], [115, 280]], [[227, 256], [267, 259], [265, 284], [225, 280]], [[337, 258], [376, 258], [376, 282], [337, 282]], [[448, 256], [510, 260], [508, 282], [446, 278]], [[557, 256], [619, 259], [618, 281], [556, 279]]], "rec_boxes": [[36, 93, 90, 109], [145, 93, 199, 109], [253, 90, 311, 109], [366, 93, 419, 109], [476, 93, 529, 109], [580, 91, 645, 110], [7, 114, 69, 140], [116, 114, 179, 140], [226, 113, 290, 142], [336, 113, 399, 140], [449, 116, 643, 139], [35, 235, 90, 251], [145, 235, 200, 251], [255, 235, 310, 251], [364, 233, 421, 252], [475, 235, 529, 251], [586, 235, 639, 251], [8, 259, 46, 282], [115, 256, 157, 284], [225, 256, 267, 284], [337, 258, 376, 282], [446, 256, 510, 282], [556, 256, 619, 281]]}}