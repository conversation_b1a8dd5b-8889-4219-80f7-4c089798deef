[15:46:25] [    INFO] - 日志系统初始化完成
[15:46:25] [    INFO] - 测试4.py开始执行...
[15:46:25] [    INFO] - pyautogui设置完成
[15:46:25] [    INFO] - 开始执行主函程序...
[15:46:25] [    INFO] - 开始初始化PaddleOCR...
[15:46:31] [   ERROR] - 查找点击图片出错: 
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 172, in wrapper
    return wrappedFunction(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 210, in locateOnScreen
    return pyscreeze.locateOnScreen(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 405, in locateOnScreen
    retVal = locate(image, screenshotIm, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 383, in locate
    points = tuple(locateAll(needleImage, haystackImage, **kwargs))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 257, in _locateAll_opencv
    raise ImageNotFoundException('Could not locate the image (highest confidence = %.3f)' % result.max())
pyscreeze.ImageNotFoundException: Could not locate the image (highest confidence = 0.747)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\天猫上款\测试4.py", line 377, in find_and_click_image
    location = pyautogui.locateOnScreen(image_path, confidence=confidence)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 174, in wrapper
    raise ImageNotFoundException  # Raise PyAutoGUI's ImageNotFoundException.
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pyautogui.ImageNotFoundException

[15:46:31] [ WARNING] - 未找到WENZI.png，使用备用坐标(540, 371)
[15:46:32] [   ERROR] - 查找点击图片出错: 
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 172, in wrapper
    return wrappedFunction(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 210, in locateOnScreen
    return pyscreeze.locateOnScreen(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 405, in locateOnScreen
    retVal = locate(image, screenshotIm, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 383, in locate
    points = tuple(locateAll(needleImage, haystackImage, **kwargs))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 257, in _locateAll_opencv
    raise ImageNotFoundException('Could not locate the image (highest confidence = %.3f)' % result.max())
pyscreeze.ImageNotFoundException: Could not locate the image (highest confidence = 0.394)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\天猫上款\测试4.py", line 377, in find_and_click_image
    location = pyautogui.locateOnScreen(image_path, confidence=confidence)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 174, in wrapper
    raise ImageNotFoundException  # Raise PyAutoGUI's ImageNotFoundException.
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pyautogui.ImageNotFoundException

[15:46:32] [ WARNING] - 未找到XINMIAOSHU.png，使用备用坐标(664, 327)
[15:46:42] [   ERROR] - 查找点击图片出错: 
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 172, in wrapper
    return wrappedFunction(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 210, in locateOnScreen
    return pyscreeze.locateOnScreen(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 405, in locateOnScreen
    retVal = locate(image, screenshotIm, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 383, in locate
    points = tuple(locateAll(needleImage, haystackImage, **kwargs))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 257, in _locateAll_opencv
    raise ImageNotFoundException('Could not locate the image (highest confidence = %.3f)' % result.max())
pyscreeze.ImageNotFoundException: Could not locate the image (highest confidence = 0.684)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\天猫上款\测试4.py", line 377, in find_and_click_image
    location = pyautogui.locateOnScreen(image_path, confidence=confidence)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 174, in wrapper
    raise ImageNotFoundException  # Raise PyAutoGUI's ImageNotFoundException.
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pyautogui.ImageNotFoundException

[15:46:42] [ WARNING] - 未找到MIAOSHUTU.png，使用备用坐标(541, 326)
[15:46:45] [   ERROR] - 查找点击图片出错: 
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 172, in wrapper
    return wrappedFunction(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 210, in locateOnScreen
    return pyscreeze.locateOnScreen(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 405, in locateOnScreen
    retVal = locate(image, screenshotIm, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 383, in locate
    points = tuple(locateAll(needleImage, haystackImage, **kwargs))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 257, in _locateAll_opencv
    raise ImageNotFoundException('Could not locate the image (highest confidence = %.3f)' % result.max())
pyscreeze.ImageNotFoundException: Could not locate the image (highest confidence = 0.354)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\天猫上款\测试4.py", line 377, in find_and_click_image
    location = pyautogui.locateOnScreen(image_path, confidence=confidence)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 174, in wrapper
    raise ImageNotFoundException  # Raise PyAutoGUI's ImageNotFoundException.
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pyautogui.ImageNotFoundException

[15:46:45] [ WARNING] - 未找到上传按钮，继续执行后续步骤
[15:47:21] [ WARNING] - 识别到的图片数量(66)超过预期数量(8)，将只保留预设文件名匹配的图片
