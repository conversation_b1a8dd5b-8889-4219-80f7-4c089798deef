[15:22:39] [    INFO] - 日志系统初始化完成
[15:22:39] [    INFO] - 测试4.py开始执行...
[15:22:39] [    INFO] - pyautogui设置完成
[15:22:39] [    INFO] - 开始执行主函程序...
[15:22:39] [    INFO] - 开始初始化PaddleOCR...
[15:22:45] [   ERROR] - 查找点击图片出错: 
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 172, in wrapper
    return wrappedFunction(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 210, in locateOnScreen
    return pyscreeze.locateOnScreen(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 405, in locateOnScreen
    retVal = locate(image, screenshotIm, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 383, in locate
    points = tuple(locateAll(needleImage, haystackImage, **kwargs))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 257, in _locateAll_opencv
    raise ImageNotFoundException('Could not locate the image (highest confidence = %.3f)' % result.max())
pyscreeze.ImageNotFoundException: Could not locate the image (highest confidence = 0.494)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\天猫上款\测试4.py", line 377, in find_and_click_image
    location = pyautogui.locateOnScreen(image_path, confidence=confidence)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 174, in wrapper
    raise ImageNotFoundException  # Raise PyAutoGUI's ImageNotFoundException.
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pyautogui.ImageNotFoundException

[15:22:45] [ WARNING] - 未找到WENZI.png，使用备用坐标(540, 371)
[15:22:47] [   ERROR] - 查找点击图片出错: 
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 172, in wrapper
    return wrappedFunction(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 210, in locateOnScreen
    return pyscreeze.locateOnScreen(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 405, in locateOnScreen
    retVal = locate(image, screenshotIm, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 383, in locate
    points = tuple(locateAll(needleImage, haystackImage, **kwargs))
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyscreeze\__init__.py", line 257, in _locateAll_opencv
    raise ImageNotFoundException('Could not locate the image (highest confidence = %.3f)' % result.max())
pyscreeze.ImageNotFoundException: Could not locate the image (highest confidence = 0.293)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "F:\天猫上款\测试4.py", line 377, in find_and_click_image
    location = pyautogui.locateOnScreen(image_path, confidence=confidence)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pyautogui\__init__.py", line 174, in wrapper
    raise ImageNotFoundException  # Raise PyAutoGUI's ImageNotFoundException.
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
pyautogui.ImageNotFoundException

[15:22:47] [ WARNING] - 未找到XINMIAOSHU.png，使用备用坐标(664, 327)
