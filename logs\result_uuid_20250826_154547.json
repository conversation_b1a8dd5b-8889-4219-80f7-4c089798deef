{"res": {"input_path": "logs\\debug_uuid_screenshot_20250826_154547.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[252, 87], [309, 87], [309, 106], [252, 106]], [[343, 78], [352, 78], [352, 85], [343, 85]], [[33, 89], [88, 89], [88, 105], [33, 105]], [[143, 89], [198, 89], [198, 105], [143, 105]], [[362, 87], [420, 87], [420, 106], [362, 106]], [[472, 87], [528, 87], [528, 106], [472, 106]], [[579, 89], [641, 89], [641, 105], [579, 105]], [[6, 110], [68, 114], [67, 136], [5, 132]], [[116, 109], [178, 113], [177, 136], [114, 132]], [[226, 109], [288, 113], [286, 136], [224, 132]], [[336, 109], [398, 113], [396, 136], [334, 132]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[33, 231], [88, 231], [88, 247], [33, 247]], [[144, 231], [198, 231], [198, 247], [144, 247]], [[252, 229], [309, 229], [309, 248], [252, 248]], [[361, 229], [418, 229], [418, 248], [361, 248]], [[474, 231], [527, 231], [527, 247], [474, 247]], [[583, 231], [636, 231], [636, 247], [583, 247]], [[5, 252], [45, 255], [43, 280], [3, 276]], [[115, 252], [155, 255], [153, 280], [113, 276]], [[226, 251], [265, 255], [263, 280], [223, 276]], [[336, 251], [376, 257], [372, 281], [332, 275]], [[445, 252], [485, 255], [483, 280], [443, 276]], [[555, 252], [595, 255], [593, 280], [553, 276]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图4.jpg", "主图2.jpg", "主图1.jpg", "主图3.jpg", "91858182-1263. 800x1200.jpg", "900x900", "900x900", "900x900", "900x900", "900x900", "900x900", "7.jpg", "5.jpg", "6.jpg", "3.jpg", "4.jpg", "1.jpg"], "rec_scores": [0.9475768208503723, 0.9714799523353577, 0.9724166989326477, 0.969568133354187, 0.9643502235412598, 0.9719808101654053, 0.9253472089767456, 0.9905036091804504, 0.9499945640563965, 0.9226723313331604, 0.8788670301437378, 0.933714747428894, 0.9625315070152283, 0.9421290755271912, 0.9572471976280212, 0.9613157510757446, 0.9560323357582092, 0.990766167640686, 0.9966920614242554, 0.9880026578903198, 0.9933600425720215, 0.9904114007949829, 0.9861119389533997], "rec_polys": [[[252, 87], [309, 87], [309, 106], [252, 106]], [[33, 89], [88, 89], [88, 105], [33, 105]], [[143, 89], [198, 89], [198, 105], [143, 105]], [[362, 87], [420, 87], [420, 106], [362, 106]], [[472, 87], [528, 87], [528, 106], [472, 106]], [[579, 89], [641, 89], [641, 105], [579, 105]], [[6, 110], [68, 114], [67, 136], [5, 132]], [[116, 109], [178, 113], [177, 136], [114, 132]], [[226, 109], [288, 113], [286, 136], [224, 132]], [[336, 109], [398, 113], [396, 136], [334, 132]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[33, 231], [88, 231], [88, 247], [33, 247]], [[144, 231], [198, 231], [198, 247], [144, 247]], [[252, 229], [309, 229], [309, 248], [252, 248]], [[361, 229], [418, 229], [418, 248], [361, 248]], [[474, 231], [527, 231], [527, 247], [474, 247]], [[583, 231], [636, 231], [636, 247], [583, 247]], [[5, 252], [45, 255], [43, 280], [3, 276]], [[115, 252], [155, 255], [153, 280], [113, 276]], [[226, 251], [265, 255], [263, 280], [223, 276]], [[336, 251], [376, 257], [372, 281], [332, 275]], [[445, 252], [485, 255], [483, 280], [443, 276]], [[555, 252], [595, 255], [593, 280], [553, 276]]], "rec_boxes": [[252, 87, 309, 106], [33, 89, 88, 105], [143, 89, 198, 105], [362, 87, 420, 106], [472, 87, 528, 106], [579, 89, 641, 105], [5, 110, 68, 136], [114, 109, 178, 136], [224, 109, 288, 136], [334, 109, 398, 136], [446, 111, 641, 135], [33, 231, 88, 247], [144, 231, 198, 247], [252, 229, 309, 248], [361, 229, 418, 248], [474, 231, 527, 247], [583, 231, 636, 247], [3, 252, 45, 280], [113, 252, 155, 280], [223, 251, 265, 280], [332, 251, 376, 281], [443, 252, 485, 280], [553, 252, 595, 280]]}}