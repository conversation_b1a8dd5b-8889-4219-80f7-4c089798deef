{"res": {"input_path": "logs\\debug_uuid_screenshot_20250825_160456.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[142, 87], [199, 87], [199, 106], [142, 106]], [[251, 83], [310, 86], [309, 108], [250, 105]], [[361, 86], [421, 86], [421, 108], [361, 108]], [[471, 86], [529, 86], [529, 108], [471, 108]], [[578, 87], [642, 87], [642, 106], [578, 106]], [[6, 110], [67, 113], [66, 135], [5, 133]], [[116, 110], [178, 113], [177, 136], [115, 134]], [[225, 109], [289, 112], [288, 137], [224, 135]], [[336, 108], [398, 112], [396, 137], [334, 133]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[28, 229], [93, 229], [93, 248], [28, 248]], [[138, 229], [203, 229], [203, 248], [138, 248]], [[248, 229], [314, 229], [314, 248], [248, 248]], [[359, 231], [424, 231], [424, 247], [359, 247]], [[470, 231], [531, 231], [531, 247], [470, 247]], [[583, 231], [637, 231], [637, 247], [583, 247]], [[6, 252], [45, 256], [43, 280], [3, 276]], [[116, 251], [155, 256], [152, 280], [113, 275]], [[226, 251], [265, 255], [263, 280], [223, 276]], [[335, 251], [375, 254], [373, 280], [333, 276]], [[445, 252], [485, 255], [483, 280], [443, 276]], [[556, 251], [618, 255], [616, 278], [554, 274]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图2.jpg", "主图3.jpg", "主图1.jpg", "主图4.jpg", "b37137bc-d7fc. 800x1200.jpg", "900x1199", "900x1199", "900x1199", "900x1199", "900x1199", "800x800", "5.jpg", "4.jpg", "3.jpg", "2.jpg", "1.jpg", "主图4jpg"], "rec_scores": [0.9677831530570984, 0.9659659266471863, 0.9227026104927063, 0.9131731390953064, 0.8892763257026672, 0.981317400932312, 0.9787489175796509, 0.9628936648368835, 0.967612624168396, 0.9493737816810608, 0.8942933678627014, 0.9702230095863342, 0.9925224184989929, 0.9905357360839844, 0.9912035465240479, 0.9925503134727478, 0.9672912359237671, 0.9965837597846985, 0.9921063184738159, 0.9921211004257202, 0.9762643575668335, 0.9861119389533997, 0.9879143238067627], "rec_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[142, 87], [199, 87], [199, 106], [142, 106]], [[251, 83], [310, 86], [309, 108], [250, 105]], [[361, 86], [421, 86], [421, 108], [361, 108]], [[471, 86], [529, 86], [529, 108], [471, 108]], [[578, 87], [642, 87], [642, 106], [578, 106]], [[6, 110], [67, 113], [66, 135], [5, 133]], [[116, 110], [178, 113], [177, 136], [115, 134]], [[225, 109], [289, 112], [288, 137], [224, 135]], [[336, 108], [398, 112], [396, 137], [334, 133]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[28, 229], [93, 229], [93, 248], [28, 248]], [[138, 229], [203, 229], [203, 248], [138, 248]], [[248, 229], [314, 229], [314, 248], [248, 248]], [[359, 231], [424, 231], [424, 247], [359, 247]], [[470, 231], [531, 231], [531, 247], [470, 247]], [[583, 231], [637, 231], [637, 247], [583, 247]], [[6, 252], [45, 256], [43, 280], [3, 276]], [[116, 251], [155, 256], [152, 280], [113, 275]], [[226, 251], [265, 255], [263, 280], [223, 276]], [[335, 251], [375, 254], [373, 280], [333, 276]], [[445, 252], [485, 255], [483, 280], [443, 276]], [[556, 251], [618, 255], [616, 278], [554, 274]]], "rec_boxes": [[32, 87, 89, 106], [142, 87, 199, 106], [250, 83, 310, 108], [361, 86, 421, 108], [471, 86, 529, 108], [578, 87, 642, 106], [5, 110, 67, 135], [115, 110, 178, 136], [224, 109, 289, 137], [334, 108, 398, 137], [446, 111, 641, 135], [28, 229, 93, 248], [138, 229, 203, 248], [248, 229, 314, 248], [359, 231, 424, 247], [470, 231, 531, 247], [583, 231, 637, 247], [3, 252, 45, 280], [113, 251, 155, 280], [223, 251, 265, 280], [333, 251, 375, 280], [443, 252, 485, 280], [554, 251, 618, 278]]}}