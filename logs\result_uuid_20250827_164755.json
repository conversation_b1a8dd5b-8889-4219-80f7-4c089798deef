{"res": {"input_path": "logs\\debug_uuid_screenshot_20250827_164755.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[142, 87], [200, 87], [200, 106], [142, 106]], [[252, 87], [309, 87], [309, 106], [252, 106]], [[362, 87], [419, 87], [419, 106], [362, 106]], [[472, 87], [529, 87], [529, 106], [472, 106]], [[579, 87], [642, 87], [642, 106], [579, 106]], [[6, 108], [68, 112], [67, 137], [4, 133]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[225, 109], [289, 112], [288, 137], [224, 135]], [[334, 108], [399, 111], [398, 137], [332, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[33, 231], [89, 231], [89, 247], [33, 247]], [[142, 228], [201, 228], [201, 250], [142, 250]], [[252, 229], [309, 229], [309, 248], [252, 248]], [[361, 229], [419, 229], [419, 248], [361, 248]], [[472, 229], [528, 229], [528, 248], [472, 248]], [[582, 229], [639, 229], [639, 248], [582, 248]], [[5, 252], [45, 255], [43, 280], [3, 276]], [[115, 252], [155, 255], [153, 280], [113, 276]], [[225, 251], [265, 254], [263, 280], [223, 276]], [[335, 251], [375, 255], [373, 280], [333, 276]], [[445, 251], [485, 254], [483, 280], [443, 276]], [[556, 251], [618, 255], [616, 278], [554, 274]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图1.jpg", "主图4.jpg", "主图3.jpg", "主图2.jpg", "e7f140ef-785d-…800x1200.jpg", "900x900", "900x900", "900x900", "900x900", "900x900", "800x800", "6.jpg", "5.jpg", "4.jpg", "3.jpg", "1.jpg", "主图1.jpg"], "rec_scores": [0.9735673069953918, 0.9733403325080872, 0.9728068709373474, 0.9793011546134949, 0.9792453050613403, 0.9875429272651672, 0.9431295394897461, 0.9113383293151855, 0.941493809223175, 0.9478397369384766, 0.8804425597190857, 0.9843560457229614, 0.9458600878715515, 0.9674514532089233, 0.9805375933647156, 0.9632470011711121, 0.9850319027900696, 0.9963321685791016, 0.9966920614242554, 0.9806004762649536, 0.9931371808052063, 0.8986754417419434, 0.9499945640563965], "rec_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[142, 87], [200, 87], [200, 106], [142, 106]], [[252, 87], [309, 87], [309, 106], [252, 106]], [[362, 87], [419, 87], [419, 106], [362, 106]], [[472, 87], [529, 87], [529, 106], [472, 106]], [[579, 87], [642, 87], [642, 106], [579, 106]], [[6, 108], [68, 112], [67, 137], [4, 133]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[225, 109], [289, 112], [288, 137], [224, 135]], [[334, 108], [399, 111], [398, 137], [332, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[33, 231], [89, 231], [89, 247], [33, 247]], [[142, 228], [201, 228], [201, 250], [142, 250]], [[252, 229], [309, 229], [309, 248], [252, 248]], [[361, 229], [419, 229], [419, 248], [361, 248]], [[472, 229], [528, 229], [528, 248], [472, 248]], [[582, 229], [639, 229], [639, 248], [582, 248]], [[5, 252], [45, 255], [43, 280], [3, 276]], [[115, 252], [155, 255], [153, 280], [113, 276]], [[225, 251], [265, 254], [263, 280], [223, 276]], [[335, 251], [375, 255], [373, 280], [333, 276]], [[445, 251], [485, 254], [483, 280], [443, 276]], [[556, 251], [618, 255], [616, 278], [554, 274]]], "rec_boxes": [[32, 87, 89, 106], [142, 87, 200, 106], [252, 87, 309, 106], [362, 87, 419, 106], [472, 87, 529, 106], [579, 87, 642, 106], [4, 108, 68, 137], [114, 109, 179, 137], [224, 109, 289, 137], [332, 108, 399, 137], [446, 111, 641, 135], [33, 231, 89, 247], [142, 228, 201, 250], [252, 229, 309, 248], [361, 229, 419, 248], [472, 229, 528, 248], [582, 229, 639, 248], [3, 252, 45, 280], [113, 252, 155, 280], [223, 251, 265, 280], [333, 251, 375, 280], [443, 251, 485, 280], [554, 251, 618, 278]]}}