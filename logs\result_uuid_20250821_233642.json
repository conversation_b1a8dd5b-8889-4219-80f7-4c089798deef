{"res": {"input_path": "logs\\debug_uuid_screenshot_20250821_233642.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 87], [91, 87], [91, 106], [32, 106]], [[142, 87], [201, 87], [201, 106], [142, 106]], [[250, 86], [332, 83], [333, 106], [251, 108]], [[362, 87], [420, 87], [420, 106], [362, 106]], [[472, 87], [528, 87], [528, 106], [472, 106]], [[577, 86], [643, 86], [643, 108], [577, 108]], [[6, 109], [68, 113], [67, 136], [5, 132]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[225, 109], [289, 112], [288, 137], [224, 135]], [[336, 109], [398, 113], [396, 136], [334, 132]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[33, 231], [88, 231], [88, 247], [33, 247]], [[144, 231], [198, 231], [198, 247], [144, 247]], [[253, 231], [308, 231], [308, 247], [253, 247]], [[362, 230], [420, 230], [420, 249], [362, 249]], [[472, 229], [529, 229], [529, 248], [472, 248]], [[583, 231], [636, 231], [636, 247], [583, 247]], [[6, 251], [177, 255], [176, 277], [6, 273]], [[226, 251], [288, 255], [286, 278], [224, 274]], [[336, 251], [398, 255], [396, 278], [334, 274]], [[446, 251], [508, 255], [506, 278], [444, 274]], [[556, 251], [618, 255], [616, 278], [554, 274]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图4.jpg", "主图1.jpg", "主图2.jpg", "主图3.jpg", "a2041bdc-e5d2.. 800x1200.jpg", "800x800", "800x800", "800x800", "800x800", "800x800", "800x800", "170e67d5-10f4..主图1.jpg", "主图1.jpg", "主图1.jpg", "主图1.jpg", "主图1.jpg"], "rec_scores": [0.9603850245475769, 0.9603850245475769, 0.9130010604858398, 0.9824671149253845, 0.9617677330970764, 0.973242998123169, 0.9218339920043945, 0.967612624168396, 0.9690300226211548, 0.9226723313331604, 0.8837777972221375, 0.9860183000564575, 0.9599075317382812, 0.9729475378990173, 0.9761301279067993, 0.9791000485420227, 0.95974200963974, 0.9472882747650146, 0.9499945640563965, 0.9499945640563965, 0.9499945640563965, 0.9499945640563965], "rec_polys": [[[32, 87], [91, 87], [91, 106], [32, 106]], [[142, 87], [201, 87], [201, 106], [142, 106]], [[250, 86], [332, 83], [333, 106], [251, 108]], [[362, 87], [420, 87], [420, 106], [362, 106]], [[472, 87], [528, 87], [528, 106], [472, 106]], [[577, 86], [643, 86], [643, 108], [577, 108]], [[6, 109], [68, 113], [67, 136], [5, 132]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[225, 109], [289, 112], [288, 137], [224, 135]], [[336, 109], [398, 113], [396, 136], [334, 132]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[33, 231], [88, 231], [88, 247], [33, 247]], [[144, 231], [198, 231], [198, 247], [144, 247]], [[253, 231], [308, 231], [308, 247], [253, 247]], [[362, 230], [420, 230], [420, 249], [362, 249]], [[472, 229], [529, 229], [529, 248], [472, 248]], [[583, 231], [636, 231], [636, 247], [583, 247]], [[6, 251], [177, 255], [176, 277], [6, 273]], [[226, 251], [288, 255], [286, 278], [224, 274]], [[336, 251], [398, 255], [396, 278], [334, 274]], [[446, 251], [508, 255], [506, 278], [444, 274]], [[556, 251], [618, 255], [616, 278], [554, 274]]], "rec_boxes": [[32, 87, 91, 106], [142, 87, 201, 106], [250, 83, 333, 108], [362, 87, 420, 106], [472, 87, 528, 106], [577, 86, 643, 108], [5, 109, 68, 136], [114, 109, 179, 137], [224, 109, 289, 137], [334, 109, 398, 136], [446, 111, 641, 135], [33, 231, 88, 247], [144, 231, 198, 247], [253, 231, 308, 247], [362, 230, 420, 249], [472, 229, 529, 248], [583, 231, 636, 247], [6, 251, 177, 277], [224, 251, 288, 278], [334, 251, 398, 278], [444, 251, 508, 278], [554, 251, 618, 278]]}}