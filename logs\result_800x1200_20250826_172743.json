{"res": {"input_path": "logs\\debug_800x1200_screenshot_20250826_172743.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[34, 91], [91, 91], [91, 110], [34, 110]], [[143, 91], [202, 91], [202, 110], [143, 110]], [[254, 91], [312, 91], [312, 110], [254, 110]], [[364, 91], [422, 91], [422, 110], [364, 110]], [[475, 93], [530, 93], [530, 109], [475, 109]], [[580, 91], [645, 91], [645, 110], [580, 110]], [[8, 113], [70, 117], [69, 140], [7, 136]], [[117, 113], [181, 116], [180, 141], [116, 139]], [[227, 112], [291, 116], [289, 141], [225, 137]], [[338, 113], [400, 117], [398, 140], [336, 136]], [[448, 113], [643, 117], [642, 139], [448, 135]], [[403, 165], [441, 170], [439, 189], [401, 185]], [[34, 233], [92, 233], [92, 252], [34, 252]], [[144, 233], [201, 233], [201, 252], [144, 252]], [[254, 233], [311, 233], [311, 252], [254, 252]], [[364, 233], [421, 233], [421, 252], [364, 252]], [[468, 230], [537, 233], [536, 255], [468, 253]], [[584, 229], [642, 232], [641, 254], [583, 251]], [[8, 255], [48, 261], [44, 285], [4, 279]], [[117, 256], [157, 259], [155, 284], [115, 280]], [[227, 256], [267, 259], [265, 284], [225, 280]], [[337, 254], [377, 257], [375, 285], [335, 281]], [[447, 257], [533, 259], [532, 281], [447, 279]], [[560, 260], [663, 260], [663, 278], [560, 278]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图1.jpg", "主图3.jpg", "主图2.jpg", "主图4jpg", "d890c5f1-bbf9- 800x1200.jpg", "900x900", "900×900", "900x900", "900x900", "800x1200", "800x800", "6.jpg", "4.jpg", "5.jpg", "2.jpg", "800x1200.jpg", "70b0f979-4a8b.."], "rec_scores": [0.9710693359375, 0.9865064024925232, 0.9869486093521118, 0.9827485084533691, 0.9903472661972046, 0.9827241897583008, 0.9825358986854553, 0.941493809223175, 0.9849018454551697, 0.9879143238067627, 0.8531175255775452, 0.9364270567893982, 0.9432479739189148, 0.9557026028633118, 0.9648411870002747, 0.962230920791626, 0.9059227705001831, 0.9879907369613647, 0.9904114007949829, 0.9966920614242554, 0.9974217414855957, 0.9656848311424255, 0.9649993777275085], "rec_polys": [[[34, 91], [91, 91], [91, 110], [34, 110]], [[143, 91], [202, 91], [202, 110], [143, 110]], [[254, 91], [312, 91], [312, 110], [254, 110]], [[364, 91], [422, 91], [422, 110], [364, 110]], [[475, 93], [530, 93], [530, 109], [475, 109]], [[580, 91], [645, 91], [645, 110], [580, 110]], [[8, 113], [70, 117], [69, 140], [7, 136]], [[117, 113], [181, 116], [180, 141], [116, 139]], [[227, 112], [291, 116], [289, 141], [225, 137]], [[338, 113], [400, 117], [398, 140], [336, 136]], [[448, 113], [643, 117], [642, 139], [448, 135]], [[34, 233], [92, 233], [92, 252], [34, 252]], [[144, 233], [201, 233], [201, 252], [144, 252]], [[254, 233], [311, 233], [311, 252], [254, 252]], [[364, 233], [421, 233], [421, 252], [364, 252]], [[468, 230], [537, 233], [536, 255], [468, 253]], [[584, 229], [642, 232], [641, 254], [583, 251]], [[8, 255], [48, 261], [44, 285], [4, 279]], [[117, 256], [157, 259], [155, 284], [115, 280]], [[227, 256], [267, 259], [265, 284], [225, 280]], [[337, 254], [377, 257], [375, 285], [335, 281]], [[447, 257], [533, 259], [532, 281], [447, 279]], [[560, 260], [663, 260], [663, 278], [560, 278]]], "rec_boxes": [[34, 91, 91, 110], [143, 91, 202, 110], [254, 91, 312, 110], [364, 91, 422, 110], [475, 93, 530, 109], [580, 91, 645, 110], [7, 113, 70, 140], [116, 113, 181, 141], [225, 112, 291, 141], [336, 113, 400, 140], [448, 113, 643, 139], [34, 233, 92, 252], [144, 233, 201, 252], [254, 233, 311, 252], [364, 233, 421, 252], [468, 230, 537, 255], [583, 229, 642, 254], [4, 255, 48, 285], [115, 256, 157, 284], [225, 256, 267, 284], [335, 254, 377, 285], [447, 257, 533, 281], [560, 260, 663, 278]]}}