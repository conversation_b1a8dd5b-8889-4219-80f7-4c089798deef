{"res": {"input_path": "logs\\debug_uuid_screenshot_20250819_120910.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[142, 87], [200, 87], [200, 106], [142, 106]], [[251, 86], [311, 86], [311, 108], [251, 108]], [[361, 87], [420, 87], [420, 106], [361, 106]], [[471, 86], [530, 86], [530, 108], [471, 108]], [[578, 87], [643, 87], [643, 106], [578, 106]], [[6, 109], [67, 113], [66, 136], [5, 132]], [[115, 108], [179, 111], [178, 137], [114, 135]], [[225, 109], [289, 112], [288, 137], [224, 135]], [[335, 108], [399, 112], [397, 138], [333, 134]], [[445, 111], [641, 113], [640, 135], [445, 133]], [[31, 229], [89, 229], [89, 248], [31, 248]], [[139, 231], [201, 231], [201, 247], [139, 247]], [[250, 231], [312, 231], [312, 247], [250, 247]], [[359, 229], [423, 229], [423, 248], [359, 248]], [[472, 229], [528, 229], [528, 248], [472, 248]], [[565, 234], [584, 234], [584, 244], [565, 244]], [[580, 228], [641, 228], [641, 250], [580, 250]], [[5, 252], [45, 255], [43, 280], [3, 276]], [[115, 252], [155, 255], [153, 280], [113, 276]], [[224, 250], [266, 253], [264, 281], [222, 277]], [[335, 251], [375, 255], [373, 280], [333, 276]], [[445, 251], [485, 254], [483, 280], [443, 276]], [[554, 251], [619, 254], [618, 279], [553, 277]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图3.jpg", "主图2.jpg", "主图4.jpg", "主图1.jpg", "f4b68b3f-1ce1-….800x1200.jp", "900x998", "900x1037", "900x1053", "900x1084", "900x900", "800x800", "6.jpg", "5.jpg", "3.jpg", "4.jpg", "1.jpg", "主图3.jpg"], "rec_scores": [0.983687698841095, 0.981044590473175, 0.9077519774436951, 0.9505891799926758, 0.9696139693260193, 0.9871238470077515, 0.961009681224823, 0.9859214425086975, 0.9113383293151855, 0.8994060158729553, 0.9275628328323364, 0.9465345740318298, 0.9911474585533142, 0.9967685341835022, 0.9923319816589355, 0.9487821459770203, 0.986045241355896, 0.9963321685791016, 0.9966920614242554, 0.9820747375488281, 0.9895421862602234, 0.8986754417419434, 0.9651204943656921], "rec_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[142, 87], [200, 87], [200, 106], [142, 106]], [[251, 86], [311, 86], [311, 108], [251, 108]], [[361, 87], [420, 87], [420, 106], [361, 106]], [[471, 86], [530, 86], [530, 108], [471, 108]], [[578, 87], [643, 87], [643, 106], [578, 106]], [[6, 109], [67, 113], [66, 136], [5, 132]], [[115, 108], [179, 111], [178, 137], [114, 135]], [[225, 109], [289, 112], [288, 137], [224, 135]], [[335, 108], [399, 112], [397, 138], [333, 134]], [[445, 111], [641, 113], [640, 135], [445, 133]], [[31, 229], [89, 229], [89, 248], [31, 248]], [[139, 231], [201, 231], [201, 247], [139, 247]], [[250, 231], [312, 231], [312, 247], [250, 247]], [[359, 229], [423, 229], [423, 248], [359, 248]], [[472, 229], [528, 229], [528, 248], [472, 248]], [[580, 228], [641, 228], [641, 250], [580, 250]], [[5, 252], [45, 255], [43, 280], [3, 276]], [[115, 252], [155, 255], [153, 280], [113, 276]], [[224, 250], [266, 253], [264, 281], [222, 277]], [[335, 251], [375, 255], [373, 280], [333, 276]], [[445, 251], [485, 254], [483, 280], [443, 276]], [[554, 251], [619, 254], [618, 279], [553, 277]]], "rec_boxes": [[32, 87, 89, 106], [142, 87, 200, 106], [251, 86, 311, 108], [361, 87, 420, 106], [471, 86, 530, 108], [578, 87, 643, 106], [5, 109, 67, 136], [114, 108, 179, 137], [224, 109, 289, 137], [333, 108, 399, 138], [445, 111, 641, 135], [31, 229, 89, 248], [139, 231, 201, 247], [250, 231, 312, 247], [359, 229, 423, 248], [472, 229, 528, 248], [580, 228, 641, 250], [3, 252, 45, 280], [113, 252, 155, 280], [222, 250, 266, 281], [333, 251, 375, 280], [443, 251, 485, 280], [553, 251, 619, 279]]}}