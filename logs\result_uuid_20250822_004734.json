{"res": {"input_path": "logs\\debug_uuid_screenshot_20250822_004734.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[33, 89], [88, 89], [88, 105], [33, 105]], [[142, 88], [199, 88], [199, 107], [142, 107]], [[252, 87], [309, 87], [309, 106], [252, 106]], [[362, 88], [419, 88], [419, 107], [362, 107]], [[473, 89], [528, 89], [528, 105], [473, 105]], [[577, 85], [643, 88], [642, 108], [577, 105]], [[6, 110], [67, 113], [66, 135], [5, 133]], [[116, 109], [177, 113], [175, 136], [114, 132]], [[227, 109], [287, 113], [285, 136], [225, 132]], [[335, 110], [397, 113], [396, 135], [334, 133]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[29, 231], [92, 231], [92, 247], [29, 247]], [[139, 229], [204, 229], [204, 248], [139, 248]], [[248, 229], [313, 229], [313, 248], [248, 248]], [[360, 231], [422, 231], [422, 247], [360, 247]], [[470, 231], [531, 231], [531, 247], [470, 247]], [[584, 231], [637, 231], [637, 247], [584, 247]], [[6, 252], [45, 257], [43, 280], [3, 275]], [[116, 251], [155, 256], [152, 280], [113, 275]], [[226, 251], [265, 255], [263, 280], [223, 276]], [[335, 252], [375, 255], [373, 280], [333, 276]], [[445, 252], [485, 255], [483, 280], [443, 276]], [[556, 251], [618, 255], [616, 278], [554, 274]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图4jpg", "主图3.jpg", "主图1.jpg", "主图2.jpg", "d503d8e3-07f6... 800x1200.jpg", "900x1200", "900x1200", "900x1200", "900x1200", "900x1200", "800x800", "5.jpg", "3.jpg", "4.jpg", "2.jpg", "1.jpg", "主图2.jpg"], "rec_scores": [0.959281325340271, 0.9711270928382874, 0.9533917307853699, 0.97557532787323, 0.9702082872390747, 0.9567987322807312, 0.9909310936927795, 0.9173253178596497, 0.9688699841499329, 0.9900397658348083, 0.8727590441703796, 0.993485152721405, 0.9937256574630737, 0.9835423231124878, 0.9927923083305359, 0.9925422668457031, 0.9683014154434204, 0.9944928288459778, 0.9968681335449219, 0.9902734756469727, 0.9980134963989258, 0.9861119389533997, 0.9826864004135132], "rec_polys": [[[33, 89], [88, 89], [88, 105], [33, 105]], [[142, 88], [199, 88], [199, 107], [142, 107]], [[252, 87], [309, 87], [309, 106], [252, 106]], [[362, 88], [419, 88], [419, 107], [362, 107]], [[473, 89], [528, 89], [528, 105], [473, 105]], [[577, 85], [643, 88], [642, 108], [577, 105]], [[6, 110], [67, 113], [66, 135], [5, 133]], [[116, 109], [177, 113], [175, 136], [114, 132]], [[227, 109], [287, 113], [285, 136], [225, 132]], [[335, 110], [397, 113], [396, 135], [334, 133]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[29, 231], [92, 231], [92, 247], [29, 247]], [[139, 229], [204, 229], [204, 248], [139, 248]], [[248, 229], [313, 229], [313, 248], [248, 248]], [[360, 231], [422, 231], [422, 247], [360, 247]], [[470, 231], [531, 231], [531, 247], [470, 247]], [[584, 231], [637, 231], [637, 247], [584, 247]], [[6, 252], [45, 257], [43, 280], [3, 275]], [[116, 251], [155, 256], [152, 280], [113, 275]], [[226, 251], [265, 255], [263, 280], [223, 276]], [[335, 252], [375, 255], [373, 280], [333, 276]], [[445, 252], [485, 255], [483, 280], [443, 276]], [[556, 251], [618, 255], [616, 278], [554, 274]]], "rec_boxes": [[33, 89, 88, 105], [142, 88, 199, 107], [252, 87, 309, 106], [362, 88, 419, 107], [473, 89, 528, 105], [577, 85, 643, 108], [5, 110, 67, 135], [114, 109, 177, 136], [225, 109, 287, 136], [334, 110, 397, 135], [446, 111, 641, 135], [29, 231, 92, 247], [139, 229, 204, 248], [248, 229, 313, 248], [360, 231, 422, 247], [470, 231, 531, 247], [584, 231, 637, 247], [3, 252, 45, 280], [113, 251, 155, 280], [223, 251, 265, 280], [333, 252, 375, 280], [443, 252, 485, 280], [554, 251, 618, 278]]}}