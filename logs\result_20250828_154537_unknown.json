{"scene": "unknown", "timestamp": "20250828_154537", "raw_result": {"res": {"input_path": "logs\\debug_files_screenshot.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[145, 36], [169, 36], [169, 54], [145, 54]], [[51, 98], [98, 98], [98, 121], [51, 121]], [[158, 92], [206, 97], [203, 126], [155, 121]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [421, 124], [372, 119]], [[482, 96], [530, 101], [528, 124], [479, 119]], [[591, 96], [637, 101], [634, 124], [588, 119]], [[696, 96], [749, 101], [747, 124], [693, 119]], [[802, 92], [860, 99], [857, 128], [798, 120]], [[895, 98], [981, 98], [981, 121], [895, 121]], [[999, 98], [1095, 98], [1095, 120], [999, 120]], [[1117, 96], [1190, 100], [1188, 124], [1116, 119]], [[1227, 94], [1299, 101], [1296, 124], [1224, 117]], [[1333, 94], [1407, 99], [1405, 126], [1332, 121]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[998, 110], [1095, 115], [1094, 138], [996, 134]], [[996, 128], [1095, 132], [1094, 156], [995, 151]], [[1039, 154], [1057, 154], [1057, 171], [1039, 171]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["4.jpeg", "5.jpeg", "6,jpeg", "7.jpeg", "8.jpeg", "9.jpeg", "10.jpeg", "11.jpeg", "800x1200.jpg", "e7bbaed6-54d", "主图1.jpeg", "主图2.jpeg", "主图3.jpeg", "主图4jpeg", "e-4040-8765-d", "bedd2ff03bf.pn", "g"], "rec_scores": [0.9939669966697693, 0.9417853355407715, 0.93913334608078, 0.9854061007499695, 0.988820493221283, 0.9855602383613586, 0.994547963142395, 0.8855990171432495, 0.923505961894989, 0.9994044899940491, 0.9721140265464783, 0.9917568564414978, 0.9388467073440552, 0.9319921135902405, 0.9782481789588928, 0.9133968353271484, 0.9956187605857849], "rec_polys": [[[51, 98], [98, 98], [98, 121], [51, 121]], [[158, 92], [206, 97], [203, 126], [155, 121]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [421, 124], [372, 119]], [[482, 96], [530, 101], [528, 124], [479, 119]], [[591, 96], [637, 101], [634, 124], [588, 119]], [[696, 96], [749, 101], [747, 124], [693, 119]], [[802, 92], [860, 99], [857, 128], [798, 120]], [[895, 98], [981, 98], [981, 121], [895, 121]], [[999, 98], [1095, 98], [1095, 120], [999, 120]], [[1117, 96], [1190, 100], [1188, 124], [1116, 119]], [[1227, 94], [1299, 101], [1296, 124], [1224, 117]], [[1333, 94], [1407, 99], [1405, 126], [1332, 121]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[998, 110], [1095, 115], [1094, 138], [996, 134]], [[996, 128], [1095, 132], [1094, 156], [995, 151]], [[1039, 154], [1057, 154], [1057, 171], [1039, 171]]], "rec_boxes": [[51, 98, 98, 121], [155, 92, 206, 126], [266, 98, 315, 125], [372, 96, 423, 124], [479, 96, 530, 124], [588, 96, 637, 124], [693, 96, 749, 124], [798, 92, 860, 128], [895, 98, 981, 121], [999, 98, 1095, 120], [1116, 96, 1190, 124], [1224, 94, 1299, 124], [1332, 94, 1407, 126], [1440, 96, 1514, 124], [996, 110, 1095, 138], [995, 128, 1095, 156], [1039, 154, 1057, 171]]}}, "processed_result": {"texts": ["4.jpeg", "5.jpeg", "6,jpeg", "7.jpeg", "8.jpeg", "9.jpeg", "10.jpeg", "11.jpeg", "800x1200.jpg", "e7bbaed6-54d", "主图1.jpeg", "主图2.jpeg", "主图3.jpeg", "主图4jpeg", "e-4040-8765-d", "bedd2ff03bf.pn", "g"], "scores": [0.9939669966697693, 0.9417853355407715, 0.93913334608078, 0.9854061007499695, 0.988820493221283, 0.9855602383613586, 0.994547963142395, 0.8855990171432495, 0.923505961894989, 0.9994044899940491, 0.9721140265464783, 0.9917568564414978, 0.9388467073440552, 0.9319921135902405, 0.9782481789588928, 0.9133968353271484, 0.9956187605857849], "boxes": [[51, 98, 98, 121], [155, 92, 206, 126], [266, 98, 315, 125], [372, 96, 423, 124], [479, 96, 530, 124], [588, 96, 637, 124], [693, 96, 749, 124], [798, 92, 860, 128], [895, 98, 981, 121], [999, 98, 1095, 120], [1116, 96, 1190, 124], [1224, 94, 1299, 124], [1332, 94, 1407, 126], [1440, 96, 1514, 124], [996, 110, 1095, 138], [995, 128, 1095, 156], [1039, 154, 1057, 171]]}}