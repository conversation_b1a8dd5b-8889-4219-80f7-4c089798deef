{"res": {"input_path": "logs\\debug_uuid_screenshot_20250817_140151.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[143, 89], [199, 89], [199, 105], [143, 105]], [[252, 87], [310, 87], [310, 106], [252, 106]], [[362, 87], [419, 87], [419, 106], [362, 106]], [[471, 87], [528, 87], [528, 106], [471, 106]], [[577, 86], [644, 86], [644, 108], [577, 108]], [[6, 109], [68, 113], [67, 136], [5, 132]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[227, 109], [288, 112], [287, 137], [226, 135]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[32, 231], [89, 231], [89, 247], [32, 247]], [[142, 229], [199, 229], [199, 248], [142, 248]], [[252, 229], [309, 229], [309, 248], [252, 248]], [[363, 229], [420, 229], [420, 248], [363, 248]], [[473, 229], [529, 229], [529, 248], [473, 248]], [[582, 227], [640, 230], [639, 249], [582, 246]], [[7, 251], [51, 255], [49, 280], [4, 275]], [[115, 251], [155, 254], [153, 280], [113, 276]], [[225, 251], [265, 254], [263, 280], [223, 276]], [[335, 251], [375, 254], [373, 280], [333, 276]], [[445, 251], [485, 254], [483, 280], [443, 276]], [[555, 252], [595, 255], [593, 280], [553, 276]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图1.jpg", "主图4.jpg", "主图2.jpg", "主图3.jpg", "fde72376-e964.. 800x1200.jpg", "900x900", "900x900", "900x900", "900x900", "900x900", "900x900", "10.jpg", "9.jpg", "8.jpg", "7.jpg", "6.jpg", "5.jpg"], "rec_scores": [0.9676037430763245, 0.9809510111808777, 0.9736105799674988, 0.9578437209129333, 0.9447972178459167, 0.9723257422447205, 0.9825358986854553, 0.9113383293151855, 0.9719315767288208, 0.9554474949836731, 0.8820489645004272, 0.9888030886650085, 0.9658121466636658, 0.9583331346511841, 0.9604032635688782, 0.9353094696998596, 0.9482609629631042, 0.9402090907096863, 0.960169792175293, 0.9813787341117859, 0.9651347994804382, 0.934729278087616, 0.9966920614242554], "rec_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[143, 89], [199, 89], [199, 105], [143, 105]], [[252, 87], [310, 87], [310, 106], [252, 106]], [[362, 87], [419, 87], [419, 106], [362, 106]], [[471, 87], [528, 87], [528, 106], [471, 106]], [[577, 86], [644, 86], [644, 108], [577, 108]], [[6, 109], [68, 113], [67, 136], [5, 132]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[227, 109], [288, 112], [287, 137], [226, 135]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[32, 231], [89, 231], [89, 247], [32, 247]], [[142, 229], [199, 229], [199, 248], [142, 248]], [[252, 229], [309, 229], [309, 248], [252, 248]], [[363, 229], [420, 229], [420, 248], [363, 248]], [[473, 229], [529, 229], [529, 248], [473, 248]], [[582, 227], [640, 230], [639, 249], [582, 246]], [[7, 251], [51, 255], [49, 280], [4, 275]], [[115, 251], [155, 254], [153, 280], [113, 276]], [[225, 251], [265, 254], [263, 280], [223, 276]], [[335, 251], [375, 254], [373, 280], [333, 276]], [[445, 251], [485, 254], [483, 280], [443, 276]], [[555, 252], [595, 255], [593, 280], [553, 276]]], "rec_boxes": [[32, 87, 89, 106], [143, 89, 199, 105], [252, 87, 310, 106], [362, 87, 419, 106], [471, 87, 528, 106], [577, 86, 644, 108], [5, 109, 68, 136], [114, 109, 179, 137], [226, 109, 288, 137], [333, 109, 399, 137], [446, 111, 641, 135], [32, 231, 89, 247], [142, 229, 199, 248], [252, 229, 309, 248], [363, 229, 420, 248], [473, 229, 529, 248], [582, 227, 640, 249], [4, 251, 51, 280], [113, 251, 155, 280], [223, 251, 265, 280], [333, 251, 375, 280], [443, 251, 485, 280], [553, 252, 595, 280]]}}