{"res": {"input_path": "logs\\debug_uuid_screenshot_20250822_225956.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 87], [90, 87], [90, 106], [32, 106]], [[141, 86], [200, 86], [200, 108], [141, 108]], [[251, 86], [310, 86], [310, 108], [251, 108]], [[361, 86], [421, 86], [421, 108], [361, 108]], [[471, 86], [529, 86], [529, 108], [471, 108]], [[578, 87], [643, 87], [643, 106], [578, 106]], [[6, 109], [68, 113], [67, 136], [5, 132]], [[116, 108], [178, 112], [176, 137], [114, 133]], [[225, 109], [289, 112], [288, 137], [224, 135]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[445, 111], [641, 113], [640, 135], [445, 133]], [[32, 229], [90, 229], [90, 248], [32, 248]], [[143, 229], [200, 229], [200, 248], [143, 248]], [[253, 231], [308, 231], [308, 247], [253, 247]], [[362, 229], [420, 229], [420, 248], [362, 248]], [[472, 229], [529, 229], [529, 248], [472, 248]], [[581, 229], [637, 229], [637, 248], [581, 248]], [[7, 252], [52, 256], [50, 280], [4, 275]], [[116, 251], [162, 255], [160, 280], [113, 275]], [[225, 251], [272, 255], [270, 280], [223, 275]], [[335, 251], [382, 255], [380, 280], [333, 275]], [[444, 250], [493, 254], [490, 281], [442, 276]], [[555, 251], [602, 255], [600, 280], [553, 275]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图3.jpg", "主图4.jpg", "主图1.jpg", "主图2.jpg", "70432b61-7a22... 800x1200.jpg", "900x900", "900x900", "900x900", "900x900", "900x900", "900x900", "17.jpg", "16.jpg", "15.jpg", "13.jpg", "14.jpg", "12.jpg"], "rec_scores": [0.970988929271698, 0.9515689611434937, 0.9094887971878052, 0.9670440554618835, 0.9609928727149963, 0.9816428422927856, 0.9683977365493774, 0.9493737816810608, 0.967612624168396, 0.9905756115913391, 0.8939940929412842, 0.9288503527641296, 0.9631302952766418, 0.9814485907554626, 0.9320151209831238, 0.9627351760864258, 0.9613565802574158, 0.9290008544921875, 0.9499896168708801, 0.9862267374992371, 0.9947006702423096, 0.9834632873535156, 0.9971208572387695], "rec_polys": [[[32, 87], [90, 87], [90, 106], [32, 106]], [[141, 86], [200, 86], [200, 108], [141, 108]], [[251, 86], [310, 86], [310, 108], [251, 108]], [[361, 86], [421, 86], [421, 108], [361, 108]], [[471, 86], [529, 86], [529, 108], [471, 108]], [[578, 87], [643, 87], [643, 106], [578, 106]], [[6, 109], [68, 113], [67, 136], [5, 132]], [[116, 108], [178, 112], [176, 137], [114, 133]], [[225, 109], [289, 112], [288, 137], [224, 135]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[445, 111], [641, 113], [640, 135], [445, 133]], [[32, 229], [90, 229], [90, 248], [32, 248]], [[143, 229], [200, 229], [200, 248], [143, 248]], [[253, 231], [308, 231], [308, 247], [253, 247]], [[362, 229], [420, 229], [420, 248], [362, 248]], [[472, 229], [529, 229], [529, 248], [472, 248]], [[581, 229], [637, 229], [637, 248], [581, 248]], [[7, 252], [52, 256], [50, 280], [4, 275]], [[116, 251], [162, 255], [160, 280], [113, 275]], [[225, 251], [272, 255], [270, 280], [223, 275]], [[335, 251], [382, 255], [380, 280], [333, 275]], [[444, 250], [493, 254], [490, 281], [442, 276]], [[555, 251], [602, 255], [600, 280], [553, 275]]], "rec_boxes": [[32, 87, 90, 106], [141, 86, 200, 108], [251, 86, 310, 108], [361, 86, 421, 108], [471, 86, 529, 108], [578, 87, 643, 106], [5, 109, 68, 136], [114, 108, 178, 137], [224, 109, 289, 137], [333, 109, 399, 137], [445, 111, 641, 135], [32, 229, 90, 248], [143, 229, 200, 248], [253, 231, 308, 247], [362, 229, 420, 248], [472, 229, 529, 248], [581, 229, 637, 248], [4, 252, 52, 280], [113, 251, 162, 280], [223, 251, 272, 280], [333, 251, 382, 280], [442, 250, 493, 281], [553, 251, 602, 280]]}}