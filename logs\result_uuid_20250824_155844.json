{"res": {"input_path": "logs\\debug_uuid_screenshot_20250824_155844.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[142, 87], [199, 87], [199, 106], [142, 106]], [[252, 87], [309, 87], [309, 106], [252, 106]], [[362, 87], [420, 87], [420, 106], [362, 106]], [[467, 86], [529, 86], [529, 108], [467, 108]], [[577, 86], [644, 86], [644, 108], [577, 108]], [[6, 110], [68, 113], [67, 135], [5, 133]], [[116, 110], [178, 113], [177, 136], [115, 134]], [[227, 109], [288, 113], [286, 136], [225, 132]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[32, 229], [89, 229], [89, 248], [32, 248]], [[143, 229], [199, 229], [199, 248], [143, 248]], [[252, 229], [309, 229], [309, 248], [252, 248]], [[362, 229], [420, 229], [420, 248], [362, 248]], [[472, 229], [528, 229], [528, 248], [472, 248]], [[584, 231], [637, 231], [637, 247], [584, 247]], [[6, 251], [45, 255], [43, 280], [3, 276]], [[114, 252], [155, 255], [153, 280], [112, 276]], [[226, 251], [265, 255], [263, 280], [223, 276]], [[335, 252], [375, 255], [373, 280], [333, 276]], [[445, 251], [485, 255], [483, 280], [443, 276]], [[556, 251], [595, 256], [592, 280], [553, 275]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图2.jpg", "主图3.jpg", "主图4.jpg", "主图1.jpg", "e1c43e24-75a9.. 800x1200.jpg", "900x900", "900x900", "900x900", "900x900", "900x900", "900x900", "8.jpg", "7.jpg", "6.jpg", "5.jpg", "4.jpg", "3.jpg"], "rec_scores": [0.9840837717056274, 0.9684833884239197, 0.9765759110450745, 0.9719911217689514, 0.9334349632263184, 0.985203742980957, 0.9826083183288574, 0.9628936648368835, 0.9707441926002502, 0.9488903284072876, 0.8861383199691772, 0.9481554627418518, 0.9521161913871765, 0.9559391140937805, 0.9592026472091675, 0.9376469254493713, 0.9680140614509583, 0.9943461418151855, 0.9953945279121399, 0.9880026578903198, 0.9966920614242554, 0.9895421862602234, 0.9968681335449219], "rec_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[142, 87], [199, 87], [199, 106], [142, 106]], [[252, 87], [309, 87], [309, 106], [252, 106]], [[362, 87], [420, 87], [420, 106], [362, 106]], [[467, 86], [529, 86], [529, 108], [467, 108]], [[577, 86], [644, 86], [644, 108], [577, 108]], [[6, 110], [68, 113], [67, 135], [5, 133]], [[116, 110], [178, 113], [177, 136], [115, 134]], [[227, 109], [288, 113], [286, 136], [225, 132]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[32, 229], [89, 229], [89, 248], [32, 248]], [[143, 229], [199, 229], [199, 248], [143, 248]], [[252, 229], [309, 229], [309, 248], [252, 248]], [[362, 229], [420, 229], [420, 248], [362, 248]], [[472, 229], [528, 229], [528, 248], [472, 248]], [[584, 231], [637, 231], [637, 247], [584, 247]], [[6, 251], [45, 255], [43, 280], [3, 276]], [[114, 252], [155, 255], [153, 280], [112, 276]], [[226, 251], [265, 255], [263, 280], [223, 276]], [[335, 252], [375, 255], [373, 280], [333, 276]], [[445, 251], [485, 255], [483, 280], [443, 276]], [[556, 251], [595, 256], [592, 280], [553, 275]]], "rec_boxes": [[32, 87, 89, 106], [142, 87, 199, 106], [252, 87, 309, 106], [362, 87, 420, 106], [467, 86, 529, 108], [577, 86, 644, 108], [5, 110, 68, 135], [115, 110, 178, 136], [225, 109, 288, 136], [333, 109, 399, 137], [446, 111, 641, 135], [32, 229, 89, 248], [143, 229, 199, 248], [252, 229, 309, 248], [362, 229, 420, 248], [472, 229, 528, 248], [584, 231, 637, 247], [3, 251, 45, 280], [112, 252, 155, 280], [223, 251, 265, 280], [333, 252, 375, 280], [443, 251, 485, 280], [553, 251, 595, 280]]}}