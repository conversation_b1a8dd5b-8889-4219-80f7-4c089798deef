{"res": {"input_path": "logs\\debug_uuid_screenshot_20250819_001920.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[141, 86], [200, 86], [200, 108], [141, 108]], [[251, 86], [311, 86], [311, 108], [251, 108]], [[361, 86], [421, 86], [421, 108], [361, 108]], [[468, 86], [536, 86], [536, 108], [468, 108]], [[582, 87], [639, 87], [639, 106], [582, 106]], [[6, 110], [68, 113], [67, 136], [5, 134]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[227, 109], [288, 113], [286, 136], [225, 132]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 113], [531, 113], [531, 135], [446, 135]], [[557, 113], [659, 113], [659, 132], [557, 132]], [[148, 195], [209, 195], [209, 225], [148, 225]], [[248, 201], [293, 201], [293, 229], [248, 229]], [[31, 229], [88, 229], [88, 248], [31, 248]], [[137, 229], [202, 229], [202, 248], [137, 248]], [[252, 229], [309, 229], [309, 248], [252, 248]], [[363, 231], [418, 231], [418, 247], [363, 247]], [[472, 229], [528, 229], [528, 248], [472, 248]], [[583, 231], [637, 231], [637, 247], [583, 247]], [[9, 256], [219, 256], [219, 274], [9, 274]], [[226, 256], [331, 256], [331, 274], [226, 274]], [[333, 252], [398, 255], [397, 277], [332, 275]], [[448, 256], [553, 256], [553, 274], [448, 274]], [[552, 252], [617, 255], [616, 277], [551, 275]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x1200", "800x800", "主图1.jpg", "主图3.jpg", "主图4.jpg", "主图2.jpg", "800x1200.jpg", "2edfd61b-3f83...", "900x900", "900x1200", "623x623", "800x800", "800x800", "800x800", "_0.9966426350... 175549166938..", "175549154461...", "主图1.jpg", "8b674caf-1e27..", "主图1.jpg"], "rec_scores": [0.9753959774971008, 0.9341562986373901, 0.9313079118728638, 0.9313079118728638, 0.9692627191543579, 0.9813737869262695, 0.9779725670814514, 0.941493809223175, 0.9707441926002502, 0.9905756115913391, 0.9589890837669373, 0.9699023962020874, 0.9496144652366638, 0.9912262558937073, 0.982020378112793, 0.9764680862426758, 0.9764255285263062, 0.9714298844337463, 0.9221739172935486, 0.919725239276886, 0.9683399200439453, 0.9769182801246643, 0.9599266052246094], "rec_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[141, 86], [200, 86], [200, 108], [141, 108]], [[251, 86], [311, 86], [311, 108], [251, 108]], [[361, 86], [421, 86], [421, 108], [361, 108]], [[468, 86], [536, 86], [536, 108], [468, 108]], [[582, 87], [639, 87], [639, 106], [582, 106]], [[6, 110], [68, 113], [67, 136], [5, 134]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[227, 109], [288, 113], [286, 136], [225, 132]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 113], [531, 113], [531, 135], [446, 135]], [[557, 113], [659, 113], [659, 132], [557, 132]], [[31, 229], [88, 229], [88, 248], [31, 248]], [[137, 229], [202, 229], [202, 248], [137, 248]], [[252, 229], [309, 229], [309, 248], [252, 248]], [[363, 231], [418, 231], [418, 247], [363, 247]], [[472, 229], [528, 229], [528, 248], [472, 248]], [[583, 231], [637, 231], [637, 247], [583, 247]], [[9, 256], [219, 256], [219, 274], [9, 274]], [[226, 256], [331, 256], [331, 274], [226, 274]], [[333, 252], [398, 255], [397, 277], [332, 275]], [[448, 256], [553, 256], [553, 274], [448, 274]], [[552, 252], [617, 255], [616, 277], [551, 275]]], "rec_boxes": [[32, 87, 89, 106], [141, 86, 200, 108], [251, 86, 311, 108], [361, 86, 421, 108], [468, 86, 536, 108], [582, 87, 639, 106], [5, 110, 68, 136], [114, 109, 179, 137], [225, 109, 288, 136], [333, 109, 399, 137], [446, 113, 531, 135], [557, 113, 659, 132], [31, 229, 88, 248], [137, 229, 202, 248], [252, 229, 309, 248], [363, 231, 418, 247], [472, 229, 528, 248], [583, 231, 637, 247], [9, 256, 219, 274], [226, 256, 331, 274], [332, 252, 398, 277], [448, 256, 553, 274], [551, 252, 617, 277]]}}