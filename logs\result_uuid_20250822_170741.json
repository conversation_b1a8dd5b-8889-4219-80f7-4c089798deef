{"res": {"input_path": "logs\\debug_uuid_screenshot_20250822_170741.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 87], [90, 87], [90, 106], [32, 106]], [[143, 89], [198, 89], [198, 105], [143, 105]], [[253, 89], [308, 89], [308, 105], [253, 105]], [[363, 89], [418, 89], [418, 105], [363, 105]], [[472, 87], [529, 87], [529, 106], [472, 106]], [[578, 87], [642, 87], [642, 106], [578, 106]], [[6, 109], [67, 113], [66, 136], [5, 132]], [[116, 110], [177, 113], [176, 136], [115, 134]], [[227, 109], [287, 113], [285, 136], [225, 132]], [[336, 109], [397, 113], [395, 136], [334, 132]], [[446, 112], [641, 114], [640, 135], [446, 133]], [[33, 231], [88, 231], [88, 247], [33, 247]], [[143, 231], [198, 231], [198, 247], [143, 247]], [[253, 231], [308, 231], [308, 247], [253, 247]], [[361, 229], [418, 229], [418, 248], [361, 248]], [[474, 231], [527, 231], [527, 247], [474, 247]], [[583, 231], [637, 231], [637, 247], [583, 247]], [[5, 253], [45, 256], [43, 280], [3, 276]], [[116, 251], [155, 255], [153, 280], [113, 276]], [[225, 252], [265, 255], [263, 280], [223, 276]], [[335, 252], [375, 255], [373, 280], [333, 276]], [[446, 252], [508, 256], [506, 278], [444, 274]], [[556, 251], [618, 255], [616, 278], [554, 274]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图1.jpg", "主图3.jpg", "主图4.jpg", "主图2.jpg", "ee05e106-45e9.. 800x1200.jpg", "900x898", "900x898", "900x898", "900x898", "800x800", "800x800", "5.jpg", "3.jpg", "4.jpg", "2.jpg", "主图4.jpg", "主图1.jpg"], "rec_scores": [0.970129668712616, 0.9663552641868591, 0.9786156415939331, 0.9752274751663208, 0.9756445288658142, 0.9806694984436035, 0.9797891974449158, 0.9639344811439514, 0.9673671722412109, 0.9792566895484924, 0.9507229924201965, 0.9753166437149048, 0.9849362969398499, 0.9890770316123962, 0.9799754023551941, 0.9803920984268188, 0.9726868867874146, 0.9978955984115601, 0.9921211004257202, 0.9904114007949829, 0.9980134963989258, 0.9163684248924255, 0.9499945640563965], "rec_polys": [[[32, 87], [90, 87], [90, 106], [32, 106]], [[143, 89], [198, 89], [198, 105], [143, 105]], [[253, 89], [308, 89], [308, 105], [253, 105]], [[363, 89], [418, 89], [418, 105], [363, 105]], [[472, 87], [529, 87], [529, 106], [472, 106]], [[578, 87], [642, 87], [642, 106], [578, 106]], [[6, 109], [67, 113], [66, 136], [5, 132]], [[116, 110], [177, 113], [176, 136], [115, 134]], [[227, 109], [287, 113], [285, 136], [225, 132]], [[336, 109], [397, 113], [395, 136], [334, 132]], [[446, 112], [641, 114], [640, 135], [446, 133]], [[33, 231], [88, 231], [88, 247], [33, 247]], [[143, 231], [198, 231], [198, 247], [143, 247]], [[253, 231], [308, 231], [308, 247], [253, 247]], [[361, 229], [418, 229], [418, 248], [361, 248]], [[474, 231], [527, 231], [527, 247], [474, 247]], [[583, 231], [637, 231], [637, 247], [583, 247]], [[5, 253], [45, 256], [43, 280], [3, 276]], [[116, 251], [155, 255], [153, 280], [113, 276]], [[225, 252], [265, 255], [263, 280], [223, 276]], [[335, 252], [375, 255], [373, 280], [333, 276]], [[446, 252], [508, 256], [506, 278], [444, 274]], [[556, 251], [618, 255], [616, 278], [554, 274]]], "rec_boxes": [[32, 87, 90, 106], [143, 89, 198, 105], [253, 89, 308, 105], [363, 89, 418, 105], [472, 87, 529, 106], [578, 87, 642, 106], [5, 109, 67, 136], [115, 110, 177, 136], [225, 109, 287, 136], [334, 109, 397, 136], [446, 112, 641, 135], [33, 231, 88, 247], [143, 231, 198, 247], [253, 231, 308, 247], [361, 229, 418, 248], [474, 231, 527, 247], [583, 231, 637, 247], [3, 253, 45, 280], [113, 251, 155, 280], [223, 252, 265, 280], [333, 252, 375, 280], [444, 252, 508, 278], [554, 251, 618, 278]]}}