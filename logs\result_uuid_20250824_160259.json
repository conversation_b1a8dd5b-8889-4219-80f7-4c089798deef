{"res": {"input_path": "logs\\debug_uuid_screenshot_20250824_160259.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[33, 89], [88, 89], [88, 105], [33, 105]], [[142, 87], [200, 87], [200, 106], [142, 106]], [[251, 86], [310, 86], [310, 108], [251, 108]], [[363, 87], [419, 87], [419, 106], [363, 106]], [[472, 87], [528, 87], [528, 106], [472, 106]], [[578, 87], [642, 87], [642, 106], [578, 106]], [[6, 109], [67, 113], [66, 136], [5, 132]], [[116, 109], [177, 113], [175, 136], [114, 132]], [[226, 109], [288, 113], [286, 136], [224, 132]], [[335, 110], [398, 113], [397, 136], [334, 134]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[29, 231], [92, 231], [92, 247], [29, 247]], [[138, 229], [203, 229], [203, 248], [138, 248]], [[248, 229], [314, 229], [314, 248], [248, 248]], [[359, 229], [423, 229], [423, 248], [359, 248]], [[468, 229], [532, 229], [532, 248], [468, 248]], [[579, 229], [642, 229], [642, 248], [579, 248]], [[6, 251], [52, 257], [50, 280], [3, 274]], [[117, 251], [162, 255], [160, 280], [114, 275]], [[226, 251], [265, 255], [263, 280], [223, 276]], [[336, 251], [375, 256], [372, 280], [333, 275]], [[445, 251], [485, 255], [483, 280], [443, 276]], [[556, 252], [595, 255], [592, 280], [554, 276]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图2.jpg", "主图4jpg", "主图3.jpg", "主图1.jpg", "d91e9a94-987 800x1200.jpg", "900x1199", "900x1199", "900x1199", "900x1199", "900x1199", "900x1199", "11.jpg", "10.jpg", "9.jpg", "8.jpg", "7.jpg", "6.jpg"], "rec_scores": [0.9771651029586792, 0.9672390818595886, 0.9713748097419739, 0.9644237160682678, 0.9775742888450623, 0.9797043204307556, 0.9835571646690369, 0.9884434342384338, 0.9226723313331604, 0.9557353258132935, 0.9199608564376831, 0.9934757947921753, 0.9744806289672852, 0.9856676459312439, 0.9774255752563477, 0.9802696704864502, 0.9927830100059509, 0.9692776799201965, 0.9178192615509033, 0.9907647371292114, 0.9975160360336304, 0.9730637669563293, 0.9969870448112488], "rec_polys": [[[33, 89], [88, 89], [88, 105], [33, 105]], [[142, 87], [200, 87], [200, 106], [142, 106]], [[251, 86], [310, 86], [310, 108], [251, 108]], [[363, 87], [419, 87], [419, 106], [363, 106]], [[472, 87], [528, 87], [528, 106], [472, 106]], [[578, 87], [642, 87], [642, 106], [578, 106]], [[6, 109], [67, 113], [66, 136], [5, 132]], [[116, 109], [177, 113], [175, 136], [114, 132]], [[226, 109], [288, 113], [286, 136], [224, 132]], [[335, 110], [398, 113], [397, 136], [334, 134]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[29, 231], [92, 231], [92, 247], [29, 247]], [[138, 229], [203, 229], [203, 248], [138, 248]], [[248, 229], [314, 229], [314, 248], [248, 248]], [[359, 229], [423, 229], [423, 248], [359, 248]], [[468, 229], [532, 229], [532, 248], [468, 248]], [[579, 229], [642, 229], [642, 248], [579, 248]], [[6, 251], [52, 257], [50, 280], [3, 274]], [[117, 251], [162, 255], [160, 280], [114, 275]], [[226, 251], [265, 255], [263, 280], [223, 276]], [[336, 251], [375, 256], [372, 280], [333, 275]], [[445, 251], [485, 255], [483, 280], [443, 276]], [[556, 252], [595, 255], [592, 280], [554, 276]]], "rec_boxes": [[33, 89, 88, 105], [142, 87, 200, 106], [251, 86, 310, 108], [363, 87, 419, 106], [472, 87, 528, 106], [578, 87, 642, 106], [5, 109, 67, 136], [114, 109, 177, 136], [224, 109, 288, 136], [334, 110, 398, 136], [446, 111, 641, 135], [29, 231, 92, 247], [138, 229, 203, 248], [248, 229, 314, 248], [359, 229, 423, 248], [468, 229, 532, 248], [579, 229, 642, 248], [3, 251, 52, 280], [114, 251, 162, 280], [223, 251, 265, 280], [333, 251, 375, 280], [443, 251, 485, 280], [554, 252, 595, 280]]}}