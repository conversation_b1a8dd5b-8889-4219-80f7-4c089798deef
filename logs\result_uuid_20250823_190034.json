{"res": {"input_path": "logs\\debug_uuid_screenshot_20250823_190034.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[31, 89], [89, 89], [89, 108], [31, 108]], [[143, 89], [200, 89], [200, 108], [143, 108]], [[251, 88], [311, 88], [311, 110], [251, 110]], [[362, 89], [420, 89], [420, 108], [362, 108]], [[472, 89], [528, 89], [528, 108], [472, 108]], [[578, 89], [643, 89], [643, 108], [578, 108]], [[6, 112], [68, 116], [67, 138], [5, 134]], [[116, 111], [178, 115], [177, 138], [114, 134]], [[226, 111], [288, 115], [286, 138], [224, 134]], [[336, 111], [398, 115], [396, 138], [334, 134]], [[446, 113], [641, 115], [640, 137], [446, 135]], [[33, 233], [89, 233], [89, 249], [33, 249]], [[141, 230], [200, 230], [200, 252], [141, 252]], [[252, 232], [309, 232], [309, 251], [252, 251]], [[362, 231], [420, 231], [420, 250], [362, 250]], [[473, 228], [529, 231], [528, 251], [472, 248]], [[580, 230], [639, 230], [639, 252], [580, 252]], [[6, 254], [68, 258], [67, 280], [5, 276]], [[116, 253], [178, 257], [177, 280], [114, 276]], [[226, 253], [288, 257], [286, 280], [224, 276]], [[336, 253], [398, 257], [396, 280], [334, 276]], [[446, 253], [508, 257], [506, 280], [444, 276]], [[556, 253], [618, 257], [616, 280], [554, 276]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图3.jpg", "主图2.jpg", "主图4jpg", "主图1.jpg", "1d8b34f3-e7c9.. 800x1200.jpg", "800x800", "800x800", "800x800", "800x800", "800x800", "800x800", "主图1.jpg", "主图1.jpg", "主图1.jpg", "主图1.jpg", "主图1.jpg", "主图1.jpg"], "rec_scores": [0.9688578844070435, 0.9579882025718689, 0.9355903267860413, 0.9598017930984497, 0.968424379825592, 0.9795494079589844, 0.9249511361122131, 0.9905036091804504, 0.9879143238067627, 0.9499945640563965, 0.8839188814163208, 0.977794349193573, 0.9315481185913086, 0.9769101738929749, 0.9748693108558655, 0.9718490839004517, 0.9349204301834106, 0.9744553565979004, 0.9258028864860535, 0.9499945640563965, 0.9499945640563965, 0.9499945640563965, 0.9499945640563965], "rec_polys": [[[31, 89], [89, 89], [89, 108], [31, 108]], [[143, 89], [200, 89], [200, 108], [143, 108]], [[251, 88], [311, 88], [311, 110], [251, 110]], [[362, 89], [420, 89], [420, 108], [362, 108]], [[472, 89], [528, 89], [528, 108], [472, 108]], [[578, 89], [643, 89], [643, 108], [578, 108]], [[6, 112], [68, 116], [67, 138], [5, 134]], [[116, 111], [178, 115], [177, 138], [114, 134]], [[226, 111], [288, 115], [286, 138], [224, 134]], [[336, 111], [398, 115], [396, 138], [334, 134]], [[446, 113], [641, 115], [640, 137], [446, 135]], [[33, 233], [89, 233], [89, 249], [33, 249]], [[141, 230], [200, 230], [200, 252], [141, 252]], [[252, 232], [309, 232], [309, 251], [252, 251]], [[362, 231], [420, 231], [420, 250], [362, 250]], [[473, 228], [529, 231], [528, 251], [472, 248]], [[580, 230], [639, 230], [639, 252], [580, 252]], [[6, 254], [68, 258], [67, 280], [5, 276]], [[116, 253], [178, 257], [177, 280], [114, 276]], [[226, 253], [288, 257], [286, 280], [224, 276]], [[336, 253], [398, 257], [396, 280], [334, 276]], [[446, 253], [508, 257], [506, 280], [444, 276]], [[556, 253], [618, 257], [616, 280], [554, 276]]], "rec_boxes": [[31, 89, 89, 108], [143, 89, 200, 108], [251, 88, 311, 110], [362, 89, 420, 108], [472, 89, 528, 108], [578, 89, 643, 108], [5, 112, 68, 138], [114, 111, 178, 138], [224, 111, 288, 138], [334, 111, 398, 138], [446, 113, 641, 137], [33, 233, 89, 249], [141, 230, 200, 252], [252, 232, 309, 251], [362, 231, 420, 250], [472, 228, 529, 251], [580, 230, 639, 252], [5, 254, 68, 280], [114, 253, 178, 280], [224, 253, 288, 280], [334, 253, 398, 280], [444, 253, 508, 280], [554, 253, 618, 280]]}}