{"res": {"input_path": "logs\\debug_uuid_screenshot_20250824_175914.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[33, 89], [88, 89], [88, 105], [33, 105]], [[143, 89], [198, 89], [198, 105], [143, 105]], [[252, 87], [309, 87], [309, 106], [252, 106]], [[363, 89], [418, 89], [418, 105], [363, 105]], [[472, 87], [529, 87], [529, 106], [472, 106]], [[578, 87], [642, 87], [642, 106], [578, 106]], [[6, 110], [67, 113], [66, 136], [5, 134]], [[116, 110], [178, 113], [177, 136], [115, 134]], [[226, 109], [288, 113], [286, 136], [224, 132]], [[335, 110], [398, 113], [397, 136], [334, 134]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[33, 231], [88, 231], [88, 247], [33, 247]], [[143, 231], [198, 231], [198, 247], [143, 247]], [[252, 229], [309, 229], [309, 248], [252, 248]], [[363, 231], [418, 231], [418, 247], [363, 247]], [[468, 229], [532, 229], [532, 248], [468, 248]], [[583, 231], [638, 231], [638, 247], [583, 247]], [[5, 252], [68, 256], [67, 278], [4, 274]], [[116, 251], [178, 255], [177, 278], [114, 274]], [[226, 251], [288, 255], [286, 278], [224, 274]], [[336, 251], [398, 256], [396, 279], [334, 274]], [[446, 251], [532, 255], [531, 277], [445, 274]], [[558, 256], [657, 256], [657, 274], [558, 274]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图4.jpg", "主图3.jpg", "主图1.jpg", "主图2.jpg", "93342077-5987 800x1200.jpg", "800x800", "800x800", "800x800", "800x800", "800x1200", "800x800", "主图4.jpg", "主图1.jpg", "主图2.jpg", "主图3.jpg", "800x1200.jpg", "d0cbe29b-875..."], "rec_scores": [0.9745849967002869, 0.970716655254364, 0.9689673781394958, 0.9652312397956848, 0.9829356074333191, 0.971150815486908, 0.9387584328651428, 0.9628936648368835, 0.9499945640563965, 0.9870602488517761, 0.8987569212913513, 0.9746800661087036, 0.9598788619041443, 0.9818666577339172, 0.9702309370040894, 0.9832982420921326, 0.9655874967575073, 0.9535796046257019, 0.9258028864860535, 0.9826864004135132, 0.9633215665817261, 0.966620683670044, 0.9439302086830139], "rec_polys": [[[33, 89], [88, 89], [88, 105], [33, 105]], [[143, 89], [198, 89], [198, 105], [143, 105]], [[252, 87], [309, 87], [309, 106], [252, 106]], [[363, 89], [418, 89], [418, 105], [363, 105]], [[472, 87], [529, 87], [529, 106], [472, 106]], [[578, 87], [642, 87], [642, 106], [578, 106]], [[6, 110], [67, 113], [66, 136], [5, 134]], [[116, 110], [178, 113], [177, 136], [115, 134]], [[226, 109], [288, 113], [286, 136], [224, 132]], [[335, 110], [398, 113], [397, 136], [334, 134]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[33, 231], [88, 231], [88, 247], [33, 247]], [[143, 231], [198, 231], [198, 247], [143, 247]], [[252, 229], [309, 229], [309, 248], [252, 248]], [[363, 231], [418, 231], [418, 247], [363, 247]], [[468, 229], [532, 229], [532, 248], [468, 248]], [[583, 231], [638, 231], [638, 247], [583, 247]], [[5, 252], [68, 256], [67, 278], [4, 274]], [[116, 251], [178, 255], [177, 278], [114, 274]], [[226, 251], [288, 255], [286, 278], [224, 274]], [[336, 251], [398, 256], [396, 279], [334, 274]], [[446, 251], [532, 255], [531, 277], [445, 274]], [[558, 256], [657, 256], [657, 274], [558, 274]]], "rec_boxes": [[33, 89, 88, 105], [143, 89, 198, 105], [252, 87, 309, 106], [363, 89, 418, 105], [472, 87, 529, 106], [578, 87, 642, 106], [5, 110, 67, 136], [115, 110, 178, 136], [224, 109, 288, 136], [334, 110, 398, 136], [446, 111, 641, 135], [33, 231, 88, 247], [143, 231, 198, 247], [252, 229, 309, 248], [363, 231, 418, 247], [468, 229, 532, 248], [583, 231, 638, 247], [4, 252, 68, 278], [114, 251, 178, 278], [224, 251, 288, 278], [334, 251, 398, 279], [445, 251, 532, 277], [558, 256, 657, 274]]}}