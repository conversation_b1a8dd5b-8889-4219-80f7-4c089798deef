{"res": {"input_path": "logs\\debug_uuid_screenshot_20250821_005140.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[140, 58], [166, 55], [168, 68], [141, 71]], [[32, 89], [89, 89], [89, 108], [32, 108]], [[143, 91], [198, 91], [198, 107], [143, 107]], [[251, 88], [311, 88], [311, 110], [251, 110]], [[363, 89], [420, 89], [420, 108], [363, 108]], [[472, 89], [528, 89], [528, 108], [472, 108]], [[579, 89], [642, 89], [642, 108], [579, 108]], [[6, 112], [68, 116], [67, 138], [5, 134]], [[116, 112], [178, 116], [177, 138], [114, 134]], [[226, 111], [288, 115], [286, 138], [224, 134]], [[336, 112], [398, 116], [396, 138], [334, 134]], [[446, 114], [641, 116], [640, 137], [446, 135]], [[31, 230], [90, 230], [90, 252], [31, 252]], [[142, 231], [199, 231], [199, 250], [142, 250]], [[251, 230], [310, 230], [310, 252], [251, 252]], [[361, 230], [420, 230], [420, 252], [361, 252]], [[472, 231], [528, 231], [528, 250], [472, 250]], [[583, 231], [638, 231], [638, 250], [583, 250]], [[6, 254], [45, 259], [43, 282], [3, 277]], [[115, 254], [155, 257], [153, 282], [113, 278]], [[225, 254], [265, 257], [263, 282], [223, 278]], [[335, 254], [375, 257], [373, 282], [333, 278]], [[445, 254], [485, 257], [483, 282], [443, 278]], [[555, 254], [595, 258], [592, 282], [553, 278]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图4.jpg", "主图1.jpg", "主图2.jpg", "主图3.jpg", "836ad589-1357.. 800x1200.jpg", "900x900", "900x900", "900x900", "900x900", "900x900", "900×900", "5.jpg", "3.jpg", "4.jpg", "2.jpg", "1.jpg", "5.jpg"], "rec_scores": [0.9748544692993164, 0.9795328378677368, 0.9239683151245117, 0.9597328305244446, 0.9723681211471558, 0.9929642677307129, 0.9253472089767456, 0.9618949294090271, 0.9826864004135132, 0.935663104057312, 0.9278769493103027, 0.9420968294143677, 0.9679375290870667, 0.8525118231773376, 0.9336710572242737, 0.9447391629219055, 0.9435048699378967, 0.9944928288459778, 0.9970253705978394, 0.9904114007949829, 0.9980134963989258, 0.9861119389533997, 0.9893779754638672], "rec_polys": [[[32, 89], [89, 89], [89, 108], [32, 108]], [[143, 91], [198, 91], [198, 107], [143, 107]], [[251, 88], [311, 88], [311, 110], [251, 110]], [[363, 89], [420, 89], [420, 108], [363, 108]], [[472, 89], [528, 89], [528, 108], [472, 108]], [[579, 89], [642, 89], [642, 108], [579, 108]], [[6, 112], [68, 116], [67, 138], [5, 134]], [[116, 112], [178, 116], [177, 138], [114, 134]], [[226, 111], [288, 115], [286, 138], [224, 134]], [[336, 112], [398, 116], [396, 138], [334, 134]], [[446, 114], [641, 116], [640, 137], [446, 135]], [[31, 230], [90, 230], [90, 252], [31, 252]], [[142, 231], [199, 231], [199, 250], [142, 250]], [[251, 230], [310, 230], [310, 252], [251, 252]], [[361, 230], [420, 230], [420, 252], [361, 252]], [[472, 231], [528, 231], [528, 250], [472, 250]], [[583, 231], [638, 231], [638, 250], [583, 250]], [[6, 254], [45, 259], [43, 282], [3, 277]], [[115, 254], [155, 257], [153, 282], [113, 278]], [[225, 254], [265, 257], [263, 282], [223, 278]], [[335, 254], [375, 257], [373, 282], [333, 278]], [[445, 254], [485, 257], [483, 282], [443, 278]], [[555, 254], [595, 258], [592, 282], [553, 278]]], "rec_boxes": [[32, 89, 89, 108], [143, 91, 198, 107], [251, 88, 311, 110], [363, 89, 420, 108], [472, 89, 528, 108], [579, 89, 642, 108], [5, 112, 68, 138], [114, 112, 178, 138], [224, 111, 288, 138], [334, 112, 398, 138], [446, 114, 641, 137], [31, 230, 90, 252], [142, 231, 199, 250], [251, 230, 310, 252], [361, 230, 420, 252], [472, 231, 528, 250], [583, 231, 638, 250], [3, 254, 45, 282], [113, 254, 155, 282], [223, 254, 265, 282], [333, 254, 375, 282], [443, 254, 485, 282], [553, 254, 595, 282]]}}