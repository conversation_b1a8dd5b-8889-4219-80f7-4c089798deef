{"res": {"input_path": "logs\\debug_uuid_screenshot_20250819_135648.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[33, 89], [89, 89], [89, 105], [33, 105]], [[142, 87], [199, 87], [199, 106], [142, 106]], [[252, 87], [310, 87], [310, 106], [252, 106]], [[360, 86], [419, 83], [420, 105], [361, 108]], [[472, 85], [530, 88], [529, 108], [471, 105]], [[577, 86], [644, 86], [644, 108], [577, 108]], [[6, 110], [67, 113], [66, 136], [5, 134]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[225, 108], [289, 112], [287, 138], [223, 134]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[445, 111], [641, 113], [640, 135], [445, 133]], [[44, 191], [98, 191], [98, 223], [44, 223]], [[28, 229], [93, 229], [93, 248], [28, 248]], [[137, 229], [204, 229], [204, 248], [137, 248]], [[248, 229], [313, 229], [313, 248], [248, 248]], [[361, 229], [420, 229], [420, 248], [361, 248]], [[473, 229], [528, 229], [528, 248], [473, 248]], [[582, 229], [638, 229], [638, 248], [582, 248]], [[5, 252], [45, 255], [43, 280], [3, 276]], [[116, 251], [155, 255], [153, 280], [113, 276]], [[224, 250], [266, 253], [264, 281], [222, 277]], [[334, 250], [376, 253], [374, 281], [332, 277]], [[445, 251], [485, 254], [483, 280], [443, 276]], [[555, 251], [595, 254], [593, 280], [553, 276]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800×800", "800x1200", "主图4.jpg", "主图1.jpg", "主图3.jpg", "主图2.jpg", "13031d9e-bcb…. 800x1200.jp", "900x1197", "900x1197", "900x1197", "900x900", "900x900", "900x900", "7.jpg", "6.jpg", "5.jpg", "4.jpg", "3.jpg", "2.jpg"], "rec_scores": [0.9776169657707214, 0.9510065913200378, 0.9783006310462952, 0.9350212216377258, 0.9288668036460876, 0.9616391062736511, 0.9387584328651428, 0.967612624168396, 0.8930421471595764, 0.9905756115913391, 0.8830429911613464, 0.9845099449157715, 0.9937034845352173, 0.9912010431289673, 0.9784877896308899, 0.9359159469604492, 0.9321359992027283, 0.990766167640686, 0.9880026578903198, 0.9928535223007202, 0.9863120317459106, 0.9298403859138489, 0.9763649702072144], "rec_polys": [[[33, 89], [89, 89], [89, 105], [33, 105]], [[142, 87], [199, 87], [199, 106], [142, 106]], [[252, 87], [310, 87], [310, 106], [252, 106]], [[360, 86], [419, 83], [420, 105], [361, 108]], [[472, 85], [530, 88], [529, 108], [471, 105]], [[577, 86], [644, 86], [644, 108], [577, 108]], [[6, 110], [67, 113], [66, 136], [5, 134]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[225, 108], [289, 112], [287, 138], [223, 134]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[445, 111], [641, 113], [640, 135], [445, 133]], [[28, 229], [93, 229], [93, 248], [28, 248]], [[137, 229], [204, 229], [204, 248], [137, 248]], [[248, 229], [313, 229], [313, 248], [248, 248]], [[361, 229], [420, 229], [420, 248], [361, 248]], [[473, 229], [528, 229], [528, 248], [473, 248]], [[582, 229], [638, 229], [638, 248], [582, 248]], [[5, 252], [45, 255], [43, 280], [3, 276]], [[116, 251], [155, 255], [153, 280], [113, 276]], [[224, 250], [266, 253], [264, 281], [222, 277]], [[334, 250], [376, 253], [374, 281], [332, 277]], [[445, 251], [485, 254], [483, 280], [443, 276]], [[555, 251], [595, 254], [593, 280], [553, 276]]], "rec_boxes": [[33, 89, 89, 105], [142, 87, 199, 106], [252, 87, 310, 106], [360, 83, 420, 108], [471, 85, 530, 108], [577, 86, 644, 108], [5, 110, 67, 136], [114, 109, 179, 137], [223, 108, 289, 138], [333, 109, 399, 137], [445, 111, 641, 135], [28, 229, 93, 248], [137, 229, 204, 248], [248, 229, 313, 248], [361, 229, 420, 248], [473, 229, 528, 248], [582, 229, 638, 248], [3, 252, 45, 280], [113, 251, 155, 280], [222, 250, 266, 281], [332, 250, 376, 281], [443, 251, 485, 280], [553, 251, 595, 280]]}}