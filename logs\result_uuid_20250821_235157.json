{"res": {"input_path": "logs\\debug_uuid_screenshot_20250821_235157.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[141, 86], [200, 86], [200, 108], [141, 108]], [[251, 86], [311, 86], [311, 108], [251, 108]], [[361, 86], [420, 86], [420, 108], [361, 108]], [[471, 86], [530, 86], [530, 108], [471, 108]], [[578, 87], [643, 87], [643, 106], [578, 106]], [[6, 109], [68, 113], [67, 136], [5, 132]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[225, 108], [289, 112], [287, 138], [223, 134]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[31, 225], [89, 228], [88, 248], [30, 245]], [[142, 229], [199, 229], [199, 248], [142, 248]], [[251, 229], [309, 229], [309, 248], [251, 248]], [[362, 229], [419, 229], [419, 248], [362, 248]], [[473, 229], [529, 229], [529, 248], [473, 248]], [[583, 229], [638, 229], [638, 248], [583, 248]], [[6, 251], [52, 257], [50, 280], [3, 275]], [[116, 251], [162, 255], [160, 280], [113, 275]], [[225, 251], [272, 255], [270, 280], [223, 275]], [[335, 251], [382, 255], [380, 280], [333, 275]], [[445, 251], [492, 255], [490, 280], [443, 275]], [[555, 251], [602, 255], [599, 280], [553, 275]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图4.jpg", "主图1.jpg", "主图2.jpg", "主图3.jpg", "891533fc-6946. 800x1200.jpg", "900x900", "900x900", "900x900", "900x900", "900x900", "900x900", "19.jpg", "18.jpg", "17.jpg", "16.jpg", "15.jpg", "13.jpg"], "rec_scores": [0.9670291543006897, 0.9156318306922913, 0.9780141115188599, 0.9093450307846069, 0.9155364632606506, 0.9856975078582764, 0.9218339920043945, 0.967612624168396, 0.894110381603241, 0.9554474949836731, 0.9008076190948486, 0.9291266202926636, 0.9650754928588867, 0.9677758812904358, 0.9582953453063965, 0.9376793503761292, 0.9311735033988953, 0.9474053382873535, 0.9590567946434021, 0.9869281649589539, 0.9928261637687683, 0.9862267374992371, 0.994172990322113], "rec_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[141, 86], [200, 86], [200, 108], [141, 108]], [[251, 86], [311, 86], [311, 108], [251, 108]], [[361, 86], [420, 86], [420, 108], [361, 108]], [[471, 86], [530, 86], [530, 108], [471, 108]], [[578, 87], [643, 87], [643, 106], [578, 106]], [[6, 109], [68, 113], [67, 136], [5, 132]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[225, 108], [289, 112], [287, 138], [223, 134]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[31, 225], [89, 228], [88, 248], [30, 245]], [[142, 229], [199, 229], [199, 248], [142, 248]], [[251, 229], [309, 229], [309, 248], [251, 248]], [[362, 229], [419, 229], [419, 248], [362, 248]], [[473, 229], [529, 229], [529, 248], [473, 248]], [[583, 229], [638, 229], [638, 248], [583, 248]], [[6, 251], [52, 257], [50, 280], [3, 275]], [[116, 251], [162, 255], [160, 280], [113, 275]], [[225, 251], [272, 255], [270, 280], [223, 275]], [[335, 251], [382, 255], [380, 280], [333, 275]], [[445, 251], [492, 255], [490, 280], [443, 275]], [[555, 251], [602, 255], [599, 280], [553, 275]]], "rec_boxes": [[32, 87, 89, 106], [141, 86, 200, 108], [251, 86, 311, 108], [361, 86, 420, 108], [471, 86, 530, 108], [578, 87, 643, 106], [5, 109, 68, 136], [114, 109, 179, 137], [223, 108, 289, 138], [333, 109, 399, 137], [446, 111, 641, 135], [30, 225, 89, 248], [142, 229, 199, 248], [251, 229, 309, 248], [362, 229, 419, 248], [473, 229, 529, 248], [583, 229, 638, 248], [3, 251, 52, 280], [113, 251, 162, 280], [223, 251, 272, 280], [333, 251, 382, 280], [443, 251, 492, 280], [553, 251, 602, 280]]}}