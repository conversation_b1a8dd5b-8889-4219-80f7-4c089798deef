{"res": {"input_path": "logs\\debug_uuid_screenshot_20250820_105327.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[29, 85], [89, 85], [89, 107], [29, 107]], [[141, 86], [201, 86], [201, 108], [141, 108]], [[251, 86], [311, 86], [311, 108], [251, 108]], [[361, 86], [421, 86], [421, 108], [361, 108]], [[471, 86], [530, 86], [530, 108], [471, 108]], [[578, 87], [642, 87], [642, 106], [578, 106]], [[5, 109], [69, 112], [68, 137], [4, 135]], [[115, 108], [179, 111], [178, 137], [114, 135]], [[225, 108], [289, 111], [288, 137], [224, 135]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[33, 231], [88, 231], [88, 247], [33, 247]], [[142, 229], [199, 229], [199, 248], [142, 248]], [[253, 231], [308, 231], [308, 247], [253, 247]], [[362, 229], [419, 229], [419, 248], [362, 248]], [[473, 229], [528, 229], [528, 248], [473, 248]], [[581, 226], [639, 229], [638, 251], [580, 248]], [[6, 251], [52, 257], [50, 280], [3, 274]], [[115, 252], [162, 256], [160, 280], [113, 275]], [[226, 250], [272, 254], [270, 281], [223, 276]], [[335, 251], [382, 255], [380, 280], [333, 275]], [[445, 251], [492, 255], [490, 280], [443, 275]], [[555, 251], [602, 255], [599, 280], [553, 275]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图4.jpg", "主图3.jpg", "主图1.jpg", "主图2.jpg", "1692c399-758c.… 800x1200.jpg", "900x900", "900x900", "900x900", "900x900", "900x900", "900×900", "16.jpg", "15.jpg", "14.jpg", "13.jpg", "12.jpg", "10.jpg"], "rec_scores": [0.9447421431541443, 0.9662269353866577, 0.8952680826187134, 0.9479063153266907, 0.9576390981674194, 0.9717193245887756, 0.9113383293151855, 0.9310798645019531, 0.9172719120979309, 0.9905756115913391, 0.8983970284461975, 0.9456266760826111, 0.9538982510566711, 0.9924029111862183, 0.9801235795021057, 0.93849116563797, 0.9341000914573669, 0.9936586022377014, 0.9467832446098328, 0.9756194949150085, 0.9947006702423096, 0.9971208572387695, 0.9853801727294922], "rec_polys": [[[29, 85], [89, 85], [89, 107], [29, 107]], [[141, 86], [201, 86], [201, 108], [141, 108]], [[251, 86], [311, 86], [311, 108], [251, 108]], [[361, 86], [421, 86], [421, 108], [361, 108]], [[471, 86], [530, 86], [530, 108], [471, 108]], [[578, 87], [642, 87], [642, 106], [578, 106]], [[5, 109], [69, 112], [68, 137], [4, 135]], [[115, 108], [179, 111], [178, 137], [114, 135]], [[225, 108], [289, 111], [288, 137], [224, 135]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[33, 231], [88, 231], [88, 247], [33, 247]], [[142, 229], [199, 229], [199, 248], [142, 248]], [[253, 231], [308, 231], [308, 247], [253, 247]], [[362, 229], [419, 229], [419, 248], [362, 248]], [[473, 229], [528, 229], [528, 248], [473, 248]], [[581, 226], [639, 229], [638, 251], [580, 248]], [[6, 251], [52, 257], [50, 280], [3, 274]], [[115, 252], [162, 256], [160, 280], [113, 275]], [[226, 250], [272, 254], [270, 281], [223, 276]], [[335, 251], [382, 255], [380, 280], [333, 275]], [[445, 251], [492, 255], [490, 280], [443, 275]], [[555, 251], [602, 255], [599, 280], [553, 275]]], "rec_boxes": [[29, 85, 89, 107], [141, 86, 201, 108], [251, 86, 311, 108], [361, 86, 421, 108], [471, 86, 530, 108], [578, 87, 642, 106], [4, 109, 69, 137], [114, 108, 179, 137], [224, 108, 289, 137], [333, 109, 399, 137], [446, 111, 641, 135], [33, 231, 88, 247], [142, 229, 199, 248], [253, 231, 308, 247], [362, 229, 419, 248], [473, 229, 528, 248], [580, 226, 639, 251], [3, 251, 52, 280], [113, 252, 162, 280], [223, 250, 272, 281], [333, 251, 382, 280], [443, 251, 492, 280], [553, 251, 602, 280]]}}