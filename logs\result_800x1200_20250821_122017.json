{"res": {"input_path": "logs\\debug_800x1200_screenshot_20250821_122017.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[34, 91], [93, 91], [93, 110], [34, 110]], [[144, 91], [202, 91], [202, 110], [144, 110]], [[255, 93], [310, 93], [310, 109], [255, 109]], [[364, 92], [422, 92], [422, 111], [364, 111]], [[474, 92], [530, 92], [530, 111], [474, 111]], [[578, 89], [645, 92], [644, 112], [578, 110]], [[8, 113], [69, 117], [68, 140], [7, 136]], [[118, 113], [179, 117], [177, 140], [116, 136]], [[228, 113], [290, 117], [288, 140], [226, 136]], [[338, 113], [400, 117], [398, 140], [336, 136]], [[448, 115], [643, 117], [642, 139], [448, 137]], [[34, 233], [91, 233], [91, 252], [34, 252]], [[143, 231], [203, 231], [203, 253], [143, 253]], [[253, 232], [313, 232], [313, 254], [253, 254]], [[363, 232], [422, 232], [422, 254], [363, 254]], [[475, 235], [529, 235], [529, 251], [475, 251]], [[585, 235], [639, 235], [639, 251], [585, 251]], [[9, 255], [53, 259], [51, 284], [6, 279]], [[118, 258], [163, 258], [163, 283], [118, 283]], [[229, 255], [274, 260], [272, 284], [226, 279]], [[337, 255], [377, 259], [375, 284], [335, 280]], [[447, 256], [487, 259], [485, 284], [445, 280]], [[557, 256], [597, 259], [595, 284], [555, 280]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图4.jpg", "主图2.jpg", "主图3.jpg", "主图1.jpg", "186c1862-9663 8001200.jpg", "900x900", "900×900", "900x900", "900x900", "900x900", "900x900", "12.jpg", "11.jpg", "10.jpg", "9.jpg", "8.jpg", "7.jpg"], "rec_scores": [0.9771952629089355, 0.9508270621299744, 0.9675056338310242, 0.9762051701545715, 0.9814582467079163, 0.9841840863227844, 0.9248542189598083, 0.9792531728744507, 0.9226723313331604, 0.9499945640563965, 0.9337700009346008, 0.9614510536193848, 0.88447105884552, 0.9314253926277161, 0.968615710735321, 0.9620932936668396, 0.9744953513145447, 0.9917308688163757, 0.9901816248893738, 0.9635313153266907, 0.9927252531051636, 0.9969016313552856, 0.990766167640686], "rec_polys": [[[34, 91], [93, 91], [93, 110], [34, 110]], [[144, 91], [202, 91], [202, 110], [144, 110]], [[255, 93], [310, 93], [310, 109], [255, 109]], [[364, 92], [422, 92], [422, 111], [364, 111]], [[474, 92], [530, 92], [530, 111], [474, 111]], [[578, 89], [645, 92], [644, 112], [578, 110]], [[8, 113], [69, 117], [68, 140], [7, 136]], [[118, 113], [179, 117], [177, 140], [116, 136]], [[228, 113], [290, 117], [288, 140], [226, 136]], [[338, 113], [400, 117], [398, 140], [336, 136]], [[448, 115], [643, 117], [642, 139], [448, 137]], [[34, 233], [91, 233], [91, 252], [34, 252]], [[143, 231], [203, 231], [203, 253], [143, 253]], [[253, 232], [313, 232], [313, 254], [253, 254]], [[363, 232], [422, 232], [422, 254], [363, 254]], [[475, 235], [529, 235], [529, 251], [475, 251]], [[585, 235], [639, 235], [639, 251], [585, 251]], [[9, 255], [53, 259], [51, 284], [6, 279]], [[118, 258], [163, 258], [163, 283], [118, 283]], [[229, 255], [274, 260], [272, 284], [226, 279]], [[337, 255], [377, 259], [375, 284], [335, 280]], [[447, 256], [487, 259], [485, 284], [445, 280]], [[557, 256], [597, 259], [595, 284], [555, 280]]], "rec_boxes": [[34, 91, 93, 110], [144, 91, 202, 110], [255, 93, 310, 109], [364, 92, 422, 111], [474, 92, 530, 111], [578, 89, 645, 112], [7, 113, 69, 140], [116, 113, 179, 140], [226, 113, 290, 140], [336, 113, 400, 140], [448, 115, 643, 139], [34, 233, 91, 252], [143, 231, 203, 253], [253, 232, 313, 254], [363, 232, 422, 254], [475, 235, 529, 251], [585, 235, 639, 251], [6, 255, 53, 284], [118, 258, 163, 283], [226, 255, 274, 284], [335, 255, 377, 284], [445, 256, 487, 284], [555, 256, 597, 284]]}}