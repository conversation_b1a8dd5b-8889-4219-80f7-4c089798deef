{"res": {"input_path": "logs\\debug_uuid_screenshot_20250820_002409.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 87], [90, 87], [90, 106], [32, 106]], [[141, 86], [200, 86], [200, 108], [141, 108]], [[251, 86], [311, 86], [311, 108], [251, 108]], [[360, 85], [420, 85], [420, 107], [360, 107]], [[470, 86], [530, 86], [530, 108], [470, 108]], [[577, 86], [643, 86], [643, 108], [577, 108]], [[6, 109], [68, 113], [67, 136], [5, 132]], [[115, 108], [179, 111], [178, 137], [114, 135]], [[225, 109], [289, 112], [288, 137], [224, 135]], [[334, 108], [399, 111], [398, 137], [332, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[33, 231], [89, 231], [89, 247], [33, 247]], [[142, 227], [200, 230], [199, 250], [141, 247]], [[252, 229], [309, 229], [309, 248], [252, 248]], [[360, 226], [421, 229], [420, 250], [359, 247]], [[472, 227], [529, 230], [528, 250], [471, 247]], [[582, 229], [640, 229], [640, 248], [582, 248]], [[6, 251], [52, 255], [50, 280], [3, 275]], [[116, 251], [162, 255], [160, 280], [113, 275]], [[224, 250], [266, 253], [264, 281], [222, 277]], [[334, 250], [376, 253], [374, 281], [332, 277]], [[444, 250], [486, 253], [484, 281], [442, 277]], [[555, 252], [595, 255], [593, 280], [553, 276]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图4.jpg", "主图1.jpg", "主图3.jpg", "主图2.jpg", "d6cc24c2-301e.. 800x1200.jpg", "900x900", "900×900", "900x900", "900×900", "900×902", "900x897", "10.jpg", "11.jpg", "9.jpg", "8.jpg", "6.jpg", "7.jpg"], "rec_scores": [0.9790562391281128, 0.9397234320640564, 0.9037562012672424, 0.9117410778999329, 0.9641535878181458, 0.9799734354019165, 0.9218339920043945, 0.9172719120979309, 0.941493809223175, 0.9478397369384766, 0.9056123495101929, 0.9526950716972351, 0.9380427002906799, 0.9569226503372192, 0.9226869344711304, 0.9267522096633911, 0.95523601770401, 0.964367687702179, 0.9589633345603943, 0.9934015274047852, 0.9855512380599976, 0.9798328280448914, 0.990766167640686], "rec_polys": [[[32, 87], [90, 87], [90, 106], [32, 106]], [[141, 86], [200, 86], [200, 108], [141, 108]], [[251, 86], [311, 86], [311, 108], [251, 108]], [[360, 85], [420, 85], [420, 107], [360, 107]], [[470, 86], [530, 86], [530, 108], [470, 108]], [[577, 86], [643, 86], [643, 108], [577, 108]], [[6, 109], [68, 113], [67, 136], [5, 132]], [[115, 108], [179, 111], [178, 137], [114, 135]], [[225, 109], [289, 112], [288, 137], [224, 135]], [[334, 108], [399, 111], [398, 137], [332, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[33, 231], [89, 231], [89, 247], [33, 247]], [[142, 227], [200, 230], [199, 250], [141, 247]], [[252, 229], [309, 229], [309, 248], [252, 248]], [[360, 226], [421, 229], [420, 250], [359, 247]], [[472, 227], [529, 230], [528, 250], [471, 247]], [[582, 229], [640, 229], [640, 248], [582, 248]], [[6, 251], [52, 255], [50, 280], [3, 275]], [[116, 251], [162, 255], [160, 280], [113, 275]], [[224, 250], [266, 253], [264, 281], [222, 277]], [[334, 250], [376, 253], [374, 281], [332, 277]], [[444, 250], [486, 253], [484, 281], [442, 277]], [[555, 252], [595, 255], [593, 280], [553, 276]]], "rec_boxes": [[32, 87, 90, 106], [141, 86, 200, 108], [251, 86, 311, 108], [360, 85, 420, 107], [470, 86, 530, 108], [577, 86, 643, 108], [5, 109, 68, 136], [114, 108, 179, 137], [224, 109, 289, 137], [332, 108, 399, 137], [446, 111, 641, 135], [33, 231, 89, 247], [141, 227, 200, 250], [252, 229, 309, 248], [359, 226, 421, 250], [471, 227, 529, 250], [582, 229, 640, 248], [3, 251, 52, 280], [113, 251, 162, 280], [222, 250, 266, 281], [332, 250, 376, 281], [442, 250, 486, 281], [553, 252, 595, 280]]}}