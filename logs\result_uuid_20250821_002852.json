{"res": {"input_path": "logs\\debug_uuid_screenshot_20250821_002852.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 89], [89, 89], [89, 108], [32, 108]], [[142, 89], [199, 89], [199, 108], [142, 108]], [[251, 85], [311, 88], [310, 110], [250, 107]], [[362, 89], [419, 89], [419, 108], [362, 108]], [[471, 88], [530, 88], [530, 110], [471, 110]], [[579, 89], [643, 89], [643, 108], [579, 108]], [[6, 112], [68, 116], [67, 138], [5, 134]], [[116, 112], [178, 115], [177, 138], [115, 136]], [[226, 112], [288, 115], [287, 138], [225, 136]], [[335, 112], [398, 115], [397, 138], [334, 136]], [[446, 113], [641, 115], [640, 137], [446, 135]], [[30, 230], [90, 230], [90, 252], [30, 252]], [[142, 231], [199, 231], [199, 250], [142, 250]], [[252, 231], [309, 231], [309, 250], [252, 250]], [[362, 231], [419, 231], [419, 250], [362, 250]], [[472, 231], [530, 231], [530, 250], [472, 250]], [[582, 230], [640, 230], [640, 252], [582, 252]], [[6, 254], [51, 257], [49, 280], [5, 277]], [[116, 253], [161, 258], [159, 282], [113, 277]], [[227, 253], [272, 258], [270, 282], [224, 277]], [[335, 254], [375, 257], [373, 282], [333, 278]], [[445, 254], [485, 257], [483, 282], [443, 278]], [[555, 255], [595, 258], [593, 282], [553, 278]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图3.jpg", "主图4.jpg", "主图1.jpg", "主图2.jpg", "30433f99-41f5-…8001200.jpg", "900x900", "900x900", "900x900", "900x900", "900x900", "900x900", "12.jpg", "11,jpg", "10.jpg", "9.jpg", "8.jpg", "7.jpg"], "rec_scores": [0.9850908517837524, 0.9741424918174744, 0.9286760091781616, 0.972716748714447, 0.9553089737892151, 0.9907468557357788, 0.9249511361122131, 0.9399977326393127, 0.9779725670814514, 0.9870602488517761, 0.8978465795516968, 0.9444628357887268, 0.9393075704574585, 0.9508438110351562, 0.9266534447669983, 0.9363008141517639, 0.9552982449531555, 0.9940526485443115, 0.9382902979850769, 0.9635313153266907, 0.9945552945137024, 0.9969016313552856, 0.9956678152084351], "rec_polys": [[[32, 89], [89, 89], [89, 108], [32, 108]], [[142, 89], [199, 89], [199, 108], [142, 108]], [[251, 85], [311, 88], [310, 110], [250, 107]], [[362, 89], [419, 89], [419, 108], [362, 108]], [[471, 88], [530, 88], [530, 110], [471, 110]], [[579, 89], [643, 89], [643, 108], [579, 108]], [[6, 112], [68, 116], [67, 138], [5, 134]], [[116, 112], [178, 115], [177, 138], [115, 136]], [[226, 112], [288, 115], [287, 138], [225, 136]], [[335, 112], [398, 115], [397, 138], [334, 136]], [[446, 113], [641, 115], [640, 137], [446, 135]], [[30, 230], [90, 230], [90, 252], [30, 252]], [[142, 231], [199, 231], [199, 250], [142, 250]], [[252, 231], [309, 231], [309, 250], [252, 250]], [[362, 231], [419, 231], [419, 250], [362, 250]], [[472, 231], [530, 231], [530, 250], [472, 250]], [[582, 230], [640, 230], [640, 252], [582, 252]], [[6, 254], [51, 257], [49, 280], [5, 277]], [[116, 253], [161, 258], [159, 282], [113, 277]], [[227, 253], [272, 258], [270, 282], [224, 277]], [[335, 254], [375, 257], [373, 282], [333, 278]], [[445, 254], [485, 257], [483, 282], [443, 278]], [[555, 255], [595, 258], [593, 282], [553, 278]]], "rec_boxes": [[32, 89, 89, 108], [142, 89, 199, 108], [250, 85, 311, 110], [362, 89, 419, 108], [471, 88, 530, 110], [579, 89, 643, 108], [5, 112, 68, 138], [115, 112, 178, 138], [225, 112, 288, 138], [334, 112, 398, 138], [446, 113, 641, 137], [30, 230, 90, 252], [142, 231, 199, 250], [252, 231, 309, 250], [362, 231, 419, 250], [472, 231, 530, 250], [582, 230, 640, 252], [5, 254, 51, 280], [113, 253, 161, 282], [224, 253, 272, 282], [333, 254, 375, 282], [443, 254, 485, 282], [553, 255, 595, 282]]}}