{"res": {"input_path": "logs\\debug_800x1200_screenshot_20250821_002902.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[34, 93], [92, 93], [92, 112], [34, 112]], [[143, 93], [201, 93], [201, 112], [143, 112]], [[254, 93], [311, 93], [311, 112], [254, 112]], [[364, 93], [421, 93], [421, 112], [364, 112]], [[474, 90], [532, 93], [531, 113], [473, 110]], [[580, 93], [645, 93], [645, 112], [580, 112]], [[8, 115], [70, 119], [69, 142], [7, 138]], [[118, 115], [180, 120], [178, 144], [116, 138]], [[228, 115], [290, 119], [288, 142], [226, 138]], [[338, 115], [400, 119], [398, 142], [336, 138]], [[448, 117], [643, 120], [642, 141], [448, 138]], [[32, 234], [91, 234], [91, 256], [32, 256]], [[143, 234], [201, 234], [201, 256], [143, 256]], [[254, 234], [312, 234], [312, 256], [254, 256]], [[365, 235], [422, 235], [422, 254], [365, 254]], [[474, 234], [531, 234], [531, 256], [474, 256]], [[584, 230], [643, 233], [642, 256], [583, 253]], [[9, 257], [53, 261], [51, 286], [6, 281]], [[119, 257], [163, 261], [161, 286], [116, 281]], [[229, 257], [274, 262], [272, 286], [226, 281]], [[337, 258], [377, 261], [375, 286], [335, 282]], [[447, 258], [487, 261], [485, 286], [445, 282]], [[558, 258], [597, 263], [594, 286], [555, 281]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图3.jpg", "主图4.jpg", "主图1.jpg", "主图2.jpg", "30433f99-41f5-. 800x1200.jpg", "900x900", "900x900", "900x900", "900×900", "900x900", "900×900", "12.jpg", "11,jpg", "10.jpg", "9.jpg", "7.jpg", "8.jpg"], "rec_scores": [0.9846022725105286, 0.9100794196128845, 0.9655996561050415, 0.972716748714447, 0.9602365493774414, 0.9803328514099121, 0.9683977365493774, 0.958002507686615, 0.9499945640563965, 0.9826864004135132, 0.9555643200874329, 0.9536916017532349, 0.9115973114967346, 0.929228663444519, 0.9430876970291138, 0.9619291424751282, 0.9442608952522278, 0.9917308688163757, 0.9411191344261169, 0.9635313153266907, 0.9945552945137024, 0.990766167640686, 0.9962676167488098], "rec_polys": [[[34, 93], [92, 93], [92, 112], [34, 112]], [[143, 93], [201, 93], [201, 112], [143, 112]], [[254, 93], [311, 93], [311, 112], [254, 112]], [[364, 93], [421, 93], [421, 112], [364, 112]], [[474, 90], [532, 93], [531, 113], [473, 110]], [[580, 93], [645, 93], [645, 112], [580, 112]], [[8, 115], [70, 119], [69, 142], [7, 138]], [[118, 115], [180, 120], [178, 144], [116, 138]], [[228, 115], [290, 119], [288, 142], [226, 138]], [[338, 115], [400, 119], [398, 142], [336, 138]], [[448, 117], [643, 120], [642, 141], [448, 138]], [[32, 234], [91, 234], [91, 256], [32, 256]], [[143, 234], [201, 234], [201, 256], [143, 256]], [[254, 234], [312, 234], [312, 256], [254, 256]], [[365, 235], [422, 235], [422, 254], [365, 254]], [[474, 234], [531, 234], [531, 256], [474, 256]], [[584, 230], [643, 233], [642, 256], [583, 253]], [[9, 257], [53, 261], [51, 286], [6, 281]], [[119, 257], [163, 261], [161, 286], [116, 281]], [[229, 257], [274, 262], [272, 286], [226, 281]], [[337, 258], [377, 261], [375, 286], [335, 282]], [[447, 258], [487, 261], [485, 286], [445, 282]], [[558, 258], [597, 263], [594, 286], [555, 281]]], "rec_boxes": [[34, 93, 92, 112], [143, 93, 201, 112], [254, 93, 311, 112], [364, 93, 421, 112], [473, 90, 532, 113], [580, 93, 645, 112], [7, 115, 70, 142], [116, 115, 180, 144], [226, 115, 290, 142], [336, 115, 400, 142], [448, 117, 643, 141], [32, 234, 91, 256], [143, 234, 201, 256], [254, 234, 312, 256], [365, 235, 422, 254], [474, 234, 531, 256], [583, 230, 643, 256], [6, 257, 53, 286], [116, 257, 163, 286], [226, 257, 274, 286], [335, 258, 377, 286], [445, 258, 487, 286], [555, 258, 597, 286]]}}