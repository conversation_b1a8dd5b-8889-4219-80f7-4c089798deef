[15:32:22] [    INFO] [测试3.py:80] - ==================================================
[15:32:22] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:32:22] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250828.log
[15:32:22] [    INFO] [测试3.py:83] - ==================================================
[15:32:22] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:32:22] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:32:22] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:32:28] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:32:28] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:32:28] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:32:28] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[15:32:28] [    INFO] [测试3.py:172] - UUID图片: 056c0261-ff21-4945-8358-de4ead56f746.png
[15:32:28] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 1
[15:32:28] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:32:28] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:32:29] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:32:29] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:32:29] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:32:29] [    INFO] [测试3.py:1291] - 已点击坐标
[15:32:33] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:32:33] [ WARNING] [测试3.py:1309] - 未找到SHANGCHUANG.png，等待1.2秒后重试
[15:32:35] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:32:35] [ WARNING] [测试3.py:1312] - 重试后仍未找到上传按钮，继续执行后续步骤
[15:32:38] [    INFO] [测试3.py:1324] - 开始查找1号文件夹...
[15:32:38] [    INFO] [测试3.py:384] - 开始查找文件夹: 1
[15:32:39] [    INFO] [测试3.py:416] - 文件夹1的目标坐标: (209, 140)
[15:32:39] [    INFO] [测试3.py:421] - 当前鼠标位置: (93, 199)
[15:32:39] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 140)
[15:32:39] [    INFO] [测试3.py:451] - 执行点击 #1
[15:32:40] [    INFO] [测试3.py:451] - 执行点击 #2
[15:32:41] [    INFO] [测试3.py:457] - 等待文件夹打开...
[15:32:41] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\1
[15:32:41] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[15:32:42] [    INFO] [测试3.py:1329] - 移动鼠标到(108,600)位置...
[15:32:43] [    INFO] [测试3.py:635] - 开始选择图片...
[15:32:45] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[15:32:45] [    INFO] [测试3.py:655] - 目标UUID: 056c0261-ff21-4945-8358-de4ead56f746.png
[15:32:45] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['056', '56c', '6c0', 'c02', '026', '261']
[15:32:45] [    INFO] [测试3.py:1562] - 开始OCR识别图片: logs\debug_files_screenshot.png
[15:32:47] [    INFO] [测试3.py:1579] - 当前识别场景: unknown
[15:32:47] [   DEBUG] [测试3.py:1582] - 正在执行OCR识别...
[15:32:49] [   DEBUG] [测试3.py:1585] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 53,  96],
       ...,
       [ 50, 119]], dtype=int16), array([[160,  98],
       ...,
       [160, 123]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  91],
       ...,
       [477, 122]], dtype=int16), array([[587,  96],
       ...,
       [587, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[805,  92],
       ...,
       [802, 122]], dtype=int16), array([[912,  92],
       ...,
       [909, 122]], dtype=int16), array([[1019,   92],
       ...,
       [1016,  122]], dtype=int16), array([[1126,   92],
       ...,
       [1123,  120]], dtype=int16), array([[1234,   92],
       ...,
       [1232,  120]], dtype=int16), array([[1345,   96],
       ...,
       [1342,  119]], dtype=int16), array([[1430,   98],
       ...,
       [1430,  120]], dtype=int16), array([[1543,   94],
       ...,
       [1542,  117]], dtype=int16), array([[1658,   96],
       ...,
       [1656,  119]], dtype=int16), array([[1429,  112],
       ...,
       [1428,  135]], dtype=int16), array([[1427,  128],
       ...,
       [1426,  151]], dtype=int16), array([[ 38, 246],
       ...,
       [ 36, 274]], dtype=int16), array([[147, 244],
       ...,
       [144, 274]], dtype=int16), array([[253, 246],
       ...,
       [251, 274]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9,jpeg', '10,jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '056c0261-ff21-', '800x1200.jpg', '主图1.jpeg', '4945-8358-de4', 'ead56f746.png', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg'], 'rec_scores': [0.9746143221855164, 0.9788720607757568, 0.9302626252174377, 0.9823043942451477, 0.9520460963249207, 0.8875851631164551, 0.941087543964386, 0.9538896083831787, 0.8853468298912048, 0.9355347752571106, 0.9877234101295471, 0.992492139339447, 0.9567533731460571, 0.9813222885131836, 0.9516213536262512, 0.9775974750518799, 0.9984042048454285, 0.9964826107025146, 0.9405160546302795, 0.9690349102020264, 0.9812813401222229], 'rec_polys': [array([[ 53,  96],
       ...,
       [ 50, 119]], dtype=int16), array([[160,  98],
       ...,
       [160, 123]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  91],
       ...,
       [477, 122]], dtype=int16), array([[587,  96],
       ...,
       [587, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[805,  92],
       ...,
       [802, 122]], dtype=int16), array([[912,  92],
       ...,
       [909, 122]], dtype=int16), array([[1019,   92],
       ...,
       [1016,  122]], dtype=int16), array([[1126,   92],
       ...,
       [1123,  120]], dtype=int16), array([[1234,   92],
       ...,
       [1232,  120]], dtype=int16), array([[1345,   96],
       ...,
       [1342,  119]], dtype=int16), array([[1430,   98],
       ...,
       [1430,  120]], dtype=int16), array([[1543,   94],
       ...,
       [1542,  117]], dtype=int16), array([[1658,   96],
       ...,
       [1656,  119]], dtype=int16), array([[1429,  112],
       ...,
       [1428,  135]], dtype=int16), array([[1427,  128],
       ...,
       [1426,  151]], dtype=int16), array([[ 38, 246],
       ...,
       [ 36, 274]], dtype=int16), array([[147, 244],
       ...,
       [144, 274]], dtype=int16), array([[253, 246],
       ...,
       [251, 274]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 50, ..., 124],
       ...,
       [251, ..., 279]], dtype=int16)}]
[15:32:49] [    INFO] [测试3.py:1553] - OCR结果已保存到: logs\result_20250828_153249_unknown.txt 和 logs\result_20250828_153249_unknown.json
[15:32:49] [    INFO] [测试3.py:1428] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 53,  96],
       ...,
       [ 50, 119]], dtype=int16), array([[160,  98],
       ...,
       [160, 123]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  91],
       ...,
       [477, 122]], dtype=int16), array([[587,  96],
       ...,
       [587, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[805,  92],
       ...,
       [802, 122]], dtype=int16), array([[912,  92],
       ...,
       [909, 122]], dtype=int16), array([[1019,   92],
       ...,
       [1016,  122]], dtype=int16), array([[1126,   92],
       ...,
       [1123,  120]], dtype=int16), array([[1234,   92],
       ...,
       [1232,  120]], dtype=int16), array([[1345,   96],
       ...,
       [1342,  119]], dtype=int16), array([[1430,   98],
       ...,
       [1430,  120]], dtype=int16), array([[1543,   94],
       ...,
       [1542,  117]], dtype=int16), array([[1658,   96],
       ...,
       [1656,  119]], dtype=int16), array([[1429,  112],
       ...,
       [1428,  135]], dtype=int16), array([[1427,  128],
       ...,
       [1426,  151]], dtype=int16), array([[ 38, 246],
       ...,
       [ 36, 274]], dtype=int16), array([[147, 244],
       ...,
       [144, 274]], dtype=int16), array([[253, 246],
       ...,
       [251, 274]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9,jpeg', '10,jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '056c0261-ff21-', '800x1200.jpg', '主图1.jpeg', '4945-8358-de4', 'ead56f746.png', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg'], 'rec_scores': [0.9746143221855164, 0.9788720607757568, 0.9302626252174377, 0.9823043942451477, 0.9520460963249207, 0.8875851631164551, 0.941087543964386, 0.9538896083831787, 0.8853468298912048, 0.9355347752571106, 0.9877234101295471, 0.992492139339447, 0.9567533731460571, 0.9813222885131836, 0.9516213536262512, 0.9775974750518799, 0.9984042048454285, 0.9964826107025146, 0.9405160546302795, 0.9690349102020264, 0.9812813401222229], 'rec_polys': [array([[ 53,  96],
       ...,
       [ 50, 119]], dtype=int16), array([[160,  98],
       ...,
       [160, 123]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  91],
       ...,
       [477, 122]], dtype=int16), array([[587,  96],
       ...,
       [587, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[805,  92],
       ...,
       [802, 122]], dtype=int16), array([[912,  92],
       ...,
       [909, 122]], dtype=int16), array([[1019,   92],
       ...,
       [1016,  122]], dtype=int16), array([[1126,   92],
       ...,
       [1123,  120]], dtype=int16), array([[1234,   92],
       ...,
       [1232,  120]], dtype=int16), array([[1345,   96],
       ...,
       [1342,  119]], dtype=int16), array([[1430,   98],
       ...,
       [1430,  120]], dtype=int16), array([[1543,   94],
       ...,
       [1542,  117]], dtype=int16), array([[1658,   96],
       ...,
       [1656,  119]], dtype=int16), array([[1429,  112],
       ...,
       [1428,  135]], dtype=int16), array([[1427,  128],
       ...,
       [1426,  151]], dtype=int16), array([[ 38, 246],
       ...,
       [ 36, 274]], dtype=int16), array([[147, 244],
       ...,
       [144, 274]], dtype=int16), array([[253, 246],
       ...,
       [251, 274]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 50, ..., 124],
       ...,
       [251, ..., 279]], dtype=int16)}
[15:32:49] [    INFO] [测试3.py:1435] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[53, 96], [99, 101], [96, 124], [50, 119]], [[160, 98], [205, 98], [205, 123], [160, 123]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 91], [534, 99], [530, 129], [477, 122]], [[587, 96], [640, 96], [640, 125], [587, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[805, 92], [858, 97], [855, 128], [802, 122]], [[912, 92], [965, 97], [962, 128], [909, 122]], [[1019, 92], [1076, 97], [1073, 128], [1016, 122]], [[1126, 92], [1183, 97], [1180, 126], [1123, 120]], [[1234, 92], [1291, 97], [1289, 126], [1232, 120]], [[1345, 96], [1398, 101], [1396, 124], [1342, 119]], [[1430, 98], [1524, 98], [1524, 120], [1430, 120]], [[1543, 94], [1630, 99], [1629, 122], [1542, 117]], [[1658, 96], [1730, 100], [1728, 124], [1656, 119]], [[1429, 112], [1527, 116], [1525, 140], [1428, 135]], [[1427, 128], [1525, 132], [1524, 156], [1426, 151]], [[38, 246], [111, 251], [109, 279], [36, 274]], [[147, 244], [220, 251], [217, 281], [144, 274]], [[253, 246], [327, 251], [325, 279], [251, 274]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9,jpeg', '10,jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '056c0261-ff21-', '800x1200.jpg', '主图1.jpeg', '4945-8358-de4', 'ead56f746.png', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg'], 'rec_scores': [0.9746143221855164, 0.9788720607757568, 0.9302626252174377, 0.9823043942451477, 0.9520460963249207, 0.8875851631164551, 0.941087543964386, 0.9538896083831787, 0.8853468298912048, 0.9355347752571106, 0.9877234101295471, 0.992492139339447, 0.9567533731460571, 0.9813222885131836, 0.9516213536262512, 0.9775974750518799, 0.9984042048454285, 0.9964826107025146, 0.9405160546302795, 0.9690349102020264, 0.9812813401222229], 'rec_polys': [[[53, 96], [99, 101], [96, 124], [50, 119]], [[160, 98], [205, 98], [205, 123], [160, 123]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 91], [534, 99], [530, 129], [477, 122]], [[587, 96], [640, 96], [640, 125], [587, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[805, 92], [858, 97], [855, 128], [802, 122]], [[912, 92], [965, 97], [962, 128], [909, 122]], [[1019, 92], [1076, 97], [1073, 128], [1016, 122]], [[1126, 92], [1183, 97], [1180, 126], [1123, 120]], [[1234, 92], [1291, 97], [1289, 126], [1232, 120]], [[1345, 96], [1398, 101], [1396, 124], [1342, 119]], [[1430, 98], [1524, 98], [1524, 120], [1430, 120]], [[1543, 94], [1630, 99], [1629, 122], [1542, 117]], [[1658, 96], [1730, 100], [1728, 124], [1656, 119]], [[1429, 112], [1527, 116], [1525, 140], [1428, 135]], [[1427, 128], [1525, 132], [1524, 156], [1426, 151]], [[38, 246], [111, 251], [109, 279], [36, 274]], [[147, 244], [220, 251], [217, 281], [144, 274]], [[253, 246], [327, 251], [325, 279], [251, 274]]], 'rec_boxes': [[50, 96, 99, 124], [160, 98, 205, 123], [266, 98, 315, 125], [372, 96, 423, 126], [477, 91, 534, 129], [587, 96, 640, 125], [698, 98, 747, 125], [802, 92, 858, 128], [909, 92, 965, 128], [1016, 92, 1076, 128], [1123, 92, 1183, 126], [1232, 92, 1291, 126], [1342, 96, 1398, 124], [1430, 98, 1524, 120], [1542, 94, 1630, 122], [1656, 96, 1730, 124], [1428, 112, 1527, 140], [1426, 128, 1525, 156], [36, 246, 111, 279], [144, 244, 220, 281], [251, 246, 327, 279]]}}
[15:32:49] [    INFO] [测试3.py:1444] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[53, 96], [99, 101], [96, 124], [50, 119]], [[160, 98], [205, 98], [205, 123], [160, 123]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 91], [534, 99], [530, 129], [477, 122]], [[587, 96], [640, 96], [640, 125], [587, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[805, 92], [858, 97], [855, 128], [802, 122]], [[912, 92], [965, 97], [962, 128], [909, 122]], [[1019, 92], [1076, 97], [1073, 128], [1016, 122]], [[1126, 92], [1183, 97], [1180, 126], [1123, 120]], [[1234, 92], [1291, 97], [1289, 126], [1232, 120]], [[1345, 96], [1398, 101], [1396, 124], [1342, 119]], [[1430, 98], [1524, 98], [1524, 120], [1430, 120]], [[1543, 94], [1630, 99], [1629, 122], [1542, 117]], [[1658, 96], [1730, 100], [1728, 124], [1656, 119]], [[1429, 112], [1527, 116], [1525, 140], [1428, 135]], [[1427, 128], [1525, 132], [1524, 156], [1426, 151]], [[38, 246], [111, 251], [109, 279], [36, 274]], [[147, 244], [220, 251], [217, 281], [144, 274]], [[253, 246], [327, 251], [325, 279], [251, 274]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9,jpeg', '10,jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '056c0261-ff21-', '800x1200.jpg', '主图1.jpeg', '4945-8358-de4', 'ead56f746.png', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg'], 'rec_scores': [0.9746143221855164, 0.9788720607757568, 0.9302626252174377, 0.9823043942451477, 0.9520460963249207, 0.8875851631164551, 0.941087543964386, 0.9538896083831787, 0.8853468298912048, 0.9355347752571106, 0.9877234101295471, 0.992492139339447, 0.9567533731460571, 0.9813222885131836, 0.9516213536262512, 0.9775974750518799, 0.9984042048454285, 0.9964826107025146, 0.9405160546302795, 0.9690349102020264, 0.9812813401222229], 'rec_polys': [[[53, 96], [99, 101], [96, 124], [50, 119]], [[160, 98], [205, 98], [205, 123], [160, 123]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 91], [534, 99], [530, 129], [477, 122]], [[587, 96], [640, 96], [640, 125], [587, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[805, 92], [858, 97], [855, 128], [802, 122]], [[912, 92], [965, 97], [962, 128], [909, 122]], [[1019, 92], [1076, 97], [1073, 128], [1016, 122]], [[1126, 92], [1183, 97], [1180, 126], [1123, 120]], [[1234, 92], [1291, 97], [1289, 126], [1232, 120]], [[1345, 96], [1398, 101], [1396, 124], [1342, 119]], [[1430, 98], [1524, 98], [1524, 120], [1430, 120]], [[1543, 94], [1630, 99], [1629, 122], [1542, 117]], [[1658, 96], [1730, 100], [1728, 124], [1656, 119]], [[1429, 112], [1527, 116], [1525, 140], [1428, 135]], [[1427, 128], [1525, 132], [1524, 156], [1426, 151]], [[38, 246], [111, 251], [109, 279], [36, 274]], [[147, 244], [220, 251], [217, 281], [144, 274]], [[253, 246], [327, 251], [325, 279], [251, 274]]], 'rec_boxes': [[50, 96, 99, 124], [160, 98, 205, 123], [266, 98, 315, 125], [372, 96, 423, 126], [477, 91, 534, 129], [587, 96, 640, 125], [698, 98, 747, 125], [802, 92, 858, 128], [909, 92, 965, 128], [1016, 92, 1076, 128], [1123, 92, 1183, 126], [1232, 92, 1291, 126], [1342, 96, 1398, 124], [1430, 98, 1524, 120], [1542, 94, 1630, 122], [1656, 96, 1730, 124], [1428, 112, 1527, 140], [1426, 128, 1525, 156], [36, 246, 111, 279], [144, 244, 220, 281], [251, 246, 327, 279]]}
[15:32:49] [    INFO] [测试3.py:1452] - 识别到的文本: ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9,jpeg', '10,jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '056c0261-ff21-', '800x1200.jpg', '主图1.jpeg', '4945-8358-de4', 'ead56f746.png', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:32:49] [    INFO] [测试3.py:1453] - 识别的置信度: [0.9746143221855164, 0.9788720607757568, 0.9302626252174377, 0.9823043942451477, 0.9520460963249207, 0.8875851631164551, 0.941087543964386, 0.9538896083831787, 0.8853468298912048, 0.9355347752571106, 0.9877234101295471, 0.992492139339447, 0.9567533731460571, 0.9813222885131836, 0.9516213536262512, 0.9775974750518799, 0.9984042048454285, 0.9964826107025146, 0.9405160546302795, 0.9690349102020264, 0.9812813401222229]
[15:32:49] [    INFO] [测试3.py:1454] - 识别的坐标框: [[50, 96, 99, 124], [160, 98, 205, 123], [266, 98, 315, 125], [372, 96, 423, 126], [477, 91, 534, 129], [587, 96, 640, 125], [698, 98, 747, 125], [802, 92, 858, 128], [909, 92, 965, 128], [1016, 92, 1076, 128], [1123, 92, 1183, 126], [1232, 92, 1291, 126], [1342, 96, 1398, 124], [1430, 98, 1524, 120], [1542, 94, 1630, 122], [1656, 96, 1730, 124], [1428, 112, 1527, 140], [1426, 128, 1525, 156], [36, 246, 111, 279], [144, 244, 220, 281], [251, 246, 327, 279]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=1.jpeg, 置信度=0.9746143221855164, 原始坐标=[50, 96, 99, 124], 转换后坐标=[[50, 96], [99, 96], [99, 124], [50, 124]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=2.jpeg, 置信度=0.9788720607757568, 原始坐标=[160, 98, 205, 123], 转换后坐标=[[160, 98], [205, 98], [205, 123], [160, 123]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=3.jpeg, 置信度=0.9302626252174377, 原始坐标=[266, 98, 315, 125], 转换后坐标=[[266, 98], [315, 98], [315, 125], [266, 125]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=4.jpeg, 置信度=0.9823043942451477, 原始坐标=[372, 96, 423, 126], 转换后坐标=[[372, 96], [423, 96], [423, 126], [372, 126]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=5.jpeg, 置信度=0.9520460963249207, 原始坐标=[477, 91, 534, 129], 转换后坐标=[[477, 91], [534, 91], [534, 129], [477, 129]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=6.jpeg, 置信度=0.8875851631164551, 原始坐标=[587, 96, 640, 125], 转换后坐标=[[587, 96], [640, 96], [640, 125], [587, 125]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=7.jpeg, 置信度=0.941087543964386, 原始坐标=[698, 98, 747, 125], 转换后坐标=[[698, 98], [747, 98], [747, 125], [698, 125]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=8,jpeg, 置信度=0.9538896083831787, 原始坐标=[802, 92, 858, 128], 转换后坐标=[[802, 92], [858, 92], [858, 128], [802, 128]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=9,jpeg, 置信度=0.8853468298912048, 原始坐标=[909, 92, 965, 128], 转换后坐标=[[909, 92], [965, 92], [965, 128], [909, 128]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=10,jpeg, 置信度=0.9355347752571106, 原始坐标=[1016, 92, 1076, 128], 转换后坐标=[[1016, 92], [1076, 92], [1076, 128], [1016, 128]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=11.jpeg, 置信度=0.9877234101295471, 原始坐标=[1123, 92, 1183, 126], 转换后坐标=[[1123, 92], [1183, 92], [1183, 126], [1123, 126]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=12.jpeg, 置信度=0.992492139339447, 原始坐标=[1232, 92, 1291, 126], 转换后坐标=[[1232, 92], [1291, 92], [1291, 126], [1232, 126]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=13.jpeg, 置信度=0.9567533731460571, 原始坐标=[1342, 96, 1398, 124], 转换后坐标=[[1342, 96], [1398, 96], [1398, 124], [1342, 124]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=056c0261-ff21-, 置信度=0.9813222885131836, 原始坐标=[1430, 98, 1524, 120], 转换后坐标=[[1430, 98], [1524, 98], [1524, 120], [1430, 120]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9516213536262512, 原始坐标=[1542, 94, 1630, 122], 转换后坐标=[[1542, 94], [1630, 94], [1630, 122], [1542, 122]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9775974750518799, 原始坐标=[1656, 96, 1730, 124], 转换后坐标=[[1656, 96], [1730, 96], [1730, 124], [1656, 124]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=4945-8358-de4, 置信度=0.9984042048454285, 原始坐标=[1428, 112, 1527, 140], 转换后坐标=[[1428, 112], [1527, 112], [1527, 140], [1428, 140]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=ead56f746.png, 置信度=0.9964826107025146, 原始坐标=[1426, 128, 1525, 156], 转换后坐标=[[1426, 128], [1525, 128], [1525, 156], [1426, 156]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9405160546302795, 原始坐标=[36, 246, 111, 279], 转换后坐标=[[36, 246], [111, 246], [111, 279], [36, 279]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9690349102020264, 原始坐标=[144, 244, 220, 281], 转换后坐标=[[144, 244], [220, 244], [220, 281], [144, 281]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9812813401222229, 原始坐标=[251, 246, 327, 279], 转换后坐标=[[251, 246], [327, 246], [327, 279], [251, 279]]
[15:32:49] [    INFO] [测试3.py:1478] - 转换完成，共转换21个结果
[15:32:49] [   DEBUG] [测试3.py:1610] - OCR结果格式转换完成: [[[[[50, 96], [99, 96], [99, 124], [50, 124]], ['1.jpeg', 0.9746143221855164]], [[[160, 98], [205, 98], [205, 123], [160, 123]], ['2.jpeg', 0.9788720607757568]], [[[266, 98], [315, 98], [315, 125], [266, 125]], ['3.jpeg', 0.9302626252174377]], [[[372, 96], [423, 96], [423, 126], [372, 126]], ['4.jpeg', 0.9823043942451477]], [[[477, 91], [534, 91], [534, 129], [477, 129]], ['5.jpeg', 0.9520460963249207]], [[[587, 96], [640, 96], [640, 125], [587, 125]], ['6.jpeg', 0.8875851631164551]], [[[698, 98], [747, 98], [747, 125], [698, 125]], ['7.jpeg', 0.941087543964386]], [[[802, 92], [858, 92], [858, 128], [802, 128]], ['8,jpeg', 0.9538896083831787]], [[[909, 92], [965, 92], [965, 128], [909, 128]], ['9,jpeg', 0.8853468298912048]], [[[1016, 92], [1076, 92], [1076, 128], [1016, 128]], ['10,jpeg', 0.9355347752571106]], [[[1123, 92], [1183, 92], [1183, 126], [1123, 126]], ['11.jpeg', 0.9877234101295471]], [[[1232, 92], [1291, 92], [1291, 126], [1232, 126]], ['12.jpeg', 0.992492139339447]], [[[1342, 96], [1398, 96], [1398, 124], [1342, 124]], ['13.jpeg', 0.9567533731460571]], [[[1430, 98], [1524, 98], [1524, 120], [1430, 120]], ['056c0261-ff21-', 0.9813222885131836]], [[[1542, 94], [1630, 94], [1630, 122], [1542, 122]], ['800x1200.jpg', 0.9516213536262512]], [[[1656, 96], [1730, 96], [1730, 124], [1656, 124]], ['主图1.jpeg', 0.9775974750518799]], [[[1428, 112], [1527, 112], [1527, 140], [1428, 140]], ['4945-8358-de4', 0.9984042048454285]], [[[1426, 128], [1525, 128], [1525, 156], [1426, 156]], ['ead56f746.png', 0.9964826107025146]], [[[36, 246], [111, 246], [111, 279], [36, 279]], ['主图2.jpeg', 0.9405160546302795]], [[[144, 244], [220, 244], [220, 281], [144, 281]], ['主图3.jpeg', 0.9690349102020264]], [[[251, 246], [327, 246], [327, 279], [251, 279]], ['主图4.jpeg', 0.9812813401222229]]]]
[15:32:49] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (234, 210), 置信度: 0.9746143221855164
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (342, 210), 置信度: 0.9788720607757568
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (450, 211), 置信度: 0.9302626252174377
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '4.jpeg', 位置: (557, 211), 置信度: 0.9823043942451477
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (665, 210), 置信度: 0.9520460963249207
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '6.jpeg', 位置: (773, 210), 置信度: 0.8875851631164551
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '7.jpeg', 位置: (882, 211), 置信度: 0.941087543964386
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '8,jpeg', 位置: (990, 210), 置信度: 0.9538896083831787
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8,jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8,jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '9,jpeg', 位置: (1097, 210), 置信度: 0.8853468298912048
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9,jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'9,jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '10,jpeg', 位置: (1206, 210), 置信度: 0.9355347752571106
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '10,jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'10,jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '11.jpeg', 位置: (1313, 209), 置信度: 0.9877234101295471
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '11.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'11.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '12.jpeg', 位置: (1421, 209), 置信度: 0.992492139339447
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '12.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'12.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '13.jpeg', 位置: (1530, 210), 置信度: 0.9567533731460571
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '13.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'13.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '056c0261-ff21-', 位置: (1637, 209), 置信度: 0.9813222885131836
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '056c0261-ff21-'
[15:32:49] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'056'在文本'056c0261-ff21-'中, 点击位置: (1637, 209)
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (1746, 208), 置信度: 0.9516213536262512
[15:32:49] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (1746, 208)
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1853, 210), 置信度: 0.9775974750518799
[15:32:49] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1853, 210)
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '4945-8358-de4', 位置: (1637, 226), 置信度: 0.9984042048454285
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4945-8358-de4'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4945-8358-de4'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: 'ead56f746.png', 位置: (1635, 242), 置信度: 0.9964826107025146
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'ead56f746.png'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'ead56f746.png'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (233, 362), 置信度: 0.9405160546302795
[15:32:49] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (233, 362)
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (342, 362), 置信度: 0.9690349102020264
[15:32:49] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (342, 362)
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (449, 362), 置信度: 0.9812813401222229
[15:32:49] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (449, 362)
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:722] - OCR识别统计:
[15:32:49] [    INFO] [测试3.py:723] - - 总文本块数: 21
[15:32:49] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 21
[15:32:49] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[15:32:49] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[15:32:49] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[15:32:49] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[15:32:49] [    INFO] [测试3.py:757] - 点击第1个位置: (1637, 209)
[15:32:50] [    INFO] [测试3.py:757] - 点击第2个位置: (1746, 208)
[15:32:50] [    INFO] [测试3.py:757] - 点击第3个位置: (1853, 210)
[15:32:51] [    INFO] [测试3.py:757] - 点击第4个位置: (233, 362)
[15:32:52] [    INFO] [测试3.py:757] - 点击第5个位置: (342, 362)
[15:32:52] [    INFO] [测试3.py:757] - 点击第6个位置: (449, 362)
[15:32:53] [    INFO] [测试3.py:764] - 释放Ctrl键
[15:32:53] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[15:32:53] [    INFO] [测试3.py:1339] - 点击打开按钮...
[15:32:53] [    INFO] [测试3.py:1343] - 等待图片加载到图片空间...
[15:32:54] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[15:32:55] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[15:32:56] [    INFO] [测试3.py:871] - 检测到'上传中'状态，位置: Box(left=571, top=846, width=55, height=19)，继续等待2秒...
[15:32:59] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[15:32:59] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[15:33:00] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[15:33:00] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[15:33:00] [    INFO] [测试3.py:1562] - 开始OCR识别图片: logs\debug_space_screenshot.png
[15:33:00] [    INFO] [测试3.py:1579] - 当前识别场景: main
[15:33:00] [   DEBUG] [测试3.py:1582] - 正在执行OCR识别...
[15:33:02] [   DEBUG] [测试3.py:1585] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[191, ..., 203],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[191, ..., 203],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[191, ..., 203],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[600,  17],
       ...,
       [600,  35]], dtype=int16), array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [290,  62]], dtype=int16), array([[429,  43],
       ...,
       [427,  61]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[295,  62],
       ...,
       [295,  77]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[564,  62],
       ...,
       [564,  77]], dtype=int16), array([[317,  93],
       ...,
       [317, 121]], dtype=int16), array([[470,  94],
       ...,
       [470, 123]], dtype=int16), array([[346, 194],
       ...,
       [346, 206]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[292, 215],
       ...,
       [291, 237]], dtype=int16), array([[423, 215],
       ...,
       [421, 235]], dtype=int16), array([[558, 216],
       ...,
       [557, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[608, 272],
       ...,
       [608, 288]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800x800', '800x800', '800×800', '800×1200', 'SA2H', 'BA2H', '56c0261-ff21-494...', '主图1.jpg', '主图1.jpg', '引主图2jpg', '引主图3.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9340793490409851, 0.9721274971961975, 0.9678918123245239, 0.9861000180244446, 0.9692646861076355, 0.9743536710739136, 0.9297652840614319, 0.9282951354980469, 0.9743536710739136, 0.9527221918106079, 0.5412565469741821, 0.5683974623680115, 0.9350449442863464, 0.9442705512046814, 0.9789379835128784, 0.9408549666404724, 0.9654053449630737, 0.9506109952926636, 0.9704387784004211, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.9202616810798645], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [290,  62]], dtype=int16), array([[429,  43],
       ...,
       [427,  61]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[295,  62],
       ...,
       [295,  77]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[564,  62],
       ...,
       [564,  77]], dtype=int16), array([[317,  93],
       ...,
       [317, 121]], dtype=int16), array([[470,  94],
       ...,
       [470, 123]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[292, 215],
       ...,
       [291, 237]], dtype=int16), array([[423, 215],
       ...,
       [421, 235]], dtype=int16), array([[558, 216],
       ...,
       [557, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [445, ..., 326]], dtype=int16)}]
[15:33:02] [    INFO] [测试3.py:1553] - OCR结果已保存到: logs\result_20250828_153302_main.txt 和 logs\result_20250828_153302_main.json
[15:33:02] [    INFO] [测试3.py:1428] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[191, ..., 203],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[191, ..., 203],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[191, ..., 203],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[600,  17],
       ...,
       [600,  35]], dtype=int16), array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [290,  62]], dtype=int16), array([[429,  43],
       ...,
       [427,  61]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[295,  62],
       ...,
       [295,  77]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[564,  62],
       ...,
       [564,  77]], dtype=int16), array([[317,  93],
       ...,
       [317, 121]], dtype=int16), array([[470,  94],
       ...,
       [470, 123]], dtype=int16), array([[346, 194],
       ...,
       [346, 206]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[292, 215],
       ...,
       [291, 237]], dtype=int16), array([[423, 215],
       ...,
       [421, 235]], dtype=int16), array([[558, 216],
       ...,
       [557, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[608, 272],
       ...,
       [608, 288]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800x800', '800x800', '800×800', '800×1200', 'SA2H', 'BA2H', '56c0261-ff21-494...', '主图1.jpg', '主图1.jpg', '引主图2jpg', '引主图3.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9340793490409851, 0.9721274971961975, 0.9678918123245239, 0.9861000180244446, 0.9692646861076355, 0.9743536710739136, 0.9297652840614319, 0.9282951354980469, 0.9743536710739136, 0.9527221918106079, 0.5412565469741821, 0.5683974623680115, 0.9350449442863464, 0.9442705512046814, 0.9789379835128784, 0.9408549666404724, 0.9654053449630737, 0.9506109952926636, 0.9704387784004211, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.9202616810798645], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [290,  62]], dtype=int16), array([[429,  43],
       ...,
       [427,  61]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[295,  62],
       ...,
       [295,  77]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[564,  62],
       ...,
       [564,  77]], dtype=int16), array([[317,  93],
       ...,
       [317, 121]], dtype=int16), array([[470,  94],
       ...,
       [470, 123]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[292, 215],
       ...,
       [291, 237]], dtype=int16), array([[423, 215],
       ...,
       [421, 235]], dtype=int16), array([[558, 216],
       ...,
       [557, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [445, ..., 326]], dtype=int16)}
[15:33:03] [    INFO] [测试3.py:1435] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[600, 17], [626, 17], [626, 35], [600, 35]], [[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 41], [351, 46], [349, 67], [290, 62]], [[429, 43], [485, 47], [484, 65], [427, 61]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[295, 62], [346, 62], [346, 77], [295, 77]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[564, 62], [620, 62], [620, 77], [564, 77]], [[317, 93], [330, 93], [330, 121], [317, 121]], [[470, 94], [483, 94], [483, 123], [470, 123]], [[346, 194], [366, 194], [366, 206], [346, 206]], [[0, 221], [103, 221], [103, 235], [0, 235]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[292, 215], [350, 219], [349, 240], [291, 237]], [[423, 215], [493, 220], [492, 239], [421, 235]], [[558, 216], [628, 220], [627, 239], [557, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[608, 272], [619, 272], [619, 288], [608, 288]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800x800', '800x800', '800×800', '800×1200', 'SA2H', 'BA2H', '56c0261-ff21-494...', '主图1.jpg', '主图1.jpg', '引主图2jpg', '引主图3.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9340793490409851, 0.9721274971961975, 0.9678918123245239, 0.9861000180244446, 0.9692646861076355, 0.9743536710739136, 0.9297652840614319, 0.9282951354980469, 0.9743536710739136, 0.9527221918106079, 0.5412565469741821, 0.5683974623680115, 0.9350449442863464, 0.9442705512046814, 0.9789379835128784, 0.9408549666404724, 0.9654053449630737, 0.9506109952926636, 0.9704387784004211, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.9202616810798645], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 41], [351, 46], [349, 67], [290, 62]], [[429, 43], [485, 47], [484, 65], [427, 61]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[295, 62], [346, 62], [346, 77], [295, 77]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[564, 62], [620, 62], [620, 77], [564, 77]], [[317, 93], [330, 93], [330, 121], [317, 121]], [[470, 94], [483, 94], [483, 123], [470, 123]], [[0, 221], [103, 221], [103, 235], [0, 235]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[292, 215], [350, 219], [349, 240], [291, 237]], [[423, 215], [493, 220], [492, 239], [421, 235]], [[558, 216], [628, 220], [627, 239], [557, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'rec_boxes': [[18, 41, 78, 67], [155, 41, 214, 67], [290, 41, 351, 67], [427, 43, 485, 65], [553, 43, 631, 65], [24, 64, 73, 76], [159, 62, 209, 77], [295, 62, 346, 77], [432, 64, 481, 76], [564, 62, 620, 77], [317, 93, 330, 121], [470, 94, 483, 123], [0, 221, 103, 235], [162, 217, 221, 238], [291, 215, 350, 240], [421, 215, 493, 239], [557, 216, 628, 239], [24, 236, 74, 251], [161, 242, 208, 254], [296, 236, 346, 251], [432, 240, 482, 255], [567, 240, 617, 255], [445, 308, 604, 326]]}}
[15:33:03] [    INFO] [测试3.py:1444] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[600, 17], [626, 17], [626, 35], [600, 35]], [[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 41], [351, 46], [349, 67], [290, 62]], [[429, 43], [485, 47], [484, 65], [427, 61]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[295, 62], [346, 62], [346, 77], [295, 77]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[564, 62], [620, 62], [620, 77], [564, 77]], [[317, 93], [330, 93], [330, 121], [317, 121]], [[470, 94], [483, 94], [483, 123], [470, 123]], [[346, 194], [366, 194], [366, 206], [346, 206]], [[0, 221], [103, 221], [103, 235], [0, 235]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[292, 215], [350, 219], [349, 240], [291, 237]], [[423, 215], [493, 220], [492, 239], [421, 235]], [[558, 216], [628, 220], [627, 239], [557, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[608, 272], [619, 272], [619, 288], [608, 288]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800x800', '800x800', '800×800', '800×1200', 'SA2H', 'BA2H', '56c0261-ff21-494...', '主图1.jpg', '主图1.jpg', '引主图2jpg', '引主图3.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9340793490409851, 0.9721274971961975, 0.9678918123245239, 0.9861000180244446, 0.9692646861076355, 0.9743536710739136, 0.9297652840614319, 0.9282951354980469, 0.9743536710739136, 0.9527221918106079, 0.5412565469741821, 0.5683974623680115, 0.9350449442863464, 0.9442705512046814, 0.9789379835128784, 0.9408549666404724, 0.9654053449630737, 0.9506109952926636, 0.9704387784004211, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.9202616810798645], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 41], [351, 46], [349, 67], [290, 62]], [[429, 43], [485, 47], [484, 65], [427, 61]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[295, 62], [346, 62], [346, 77], [295, 77]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[564, 62], [620, 62], [620, 77], [564, 77]], [[317, 93], [330, 93], [330, 121], [317, 121]], [[470, 94], [483, 94], [483, 123], [470, 123]], [[0, 221], [103, 221], [103, 235], [0, 235]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[292, 215], [350, 219], [349, 240], [291, 237]], [[423, 215], [493, 220], [492, 239], [421, 235]], [[558, 216], [628, 220], [627, 239], [557, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'rec_boxes': [[18, 41, 78, 67], [155, 41, 214, 67], [290, 41, 351, 67], [427, 43, 485, 65], [553, 43, 631, 65], [24, 64, 73, 76], [159, 62, 209, 77], [295, 62, 346, 77], [432, 64, 481, 76], [564, 62, 620, 77], [317, 93, 330, 121], [470, 94, 483, 123], [0, 221, 103, 235], [162, 217, 221, 238], [291, 215, 350, 240], [421, 215, 493, 239], [557, 216, 628, 239], [24, 236, 74, 251], [161, 242, 208, 254], [296, 236, 346, 251], [432, 240, 482, 255], [567, 240, 617, 255], [445, 308, 604, 326]]}
[15:33:03] [    INFO] [测试3.py:1452] - 识别到的文本: ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800x800', '800x800', '800×800', '800×1200', 'SA2H', 'BA2H', '56c0261-ff21-494...', '主图1.jpg', '主图1.jpg', '引主图2jpg', '引主图3.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：1:1智能裁剪']
[15:33:03] [    INFO] [测试3.py:1453] - 识别的置信度: [0.9340793490409851, 0.9721274971961975, 0.9678918123245239, 0.9861000180244446, 0.9692646861076355, 0.9743536710739136, 0.9297652840614319, 0.9282951354980469, 0.9743536710739136, 0.9527221918106079, 0.5412565469741821, 0.5683974623680115, 0.9350449442863464, 0.9442705512046814, 0.9789379835128784, 0.9408549666404724, 0.9654053449630737, 0.9506109952926636, 0.9704387784004211, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.9202616810798645]
[15:33:03] [    INFO] [测试3.py:1454] - 识别的坐标框: [[18, 41, 78, 67], [155, 41, 214, 67], [290, 41, 351, 67], [427, 43, 485, 65], [553, 43, 631, 65], [24, 64, 73, 76], [159, 62, 209, 77], [295, 62, 346, 77], [432, 64, 481, 76], [564, 62, 620, 77], [317, 93, 330, 121], [470, 94, 483, 123], [0, 221, 103, 235], [162, 217, 221, 238], [291, 215, 350, 240], [421, 215, 493, 239], [557, 216, 628, 239], [24, 236, 74, 251], [161, 242, 208, 254], [296, 236, 346, 251], [432, 240, 482, 255], [567, 240, 617, 255], [445, 308, 604, 326]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9340793490409851, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9721274971961975, 原始坐标=[155, 41, 214, 67], 转换后坐标=[[155, 41], [214, 41], [214, 67], [155, 67]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9678918123245239, 原始坐标=[290, 41, 351, 67], 转换后坐标=[[290, 41], [351, 41], [351, 67], [290, 67]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9861000180244446, 原始坐标=[427, 43, 485, 65], 转换后坐标=[[427, 43], [485, 43], [485, 65], [427, 65]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9692646861076355, 原始坐标=[553, 43, 631, 65], 转换后坐标=[[553, 43], [631, 43], [631, 65], [553, 65]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9743536710739136, 原始坐标=[24, 64, 73, 76], 转换后坐标=[[24, 64], [73, 64], [73, 76], [24, 76]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800x800, 置信度=0.9297652840614319, 原始坐标=[159, 62, 209, 77], 转换后坐标=[[159, 62], [209, 62], [209, 77], [159, 77]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800x800, 置信度=0.9282951354980469, 原始坐标=[295, 62, 346, 77], 转换后坐标=[[295, 62], [346, 62], [346, 77], [295, 77]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9743536710739136, 原始坐标=[432, 64, 481, 76], 转换后坐标=[[432, 64], [481, 64], [481, 76], [432, 76]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×1200, 置信度=0.9527221918106079, 原始坐标=[564, 62, 620, 77], 转换后坐标=[[564, 62], [620, 62], [620, 77], [564, 77]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=SA2H, 置信度=0.5412565469741821, 原始坐标=[317, 93, 330, 121], 转换后坐标=[[317, 93], [330, 93], [330, 121], [317, 121]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=BA2H, 置信度=0.5683974623680115, 原始坐标=[470, 94, 483, 123], 转换后坐标=[[470, 94], [483, 94], [483, 123], [470, 123]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=56c0261-ff21-494..., 置信度=0.9350449442863464, 原始坐标=[0, 221, 103, 235], 转换后坐标=[[0, 221], [103, 221], [103, 235], [0, 235]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9442705512046814, 原始坐标=[162, 217, 221, 238], 转换后坐标=[[162, 217], [221, 217], [221, 238], [162, 238]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9789379835128784, 原始坐标=[291, 215, 350, 240], 转换后坐标=[[291, 215], [350, 215], [350, 240], [291, 240]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=引主图2jpg, 置信度=0.9408549666404724, 原始坐标=[421, 215, 493, 239], 转换后坐标=[[421, 215], [493, 215], [493, 239], [421, 239]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=引主图3.jpg, 置信度=0.9654053449630737, 原始坐标=[557, 216, 628, 239], 转换后坐标=[[557, 216], [628, 216], [628, 239], [557, 239]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[24, 236, 74, 251], 转换后坐标=[[24, 236], [74, 236], [74, 251], [24, 251]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9704387784004211, 原始坐标=[161, 242, 208, 254], 转换后坐标=[[161, 242], [208, 242], [208, 254], [161, 254]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[296, 236, 346, 251], 转换后坐标=[[296, 236], [346, 236], [346, 251], [296, 251]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[432, 240, 482, 255], 转换后坐标=[[432, 240], [482, 240], [482, 255], [432, 255]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800x800, 置信度=0.9297652840614319, 原始坐标=[567, 240, 617, 255], 转换后坐标=[[567, 240], [617, 240], [617, 255], [567, 255]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=①裁剪宽高比：1:1智能裁剪, 置信度=0.9202616810798645, 原始坐标=[445, 308, 604, 326], 转换后坐标=[[445, 308], [604, 308], [604, 326], [445, 326]]
[15:33:03] [    INFO] [测试3.py:1478] - 转换完成，共转换23个结果
[15:33:03] [   DEBUG] [测试3.py:1610] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图4.jpg', 0.9340793490409851]], [[[155, 41], [214, 41], [214, 67], [155, 67]], ['主图3.jpg', 0.9721274971961975]], [[[290, 41], [351, 41], [351, 67], [290, 67]], ['主图2.jpg', 0.9678918123245239]], [[[427, 43], [485, 43], [485, 65], [427, 65]], ['主图1.jpg', 0.9861000180244446]], [[[553, 43], [631, 43], [631, 65], [553, 65]], ['800x1200.jpg', 0.9692646861076355]], [[[24, 64], [73, 64], [73, 76], [24, 76]], ['800×800', 0.9743536710739136]], [[[159, 62], [209, 62], [209, 77], [159, 77]], ['800x800', 0.9297652840614319]], [[[295, 62], [346, 62], [346, 77], [295, 77]], ['800x800', 0.9282951354980469]], [[[432, 64], [481, 64], [481, 76], [432, 76]], ['800×800', 0.9743536710739136]], [[[564, 62], [620, 62], [620, 77], [564, 77]], ['800×1200', 0.9527221918106079]], [[[317, 93], [330, 93], [330, 121], [317, 121]], ['SA2H', 0.5412565469741821]], [[[470, 94], [483, 94], [483, 123], [470, 123]], ['BA2H', 0.5683974623680115]], [[[0, 221], [103, 221], [103, 235], [0, 235]], ['56c0261-ff21-494...', 0.9350449442863464]], [[[162, 217], [221, 217], [221, 238], [162, 238]], ['主图1.jpg', 0.9442705512046814]], [[[291, 215], [350, 215], [350, 240], [291, 240]], ['主图1.jpg', 0.9789379835128784]], [[[421, 215], [493, 215], [493, 239], [421, 239]], ['引主图2jpg', 0.9408549666404724]], [[[557, 216], [628, 216], [628, 239], [557, 239]], ['引主图3.jpg', 0.9654053449630737]], [[[24, 236], [74, 236], [74, 251], [24, 251]], ['800×800', 0.9506109952926636]], [[[161, 242], [208, 242], [208, 254], [161, 254]], ['800×800', 0.9704387784004211]], [[[296, 236], [346, 236], [346, 251], [296, 251]], ['800×800', 0.9506109952926636]], [[[432, 240], [482, 240], [482, 255], [432, 255]], ['800×800', 0.9506109952926636]], [[[567, 240], [617, 240], [617, 255], [567, 255]], ['800x800', 0.9297652840614319]], [[[445, 308], [604, 308], [604, 326], [445, 326]], ['①裁剪宽高比：1:1智能裁剪', 0.9202616810798645]]]]
[15:33:03] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9340793490409851
[15:33:03] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (864, 604), 置信度: 0.9721274971961975
[15:33:03] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1000, 604), 置信度: 0.9678918123245239
[15:33:03] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1136, 604), 置信度: 0.9861000180244446
[15:33:03] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (871, 777), 置信度: 0.9442705512046814
[15:33:03] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1000, 777), 置信度: 0.9789379835128784
[15:33:03] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1136, 534)
[15:33:04] [    INFO] [测试3.py:978] - 主图1已经点击过，跳过
[15:33:04] [    INFO] [测试3.py:978] - 主图1已经点击过，跳过
[15:33:04] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1000, 534)
[15:33:05] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (864, 534)
[15:33:06] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[15:33:08] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[15:33:08] [    INFO] [测试3.py:1371] - 点击右侧空白处关闭图片空间...
[15:33:09] [    INFO] [测试3.py:1376] - 点击裁剪按钮...
[15:33:10] [    INFO] [测试3.py:1383] - 开始上传UUID透明图...
[15:33:11] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[15:33:12] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[15:33:16] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[15:33:16] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[15:33:16] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250828_153316.png
[15:33:16] [    INFO] [测试3.py:1094] - 目标UUID: 056c0261-ff21-4945-8358-de4ead56f746.png
[15:33:16] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['056', '56c', '6c0', 'c02', '026', '261']
[15:33:16] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[15:33:18] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250828_153316.json
[15:33:18] [    INFO] [测试3.py:1120] - 识别到 26 个文本块
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (61, 97), 置信度: 0.9657
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (170, 97), 置信度: 0.9614
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (281, 96), 置信度: 0.9726
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (391, 96), 置信度: 0.9801
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (501, 96), 置信度: 0.9845
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (610, 96), 置信度: 0.9594
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (36, 122), 置信度: 0.9218
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (146, 123), 置信度: 0.9415
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (256, 123), 置信度: 0.9676
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (366, 123), 置信度: 0.9906
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200.jpg, 位置: (488, 122), 置信度: 0.9642
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 056c0261-ff21-., 位置: (609, 123), 置信度: 0.9878
[15:33:18] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'056'在文本'056c0261-ff21-.'中
[15:33:18] [    INFO] [测试3.py:1139] - 计算的点击位置: (1379, 623)
[15:33:18] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250828_153316
[15:33:20] [    INFO] [测试3.py:1400] - 开始上传800x1200图片...
[15:33:21] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[15:33:22] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[15:33:25] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[15:33:25] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[15:33:26] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250828_153326.png
[15:33:26] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[15:33:28] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250828_153326.json
[15:33:28] [    INFO] [测试3.py:1233] - 识别到 27 个文本块
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (63, 100), 置信度: 0.9631
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (172, 100), 置信度: 0.9795
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (283, 100), 置信度: 0.9797
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (392, 100), 置信度: 0.9710
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (502, 100), 置信度: 0.9689
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (612, 100), 置信度: 0.9778
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 主图3.jpg, 位置: (38, 127), 置信度: 0.9381
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 主图4jpg, 位置: (147, 126), 置信度: 0.9884
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 主图3.jpg, 位置: (258, 126), 置信度: 0.9227
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 主图4.jpg, 位置: (368, 127), 置信度: 0.9390
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 主图1.jpg, 位置: (478, 126), 置信度: 0.9500
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 主图2.jpg, 位置: (588, 126), 置信度: 0.9827
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: A2H, 位置: (395, 166), 置信度: 0.7189
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: SA2H, 位置: (518, 165), 置信度: 0.7475
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 3A2, 位置: (617, 165), 置信度: 0.6437
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (63, 242), 置信度: 0.9571
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (174, 243), 置信度: 0.9882
[15:33:28] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[15:33:28] [    INFO] [测试3.py:1251] - 计算的点击位置: (804, 739)
[15:33:28] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250828_153326
[15:33:30] [    INFO] [测试3.py:1413] - 向下滚动页面...
[15:33:31] [    INFO] [测试3.py:1417] - 文件选择完成，上传流程结束
[15:33:31] [    INFO] [测试3.py:1652] - 已创建测试3完成信号文件
