[14:54:45] [    INFO] [测试3.py:80] - ==================================================
[14:54:45] [    INFO] [测试3.py:81] - 日志系统初始化完成
[14:54:45] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250828.log
[14:54:45] [    INFO] [测试3.py:83] - ==================================================
[14:54:45] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[14:54:45] [    INFO] [测试3.py:113] - pyautogui设置完成
[14:54:45] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[14:54:50] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[14:54:50] [    INFO] [测试3.py:169] - 成功加载图片配置:
[14:54:50] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[14:54:50] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[14:54:50] [    INFO] [测试3.py:172] - UUID图片: 056c0261-ff21-4945-8358-de4ead56f746.png
[14:54:50] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 1
[14:54:50] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[14:54:50] [    INFO] [测试3.py:1274] - 开始上传流程...
[14:54:51] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[14:54:51] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[14:54:52] [    INFO] [测试3.py:1289] - 准备点击坐标...
[14:54:52] [    INFO] [测试3.py:1291] - 已点击坐标
[14:54:56] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[14:54:56] [ WARNING] [测试3.py:1309] - 未找到SHANGCHUANG.png，等待1.2秒后重试
[14:54:57] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[14:54:57] [   ERROR] [测试3.py:1421] - 上传流程出错: 重试后仍未找到上传按钮
[14:54:57] [    INFO] [测试3.py:1652] - 已创建测试3完成信号文件
[14:56:53] [    INFO] [测试3.py:80] - ==================================================
[14:56:53] [    INFO] [测试3.py:81] - 日志系统初始化完成
[14:56:53] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250828.log
[14:56:53] [    INFO] [测试3.py:83] - ==================================================
[14:56:53] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[14:56:53] [    INFO] [测试3.py:113] - pyautogui设置完成
[14:56:53] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[14:56:58] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[14:56:58] [    INFO] [测试3.py:169] - 成功加载图片配置:
[14:56:58] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[14:56:58] [    INFO] [测试3.py:171] - 详情图: ['10.jpeg', '11.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[14:56:58] [    INFO] [测试3.py:172] - UUID图片: f24a93b2-ed84-43a4-a1d5-2b475b112391.png
[14:56:58] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 2
[14:56:58] [    INFO] [测试3.py:213] - 初始鼠标位置: (1600, 510)
[14:56:58] [    INFO] [测试3.py:1274] - 开始上传流程...
[14:56:59] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[14:56:59] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[14:57:00] [    INFO] [测试3.py:1289] - 准备点击坐标...
[14:57:00] [    INFO] [测试3.py:1291] - 已点击坐标
[14:57:02] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[14:57:02] [ WARNING] [测试3.py:1297] - 未找到BENDISHANGCHUANG.png，使用备用方案
[14:57:04] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[14:57:04] [   ERROR] [测试3.py:1421] - 上传流程出错: 重试后仍未找到本地上传按钮
[14:57:04] [    INFO] [测试3.py:1652] - 已创建测试3完成信号文件
[14:59:01] [    INFO] [测试3.py:80] - ==================================================
[14:59:01] [    INFO] [测试3.py:81] - 日志系统初始化完成
[14:59:01] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250828.log
[14:59:01] [    INFO] [测试3.py:83] - ==================================================
[14:59:01] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[14:59:01] [    INFO] [测试3.py:113] - pyautogui设置完成
[14:59:01] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[14:59:06] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[14:59:06] [    INFO] [测试3.py:169] - 成功加载图片配置:
[14:59:06] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[14:59:06] [    INFO] [测试3.py:171] - 详情图: ['10.jpeg', '11.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[14:59:06] [    INFO] [测试3.py:172] - UUID图片: e7bbaed6-54de-4040-8765-dbedd2ff03bf.png
[14:59:06] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 3
[14:59:06] [    INFO] [测试3.py:213] - 初始鼠标位置: (1600, 510)
[14:59:06] [    INFO] [测试3.py:1274] - 开始上传流程...
[14:59:07] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[14:59:07] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[14:59:07] [    INFO] [测试3.py:1289] - 准备点击坐标...
[14:59:08] [    INFO] [测试3.py:1291] - 已点击坐标
[14:59:10] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[14:59:10] [ WARNING] [测试3.py:1297] - 未找到BENDISHANGCHUANG.png，使用备用方案
[14:59:12] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[14:59:12] [   ERROR] [测试3.py:1421] - 上传流程出错: 重试后仍未找到本地上传按钮
[14:59:12] [    INFO] [测试3.py:1652] - 已创建测试3完成信号文件
[15:01:10] [    INFO] [测试3.py:80] - ==================================================
[15:01:10] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:01:10] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250828.log
[15:01:10] [    INFO] [测试3.py:83] - ==================================================
[15:01:10] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:01:10] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:01:10] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:01:15] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:01:15] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:01:15] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:01:15] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '2.jpeg', '3.jpeg']
[15:01:15] [    INFO] [测试3.py:172] - UUID图片: 1103304c-66b0-4e04-a510-3ac372fe9ea2.png
[15:01:15] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 4
[15:01:15] [    INFO] [测试3.py:213] - 初始鼠标位置: (1600, 510)
[15:01:15] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:01:16] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:01:16] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:01:17] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:01:17] [    INFO] [测试3.py:1291] - 已点击坐标
[15:01:19] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:01:19] [ WARNING] [测试3.py:1297] - 未找到BENDISHANGCHUANG.png，使用备用方案
[15:01:21] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:01:21] [   ERROR] [测试3.py:1421] - 上传流程出错: 重试后仍未找到本地上传按钮
[15:01:21] [    INFO] [测试3.py:1652] - 已创建测试3完成信号文件
[15:03:16] [    INFO] [测试3.py:80] - ==================================================
[15:03:16] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:03:16] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250828.log
[15:03:16] [    INFO] [测试3.py:83] - ==================================================
[15:03:16] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:03:16] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:03:16] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:03:22] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:03:22] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:03:22] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:03:22] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg']
[15:03:22] [    INFO] [测试3.py:172] - UUID图片: 85ebbb0b-b693-48c1-9443-b8c8cb62c591.png
[15:03:22] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 5
[15:03:22] [    INFO] [测试3.py:213] - 初始鼠标位置: (1600, 510)
[15:03:22] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:03:22] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:03:23] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:03:23] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:03:23] [    INFO] [测试3.py:1291] - 已点击坐标
[15:03:25] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:03:25] [ WARNING] [测试3.py:1297] - 未找到BENDISHANGCHUANG.png，使用备用方案
[15:03:28] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:03:28] [   ERROR] [测试3.py:1421] - 上传流程出错: 重试后仍未找到本地上传按钮
[15:03:28] [    INFO] [测试3.py:1652] - 已创建测试3完成信号文件
[15:06:11] [    INFO] [测试3.py:80] - ==================================================
[15:06:11] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:06:11] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250828.log
[15:06:11] [    INFO] [测试3.py:83] - ==================================================
[15:06:11] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:06:11] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:06:11] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:06:17] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:06:17] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:06:17] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:06:17] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[15:06:17] [    INFO] [测试3.py:172] - UUID图片: 056c0261-ff21-4945-8358-de4ead56f746.png
[15:06:17] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 1
[15:06:17] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:06:17] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:06:17] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:06:18] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:06:18] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:06:18] [    INFO] [测试3.py:1291] - 已点击坐标
[15:06:22] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:06:22] [ WARNING] [测试3.py:1309] - 未找到SHANGCHUANG.png，等待1.2秒后重试
[15:06:23] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:06:23] [   ERROR] [测试3.py:1421] - 上传流程出错: 重试后仍未找到上传按钮
[15:06:23] [    INFO] [测试3.py:1652] - 已创建测试3完成信号文件
[15:10:19] [    INFO] [测试3.py:80] - ==================================================
[15:10:19] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:10:19] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250828.log
[15:10:19] [    INFO] [测试3.py:83] - ==================================================
[15:10:19] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:10:19] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:10:19] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:10:24] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:10:24] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:10:24] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:10:24] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[15:10:24] [    INFO] [测试3.py:172] - UUID图片: 056c0261-ff21-4945-8358-de4ead56f746.png
[15:10:24] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 1
[15:10:24] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:10:24] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:10:25] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:10:25] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:10:26] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:10:26] [    INFO] [测试3.py:1291] - 已点击坐标
[15:10:30] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:10:30] [ WARNING] [测试3.py:1309] - 未找到SHANGCHUANG.png，等待1.2秒后重试
[15:10:31] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:10:31] [   ERROR] [测试3.py:1421] - 上传流程出错: 重试后仍未找到上传按钮
[15:10:31] [    INFO] [测试3.py:1652] - 已创建测试3完成信号文件
[15:13:38] [    INFO] [测试3.py:80] - ==================================================
[15:13:38] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:13:38] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250828.log
[15:13:38] [    INFO] [测试3.py:83] - ==================================================
[15:13:38] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:13:38] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:13:38] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:13:44] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:13:44] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:13:44] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:13:44] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[15:13:44] [    INFO] [测试3.py:172] - UUID图片: 056c0261-ff21-4945-8358-de4ead56f746.png
[15:13:44] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 1
[15:13:44] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:13:44] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:13:44] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:13:44] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:13:45] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:13:45] [    INFO] [测试3.py:1291] - 已点击坐标
[15:13:49] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:13:49] [ WARNING] [测试3.py:1309] - 未找到SHANGCHUANG.png，等待1.2秒后重试
[15:13:50] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:13:50] [   ERROR] [测试3.py:1421] - 上传流程出错: 重试后仍未找到上传按钮
[15:13:50] [    INFO] [测试3.py:1652] - 已创建测试3完成信号文件
[15:18:42] [    INFO] [测试3.py:80] - ==================================================
[15:18:42] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:18:42] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250828.log
[15:18:42] [    INFO] [测试3.py:83] - ==================================================
[15:18:42] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:18:42] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:18:42] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:18:48] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:18:48] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:18:48] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:18:48] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[15:18:48] [    INFO] [测试3.py:172] - UUID图片: 056c0261-ff21-4945-8358-de4ead56f746.png
[15:18:48] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 1
[15:18:48] [    INFO] [测试3.py:213] - 初始鼠标位置: (1748, 406)
[15:18:48] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:18:48] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:18:49] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:18:49] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:18:49] [    INFO] [测试3.py:1291] - 已点击坐标
[15:18:53] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:18:53] [ WARNING] [测试3.py:1309] - 未找到SHANGCHUANG.png，等待1.2秒后重试
[15:18:54] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:18:54] [   ERROR] [测试3.py:1421] - 上传流程出错: 重试后仍未找到上传按钮
[15:18:54] [    INFO] [测试3.py:1652] - 已创建测试3完成信号文件
[15:22:21] [    INFO] [测试3.py:80] - ==================================================
[15:22:21] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:22:21] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250828.log
[15:22:21] [    INFO] [测试3.py:83] - ==================================================
[15:22:21] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:22:21] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:22:21] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:22:26] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:22:26] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:22:26] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:22:26] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[15:22:26] [    INFO] [测试3.py:172] - UUID图片: 056c0261-ff21-4945-8358-de4ead56f746.png
[15:22:26] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 1
[15:22:26] [    INFO] [测试3.py:213] - 初始鼠标位置: (1695, 380)
[15:22:26] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:22:27] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:22:27] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:22:28] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:22:28] [    INFO] [测试3.py:1291] - 已点击坐标
[15:22:32] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:22:32] [ WARNING] [测试3.py:1309] - 未找到SHANGCHUANG.png，等待1.2秒后重试
[15:22:33] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:22:33] [   ERROR] [测试3.py:1421] - 上传流程出错: 重试后仍未找到上传按钮
[15:22:33] [    INFO] [测试3.py:1652] - 已创建测试3完成信号文件
