[15:32:22] [    INFO] [测试3.py:80] - ==================================================
[15:32:22] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:32:22] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250828.log
[15:32:22] [    INFO] [测试3.py:83] - ==================================================
[15:32:22] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:32:22] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:32:22] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:32:28] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:32:28] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:32:28] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:32:28] [    INFO] [测试3.py:171] - 详情图: ['1.jpeg', '10.jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[15:32:28] [    INFO] [测试3.py:172] - UUID图片: 056c0261-ff21-4945-8358-de4ead56f746.png
[15:32:28] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 1
[15:32:28] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:32:28] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:32:29] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:32:29] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:32:29] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:32:29] [    INFO] [测试3.py:1291] - 已点击坐标
[15:32:33] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:32:33] [ WARNING] [测试3.py:1309] - 未找到SHANGCHUANG.png，等待1.2秒后重试
[15:32:35] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:32:35] [ WARNING] [测试3.py:1312] - 重试后仍未找到上传按钮，继续执行后续步骤
[15:32:38] [    INFO] [测试3.py:1324] - 开始查找1号文件夹...
[15:32:38] [    INFO] [测试3.py:384] - 开始查找文件夹: 1
[15:32:39] [    INFO] [测试3.py:416] - 文件夹1的目标坐标: (209, 140)
[15:32:39] [    INFO] [测试3.py:421] - 当前鼠标位置: (93, 199)
[15:32:39] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 140)
[15:32:39] [    INFO] [测试3.py:451] - 执行点击 #1
[15:32:40] [    INFO] [测试3.py:451] - 执行点击 #2
[15:32:41] [    INFO] [测试3.py:457] - 等待文件夹打开...
[15:32:41] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\1
[15:32:41] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[15:32:42] [    INFO] [测试3.py:1329] - 移动鼠标到(108,600)位置...
[15:32:43] [    INFO] [测试3.py:635] - 开始选择图片...
[15:32:45] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[15:32:45] [    INFO] [测试3.py:655] - 目标UUID: 056c0261-ff21-4945-8358-de4ead56f746.png
[15:32:45] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['056', '56c', '6c0', 'c02', '026', '261']
[15:32:45] [    INFO] [测试3.py:1562] - 开始OCR识别图片: logs\debug_files_screenshot.png
[15:32:47] [    INFO] [测试3.py:1579] - 当前识别场景: unknown
[15:32:47] [   DEBUG] [测试3.py:1582] - 正在执行OCR识别...
[15:32:49] [   DEBUG] [测试3.py:1585] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 53,  96],
       ...,
       [ 50, 119]], dtype=int16), array([[160,  98],
       ...,
       [160, 123]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  91],
       ...,
       [477, 122]], dtype=int16), array([[587,  96],
       ...,
       [587, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[805,  92],
       ...,
       [802, 122]], dtype=int16), array([[912,  92],
       ...,
       [909, 122]], dtype=int16), array([[1019,   92],
       ...,
       [1016,  122]], dtype=int16), array([[1126,   92],
       ...,
       [1123,  120]], dtype=int16), array([[1234,   92],
       ...,
       [1232,  120]], dtype=int16), array([[1345,   96],
       ...,
       [1342,  119]], dtype=int16), array([[1430,   98],
       ...,
       [1430,  120]], dtype=int16), array([[1543,   94],
       ...,
       [1542,  117]], dtype=int16), array([[1658,   96],
       ...,
       [1656,  119]], dtype=int16), array([[1429,  112],
       ...,
       [1428,  135]], dtype=int16), array([[1427,  128],
       ...,
       [1426,  151]], dtype=int16), array([[ 38, 246],
       ...,
       [ 36, 274]], dtype=int16), array([[147, 244],
       ...,
       [144, 274]], dtype=int16), array([[253, 246],
       ...,
       [251, 274]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9,jpeg', '10,jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '056c0261-ff21-', '800x1200.jpg', '主图1.jpeg', '4945-8358-de4', 'ead56f746.png', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg'], 'rec_scores': [0.9746143221855164, 0.9788720607757568, 0.9302626252174377, 0.9823043942451477, 0.9520460963249207, 0.8875851631164551, 0.941087543964386, 0.9538896083831787, 0.8853468298912048, 0.9355347752571106, 0.9877234101295471, 0.992492139339447, 0.9567533731460571, 0.9813222885131836, 0.9516213536262512, 0.9775974750518799, 0.9984042048454285, 0.9964826107025146, 0.9405160546302795, 0.9690349102020264, 0.9812813401222229], 'rec_polys': [array([[ 53,  96],
       ...,
       [ 50, 119]], dtype=int16), array([[160,  98],
       ...,
       [160, 123]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  91],
       ...,
       [477, 122]], dtype=int16), array([[587,  96],
       ...,
       [587, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[805,  92],
       ...,
       [802, 122]], dtype=int16), array([[912,  92],
       ...,
       [909, 122]], dtype=int16), array([[1019,   92],
       ...,
       [1016,  122]], dtype=int16), array([[1126,   92],
       ...,
       [1123,  120]], dtype=int16), array([[1234,   92],
       ...,
       [1232,  120]], dtype=int16), array([[1345,   96],
       ...,
       [1342,  119]], dtype=int16), array([[1430,   98],
       ...,
       [1430,  120]], dtype=int16), array([[1543,   94],
       ...,
       [1542,  117]], dtype=int16), array([[1658,   96],
       ...,
       [1656,  119]], dtype=int16), array([[1429,  112],
       ...,
       [1428,  135]], dtype=int16), array([[1427,  128],
       ...,
       [1426,  151]], dtype=int16), array([[ 38, 246],
       ...,
       [ 36, 274]], dtype=int16), array([[147, 244],
       ...,
       [144, 274]], dtype=int16), array([[253, 246],
       ...,
       [251, 274]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 50, ..., 124],
       ...,
       [251, ..., 279]], dtype=int16)}]
[15:32:49] [    INFO] [测试3.py:1553] - OCR结果已保存到: logs\result_20250828_153249_unknown.txt 和 logs\result_20250828_153249_unknown.json
[15:32:49] [    INFO] [测试3.py:1428] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 53,  96],
       ...,
       [ 50, 119]], dtype=int16), array([[160,  98],
       ...,
       [160, 123]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  91],
       ...,
       [477, 122]], dtype=int16), array([[587,  96],
       ...,
       [587, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[805,  92],
       ...,
       [802, 122]], dtype=int16), array([[912,  92],
       ...,
       [909, 122]], dtype=int16), array([[1019,   92],
       ...,
       [1016,  122]], dtype=int16), array([[1126,   92],
       ...,
       [1123,  120]], dtype=int16), array([[1234,   92],
       ...,
       [1232,  120]], dtype=int16), array([[1345,   96],
       ...,
       [1342,  119]], dtype=int16), array([[1430,   98],
       ...,
       [1430,  120]], dtype=int16), array([[1543,   94],
       ...,
       [1542,  117]], dtype=int16), array([[1658,   96],
       ...,
       [1656,  119]], dtype=int16), array([[1429,  112],
       ...,
       [1428,  135]], dtype=int16), array([[1427,  128],
       ...,
       [1426,  151]], dtype=int16), array([[ 38, 246],
       ...,
       [ 36, 274]], dtype=int16), array([[147, 244],
       ...,
       [144, 274]], dtype=int16), array([[253, 246],
       ...,
       [251, 274]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9,jpeg', '10,jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '056c0261-ff21-', '800x1200.jpg', '主图1.jpeg', '4945-8358-de4', 'ead56f746.png', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg'], 'rec_scores': [0.9746143221855164, 0.9788720607757568, 0.9302626252174377, 0.9823043942451477, 0.9520460963249207, 0.8875851631164551, 0.941087543964386, 0.9538896083831787, 0.8853468298912048, 0.9355347752571106, 0.9877234101295471, 0.992492139339447, 0.9567533731460571, 0.9813222885131836, 0.9516213536262512, 0.9775974750518799, 0.9984042048454285, 0.9964826107025146, 0.9405160546302795, 0.9690349102020264, 0.9812813401222229], 'rec_polys': [array([[ 53,  96],
       ...,
       [ 50, 119]], dtype=int16), array([[160,  98],
       ...,
       [160, 123]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 121]], dtype=int16), array([[482,  91],
       ...,
       [477, 122]], dtype=int16), array([[587,  96],
       ...,
       [587, 125]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[805,  92],
       ...,
       [802, 122]], dtype=int16), array([[912,  92],
       ...,
       [909, 122]], dtype=int16), array([[1019,   92],
       ...,
       [1016,  122]], dtype=int16), array([[1126,   92],
       ...,
       [1123,  120]], dtype=int16), array([[1234,   92],
       ...,
       [1232,  120]], dtype=int16), array([[1345,   96],
       ...,
       [1342,  119]], dtype=int16), array([[1430,   98],
       ...,
       [1430,  120]], dtype=int16), array([[1543,   94],
       ...,
       [1542,  117]], dtype=int16), array([[1658,   96],
       ...,
       [1656,  119]], dtype=int16), array([[1429,  112],
       ...,
       [1428,  135]], dtype=int16), array([[1427,  128],
       ...,
       [1426,  151]], dtype=int16), array([[ 38, 246],
       ...,
       [ 36, 274]], dtype=int16), array([[147, 244],
       ...,
       [144, 274]], dtype=int16), array([[253, 246],
       ...,
       [251, 274]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 50, ..., 124],
       ...,
       [251, ..., 279]], dtype=int16)}
[15:32:49] [    INFO] [测试3.py:1435] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[53, 96], [99, 101], [96, 124], [50, 119]], [[160, 98], [205, 98], [205, 123], [160, 123]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 91], [534, 99], [530, 129], [477, 122]], [[587, 96], [640, 96], [640, 125], [587, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[805, 92], [858, 97], [855, 128], [802, 122]], [[912, 92], [965, 97], [962, 128], [909, 122]], [[1019, 92], [1076, 97], [1073, 128], [1016, 122]], [[1126, 92], [1183, 97], [1180, 126], [1123, 120]], [[1234, 92], [1291, 97], [1289, 126], [1232, 120]], [[1345, 96], [1398, 101], [1396, 124], [1342, 119]], [[1430, 98], [1524, 98], [1524, 120], [1430, 120]], [[1543, 94], [1630, 99], [1629, 122], [1542, 117]], [[1658, 96], [1730, 100], [1728, 124], [1656, 119]], [[1429, 112], [1527, 116], [1525, 140], [1428, 135]], [[1427, 128], [1525, 132], [1524, 156], [1426, 151]], [[38, 246], [111, 251], [109, 279], [36, 274]], [[147, 244], [220, 251], [217, 281], [144, 274]], [[253, 246], [327, 251], [325, 279], [251, 274]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9,jpeg', '10,jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '056c0261-ff21-', '800x1200.jpg', '主图1.jpeg', '4945-8358-de4', 'ead56f746.png', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg'], 'rec_scores': [0.9746143221855164, 0.9788720607757568, 0.9302626252174377, 0.9823043942451477, 0.9520460963249207, 0.8875851631164551, 0.941087543964386, 0.9538896083831787, 0.8853468298912048, 0.9355347752571106, 0.9877234101295471, 0.992492139339447, 0.9567533731460571, 0.9813222885131836, 0.9516213536262512, 0.9775974750518799, 0.9984042048454285, 0.9964826107025146, 0.9405160546302795, 0.9690349102020264, 0.9812813401222229], 'rec_polys': [[[53, 96], [99, 101], [96, 124], [50, 119]], [[160, 98], [205, 98], [205, 123], [160, 123]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 91], [534, 99], [530, 129], [477, 122]], [[587, 96], [640, 96], [640, 125], [587, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[805, 92], [858, 97], [855, 128], [802, 122]], [[912, 92], [965, 97], [962, 128], [909, 122]], [[1019, 92], [1076, 97], [1073, 128], [1016, 122]], [[1126, 92], [1183, 97], [1180, 126], [1123, 120]], [[1234, 92], [1291, 97], [1289, 126], [1232, 120]], [[1345, 96], [1398, 101], [1396, 124], [1342, 119]], [[1430, 98], [1524, 98], [1524, 120], [1430, 120]], [[1543, 94], [1630, 99], [1629, 122], [1542, 117]], [[1658, 96], [1730, 100], [1728, 124], [1656, 119]], [[1429, 112], [1527, 116], [1525, 140], [1428, 135]], [[1427, 128], [1525, 132], [1524, 156], [1426, 151]], [[38, 246], [111, 251], [109, 279], [36, 274]], [[147, 244], [220, 251], [217, 281], [144, 274]], [[253, 246], [327, 251], [325, 279], [251, 274]]], 'rec_boxes': [[50, 96, 99, 124], [160, 98, 205, 123], [266, 98, 315, 125], [372, 96, 423, 126], [477, 91, 534, 129], [587, 96, 640, 125], [698, 98, 747, 125], [802, 92, 858, 128], [909, 92, 965, 128], [1016, 92, 1076, 128], [1123, 92, 1183, 126], [1232, 92, 1291, 126], [1342, 96, 1398, 124], [1430, 98, 1524, 120], [1542, 94, 1630, 122], [1656, 96, 1730, 124], [1428, 112, 1527, 140], [1426, 128, 1525, 156], [36, 246, 111, 279], [144, 244, 220, 281], [251, 246, 327, 279]]}}
[15:32:49] [    INFO] [测试3.py:1444] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[53, 96], [99, 101], [96, 124], [50, 119]], [[160, 98], [205, 98], [205, 123], [160, 123]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 91], [534, 99], [530, 129], [477, 122]], [[587, 96], [640, 96], [640, 125], [587, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[805, 92], [858, 97], [855, 128], [802, 122]], [[912, 92], [965, 97], [962, 128], [909, 122]], [[1019, 92], [1076, 97], [1073, 128], [1016, 122]], [[1126, 92], [1183, 97], [1180, 126], [1123, 120]], [[1234, 92], [1291, 97], [1289, 126], [1232, 120]], [[1345, 96], [1398, 101], [1396, 124], [1342, 119]], [[1430, 98], [1524, 98], [1524, 120], [1430, 120]], [[1543, 94], [1630, 99], [1629, 122], [1542, 117]], [[1658, 96], [1730, 100], [1728, 124], [1656, 119]], [[1429, 112], [1527, 116], [1525, 140], [1428, 135]], [[1427, 128], [1525, 132], [1524, 156], [1426, 151]], [[38, 246], [111, 251], [109, 279], [36, 274]], [[147, 244], [220, 251], [217, 281], [144, 274]], [[253, 246], [327, 251], [325, 279], [251, 274]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9,jpeg', '10,jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '056c0261-ff21-', '800x1200.jpg', '主图1.jpeg', '4945-8358-de4', 'ead56f746.png', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg'], 'rec_scores': [0.9746143221855164, 0.9788720607757568, 0.9302626252174377, 0.9823043942451477, 0.9520460963249207, 0.8875851631164551, 0.941087543964386, 0.9538896083831787, 0.8853468298912048, 0.9355347752571106, 0.9877234101295471, 0.992492139339447, 0.9567533731460571, 0.9813222885131836, 0.9516213536262512, 0.9775974750518799, 0.9984042048454285, 0.9964826107025146, 0.9405160546302795, 0.9690349102020264, 0.9812813401222229], 'rec_polys': [[[53, 96], [99, 101], [96, 124], [50, 119]], [[160, 98], [205, 98], [205, 123], [160, 123]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [420, 126], [372, 121]], [[482, 91], [534, 99], [530, 129], [477, 122]], [[587, 96], [640, 96], [640, 125], [587, 125]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[805, 92], [858, 97], [855, 128], [802, 122]], [[912, 92], [965, 97], [962, 128], [909, 122]], [[1019, 92], [1076, 97], [1073, 128], [1016, 122]], [[1126, 92], [1183, 97], [1180, 126], [1123, 120]], [[1234, 92], [1291, 97], [1289, 126], [1232, 120]], [[1345, 96], [1398, 101], [1396, 124], [1342, 119]], [[1430, 98], [1524, 98], [1524, 120], [1430, 120]], [[1543, 94], [1630, 99], [1629, 122], [1542, 117]], [[1658, 96], [1730, 100], [1728, 124], [1656, 119]], [[1429, 112], [1527, 116], [1525, 140], [1428, 135]], [[1427, 128], [1525, 132], [1524, 156], [1426, 151]], [[38, 246], [111, 251], [109, 279], [36, 274]], [[147, 244], [220, 251], [217, 281], [144, 274]], [[253, 246], [327, 251], [325, 279], [251, 274]]], 'rec_boxes': [[50, 96, 99, 124], [160, 98, 205, 123], [266, 98, 315, 125], [372, 96, 423, 126], [477, 91, 534, 129], [587, 96, 640, 125], [698, 98, 747, 125], [802, 92, 858, 128], [909, 92, 965, 128], [1016, 92, 1076, 128], [1123, 92, 1183, 126], [1232, 92, 1291, 126], [1342, 96, 1398, 124], [1430, 98, 1524, 120], [1542, 94, 1630, 122], [1656, 96, 1730, 124], [1428, 112, 1527, 140], [1426, 128, 1525, 156], [36, 246, 111, 279], [144, 244, 220, 281], [251, 246, 327, 279]]}
[15:32:49] [    INFO] [测试3.py:1452] - 识别到的文本: ['1.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9,jpeg', '10,jpeg', '11.jpeg', '12.jpeg', '13.jpeg', '056c0261-ff21-', '800x1200.jpg', '主图1.jpeg', '4945-8358-de4', 'ead56f746.png', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:32:49] [    INFO] [测试3.py:1453] - 识别的置信度: [0.9746143221855164, 0.9788720607757568, 0.9302626252174377, 0.9823043942451477, 0.9520460963249207, 0.8875851631164551, 0.941087543964386, 0.9538896083831787, 0.8853468298912048, 0.9355347752571106, 0.9877234101295471, 0.992492139339447, 0.9567533731460571, 0.9813222885131836, 0.9516213536262512, 0.9775974750518799, 0.9984042048454285, 0.9964826107025146, 0.9405160546302795, 0.9690349102020264, 0.9812813401222229]
[15:32:49] [    INFO] [测试3.py:1454] - 识别的坐标框: [[50, 96, 99, 124], [160, 98, 205, 123], [266, 98, 315, 125], [372, 96, 423, 126], [477, 91, 534, 129], [587, 96, 640, 125], [698, 98, 747, 125], [802, 92, 858, 128], [909, 92, 965, 128], [1016, 92, 1076, 128], [1123, 92, 1183, 126], [1232, 92, 1291, 126], [1342, 96, 1398, 124], [1430, 98, 1524, 120], [1542, 94, 1630, 122], [1656, 96, 1730, 124], [1428, 112, 1527, 140], [1426, 128, 1525, 156], [36, 246, 111, 279], [144, 244, 220, 281], [251, 246, 327, 279]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=1.jpeg, 置信度=0.9746143221855164, 原始坐标=[50, 96, 99, 124], 转换后坐标=[[50, 96], [99, 96], [99, 124], [50, 124]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=2.jpeg, 置信度=0.9788720607757568, 原始坐标=[160, 98, 205, 123], 转换后坐标=[[160, 98], [205, 98], [205, 123], [160, 123]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=3.jpeg, 置信度=0.9302626252174377, 原始坐标=[266, 98, 315, 125], 转换后坐标=[[266, 98], [315, 98], [315, 125], [266, 125]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=4.jpeg, 置信度=0.9823043942451477, 原始坐标=[372, 96, 423, 126], 转换后坐标=[[372, 96], [423, 96], [423, 126], [372, 126]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=5.jpeg, 置信度=0.9520460963249207, 原始坐标=[477, 91, 534, 129], 转换后坐标=[[477, 91], [534, 91], [534, 129], [477, 129]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=6.jpeg, 置信度=0.8875851631164551, 原始坐标=[587, 96, 640, 125], 转换后坐标=[[587, 96], [640, 96], [640, 125], [587, 125]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=7.jpeg, 置信度=0.941087543964386, 原始坐标=[698, 98, 747, 125], 转换后坐标=[[698, 98], [747, 98], [747, 125], [698, 125]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=8,jpeg, 置信度=0.9538896083831787, 原始坐标=[802, 92, 858, 128], 转换后坐标=[[802, 92], [858, 92], [858, 128], [802, 128]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=9,jpeg, 置信度=0.8853468298912048, 原始坐标=[909, 92, 965, 128], 转换后坐标=[[909, 92], [965, 92], [965, 128], [909, 128]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=10,jpeg, 置信度=0.9355347752571106, 原始坐标=[1016, 92, 1076, 128], 转换后坐标=[[1016, 92], [1076, 92], [1076, 128], [1016, 128]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=11.jpeg, 置信度=0.9877234101295471, 原始坐标=[1123, 92, 1183, 126], 转换后坐标=[[1123, 92], [1183, 92], [1183, 126], [1123, 126]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=12.jpeg, 置信度=0.992492139339447, 原始坐标=[1232, 92, 1291, 126], 转换后坐标=[[1232, 92], [1291, 92], [1291, 126], [1232, 126]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=13.jpeg, 置信度=0.9567533731460571, 原始坐标=[1342, 96, 1398, 124], 转换后坐标=[[1342, 96], [1398, 96], [1398, 124], [1342, 124]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=056c0261-ff21-, 置信度=0.9813222885131836, 原始坐标=[1430, 98, 1524, 120], 转换后坐标=[[1430, 98], [1524, 98], [1524, 120], [1430, 120]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9516213536262512, 原始坐标=[1542, 94, 1630, 122], 转换后坐标=[[1542, 94], [1630, 94], [1630, 122], [1542, 122]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9775974750518799, 原始坐标=[1656, 96, 1730, 124], 转换后坐标=[[1656, 96], [1730, 96], [1730, 124], [1656, 124]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=4945-8358-de4, 置信度=0.9984042048454285, 原始坐标=[1428, 112, 1527, 140], 转换后坐标=[[1428, 112], [1527, 112], [1527, 140], [1428, 140]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=ead56f746.png, 置信度=0.9964826107025146, 原始坐标=[1426, 128, 1525, 156], 转换后坐标=[[1426, 128], [1525, 128], [1525, 156], [1426, 156]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9405160546302795, 原始坐标=[36, 246, 111, 279], 转换后坐标=[[36, 246], [111, 246], [111, 279], [36, 279]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9690349102020264, 原始坐标=[144, 244, 220, 281], 转换后坐标=[[144, 244], [220, 244], [220, 281], [144, 281]]
[15:32:49] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.9812813401222229, 原始坐标=[251, 246, 327, 279], 转换后坐标=[[251, 246], [327, 246], [327, 279], [251, 279]]
[15:32:49] [    INFO] [测试3.py:1478] - 转换完成，共转换21个结果
[15:32:49] [   DEBUG] [测试3.py:1610] - OCR结果格式转换完成: [[[[[50, 96], [99, 96], [99, 124], [50, 124]], ['1.jpeg', 0.9746143221855164]], [[[160, 98], [205, 98], [205, 123], [160, 123]], ['2.jpeg', 0.9788720607757568]], [[[266, 98], [315, 98], [315, 125], [266, 125]], ['3.jpeg', 0.9302626252174377]], [[[372, 96], [423, 96], [423, 126], [372, 126]], ['4.jpeg', 0.9823043942451477]], [[[477, 91], [534, 91], [534, 129], [477, 129]], ['5.jpeg', 0.9520460963249207]], [[[587, 96], [640, 96], [640, 125], [587, 125]], ['6.jpeg', 0.8875851631164551]], [[[698, 98], [747, 98], [747, 125], [698, 125]], ['7.jpeg', 0.941087543964386]], [[[802, 92], [858, 92], [858, 128], [802, 128]], ['8,jpeg', 0.9538896083831787]], [[[909, 92], [965, 92], [965, 128], [909, 128]], ['9,jpeg', 0.8853468298912048]], [[[1016, 92], [1076, 92], [1076, 128], [1016, 128]], ['10,jpeg', 0.9355347752571106]], [[[1123, 92], [1183, 92], [1183, 126], [1123, 126]], ['11.jpeg', 0.9877234101295471]], [[[1232, 92], [1291, 92], [1291, 126], [1232, 126]], ['12.jpeg', 0.992492139339447]], [[[1342, 96], [1398, 96], [1398, 124], [1342, 124]], ['13.jpeg', 0.9567533731460571]], [[[1430, 98], [1524, 98], [1524, 120], [1430, 120]], ['056c0261-ff21-', 0.9813222885131836]], [[[1542, 94], [1630, 94], [1630, 122], [1542, 122]], ['800x1200.jpg', 0.9516213536262512]], [[[1656, 96], [1730, 96], [1730, 124], [1656, 124]], ['主图1.jpeg', 0.9775974750518799]], [[[1428, 112], [1527, 112], [1527, 140], [1428, 140]], ['4945-8358-de4', 0.9984042048454285]], [[[1426, 128], [1525, 128], [1525, 156], [1426, 156]], ['ead56f746.png', 0.9964826107025146]], [[[36, 246], [111, 246], [111, 279], [36, 279]], ['主图2.jpeg', 0.9405160546302795]], [[[144, 244], [220, 244], [220, 281], [144, 281]], ['主图3.jpeg', 0.9690349102020264]], [[[251, 246], [327, 246], [327, 279], [251, 279]], ['主图4.jpeg', 0.9812813401222229]]]]
[15:32:49] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '1.jpeg', 位置: (234, 210), 置信度: 0.9746143221855164
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '1.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'1.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (342, 210), 置信度: 0.9788720607757568
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (450, 211), 置信度: 0.9302626252174377
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '4.jpeg', 位置: (557, 211), 置信度: 0.9823043942451477
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (665, 210), 置信度: 0.9520460963249207
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '6.jpeg', 位置: (773, 210), 置信度: 0.8875851631164551
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '7.jpeg', 位置: (882, 211), 置信度: 0.941087543964386
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '8,jpeg', 位置: (990, 210), 置信度: 0.9538896083831787
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8,jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8,jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '9,jpeg', 位置: (1097, 210), 置信度: 0.8853468298912048
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9,jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'9,jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '10,jpeg', 位置: (1206, 210), 置信度: 0.9355347752571106
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '10,jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'10,jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '11.jpeg', 位置: (1313, 209), 置信度: 0.9877234101295471
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '11.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'11.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '12.jpeg', 位置: (1421, 209), 置信度: 0.992492139339447
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '12.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'12.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '13.jpeg', 位置: (1530, 210), 置信度: 0.9567533731460571
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '13.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'13.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '056c0261-ff21-', 位置: (1637, 209), 置信度: 0.9813222885131836
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '056c0261-ff21-'
[15:32:49] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'056'在文本'056c0261-ff21-'中, 点击位置: (1637, 209)
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (1746, 208), 置信度: 0.9516213536262512
[15:32:49] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (1746, 208)
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1853, 210), 置信度: 0.9775974750518799
[15:32:49] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1853, 210)
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '4945-8358-de4', 位置: (1637, 226), 置信度: 0.9984042048454285
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4945-8358-de4'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4945-8358-de4'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: 'ead56f746.png', 位置: (1635, 242), 置信度: 0.9964826107025146
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'ead56f746.png'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'ead56f746.png'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (233, 362), 置信度: 0.9405160546302795
[15:32:49] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (233, 362)
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (342, 362), 置信度: 0.9690349102020264
[15:32:49] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (342, 362)
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (449, 362), 置信度: 0.9812813401222229
[15:32:49] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (449, 362)
[15:32:49] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[15:32:49] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[15:32:49] [    INFO] [测试3.py:722] - OCR识别统计:
[15:32:49] [    INFO] [测试3.py:723] - - 总文本块数: 21
[15:32:49] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 21
[15:32:49] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[15:32:49] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[15:32:49] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[15:32:49] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[15:32:49] [    INFO] [测试3.py:757] - 点击第1个位置: (1637, 209)
[15:32:50] [    INFO] [测试3.py:757] - 点击第2个位置: (1746, 208)
[15:32:50] [    INFO] [测试3.py:757] - 点击第3个位置: (1853, 210)
[15:32:51] [    INFO] [测试3.py:757] - 点击第4个位置: (233, 362)
[15:32:52] [    INFO] [测试3.py:757] - 点击第5个位置: (342, 362)
[15:32:52] [    INFO] [测试3.py:757] - 点击第6个位置: (449, 362)
[15:32:53] [    INFO] [测试3.py:764] - 释放Ctrl键
[15:32:53] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[15:32:53] [    INFO] [测试3.py:1339] - 点击打开按钮...
[15:32:53] [    INFO] [测试3.py:1343] - 等待图片加载到图片空间...
[15:32:54] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[15:32:55] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[15:32:56] [    INFO] [测试3.py:871] - 检测到'上传中'状态，位置: Box(left=571, top=846, width=55, height=19)，继续等待2秒...
[15:32:59] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[15:32:59] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[15:33:00] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[15:33:00] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[15:33:00] [    INFO] [测试3.py:1562] - 开始OCR识别图片: logs\debug_space_screenshot.png
[15:33:00] [    INFO] [测试3.py:1579] - 当前识别场景: main
[15:33:00] [   DEBUG] [测试3.py:1582] - 正在执行OCR识别...
[15:33:02] [   DEBUG] [测试3.py:1585] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[191, ..., 203],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[191, ..., 203],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[191, ..., 203],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[600,  17],
       ...,
       [600,  35]], dtype=int16), array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [290,  62]], dtype=int16), array([[429,  43],
       ...,
       [427,  61]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[295,  62],
       ...,
       [295,  77]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[564,  62],
       ...,
       [564,  77]], dtype=int16), array([[317,  93],
       ...,
       [317, 121]], dtype=int16), array([[470,  94],
       ...,
       [470, 123]], dtype=int16), array([[346, 194],
       ...,
       [346, 206]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[292, 215],
       ...,
       [291, 237]], dtype=int16), array([[423, 215],
       ...,
       [421, 235]], dtype=int16), array([[558, 216],
       ...,
       [557, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[608, 272],
       ...,
       [608, 288]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800x800', '800x800', '800×800', '800×1200', 'SA2H', 'BA2H', '56c0261-ff21-494...', '主图1.jpg', '主图1.jpg', '引主图2jpg', '引主图3.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9340793490409851, 0.9721274971961975, 0.9678918123245239, 0.9861000180244446, 0.9692646861076355, 0.9743536710739136, 0.9297652840614319, 0.9282951354980469, 0.9743536710739136, 0.9527221918106079, 0.5412565469741821, 0.5683974623680115, 0.9350449442863464, 0.9442705512046814, 0.9789379835128784, 0.9408549666404724, 0.9654053449630737, 0.9506109952926636, 0.9704387784004211, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.9202616810798645], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [290,  62]], dtype=int16), array([[429,  43],
       ...,
       [427,  61]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[295,  62],
       ...,
       [295,  77]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[564,  62],
       ...,
       [564,  77]], dtype=int16), array([[317,  93],
       ...,
       [317, 121]], dtype=int16), array([[470,  94],
       ...,
       [470, 123]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[292, 215],
       ...,
       [291, 237]], dtype=int16), array([[423, 215],
       ...,
       [421, 235]], dtype=int16), array([[558, 216],
       ...,
       [557, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [445, ..., 326]], dtype=int16)}]
[15:33:02] [    INFO] [测试3.py:1553] - OCR结果已保存到: logs\result_20250828_153302_main.txt 和 logs\result_20250828_153302_main.json
[15:33:02] [    INFO] [测试3.py:1428] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[191, ..., 203],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[191, ..., 203],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[191, ..., 203],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[600,  17],
       ...,
       [600,  35]], dtype=int16), array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [290,  62]], dtype=int16), array([[429,  43],
       ...,
       [427,  61]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[295,  62],
       ...,
       [295,  77]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[564,  62],
       ...,
       [564,  77]], dtype=int16), array([[317,  93],
       ...,
       [317, 121]], dtype=int16), array([[470,  94],
       ...,
       [470, 123]], dtype=int16), array([[346, 194],
       ...,
       [346, 206]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[292, 215],
       ...,
       [291, 237]], dtype=int16), array([[423, 215],
       ...,
       [421, 235]], dtype=int16), array([[558, 216],
       ...,
       [557, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[608, 272],
       ...,
       [608, 288]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800x800', '800x800', '800×800', '800×1200', 'SA2H', 'BA2H', '56c0261-ff21-494...', '主图1.jpg', '主图1.jpg', '引主图2jpg', '引主图3.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9340793490409851, 0.9721274971961975, 0.9678918123245239, 0.9861000180244446, 0.9692646861076355, 0.9743536710739136, 0.9297652840614319, 0.9282951354980469, 0.9743536710739136, 0.9527221918106079, 0.5412565469741821, 0.5683974623680115, 0.9350449442863464, 0.9442705512046814, 0.9789379835128784, 0.9408549666404724, 0.9654053449630737, 0.9506109952926636, 0.9704387784004211, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.9202616810798645], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[157,  41],
       ...,
       [155,  63]], dtype=int16), array([[292,  41],
       ...,
       [290,  62]], dtype=int16), array([[429,  43],
       ...,
       [427,  61]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[159,  62],
       ...,
       [159,  77]], dtype=int16), array([[295,  62],
       ...,
       [295,  77]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[564,  62],
       ...,
       [564,  77]], dtype=int16), array([[317,  93],
       ...,
       [317, 121]], dtype=int16), array([[470,  94],
       ...,
       [470, 123]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[163, 217],
       ...,
       [162, 236]], dtype=int16), array([[292, 215],
       ...,
       [291, 237]], dtype=int16), array([[423, 215],
       ...,
       [421, 235]], dtype=int16), array([[558, 216],
       ...,
       [557, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[296, 236],
       ...,
       [296, 251]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [445, ..., 326]], dtype=int16)}
[15:33:03] [    INFO] [测试3.py:1435] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[600, 17], [626, 17], [626, 35], [600, 35]], [[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 41], [351, 46], [349, 67], [290, 62]], [[429, 43], [485, 47], [484, 65], [427, 61]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[295, 62], [346, 62], [346, 77], [295, 77]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[564, 62], [620, 62], [620, 77], [564, 77]], [[317, 93], [330, 93], [330, 121], [317, 121]], [[470, 94], [483, 94], [483, 123], [470, 123]], [[346, 194], [366, 194], [366, 206], [346, 206]], [[0, 221], [103, 221], [103, 235], [0, 235]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[292, 215], [350, 219], [349, 240], [291, 237]], [[423, 215], [493, 220], [492, 239], [421, 235]], [[558, 216], [628, 220], [627, 239], [557, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[608, 272], [619, 272], [619, 288], [608, 288]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800x800', '800x800', '800×800', '800×1200', 'SA2H', 'BA2H', '56c0261-ff21-494...', '主图1.jpg', '主图1.jpg', '引主图2jpg', '引主图3.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9340793490409851, 0.9721274971961975, 0.9678918123245239, 0.9861000180244446, 0.9692646861076355, 0.9743536710739136, 0.9297652840614319, 0.9282951354980469, 0.9743536710739136, 0.9527221918106079, 0.5412565469741821, 0.5683974623680115, 0.9350449442863464, 0.9442705512046814, 0.9789379835128784, 0.9408549666404724, 0.9654053449630737, 0.9506109952926636, 0.9704387784004211, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.9202616810798645], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 41], [351, 46], [349, 67], [290, 62]], [[429, 43], [485, 47], [484, 65], [427, 61]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[295, 62], [346, 62], [346, 77], [295, 77]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[564, 62], [620, 62], [620, 77], [564, 77]], [[317, 93], [330, 93], [330, 121], [317, 121]], [[470, 94], [483, 94], [483, 123], [470, 123]], [[0, 221], [103, 221], [103, 235], [0, 235]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[292, 215], [350, 219], [349, 240], [291, 237]], [[423, 215], [493, 220], [492, 239], [421, 235]], [[558, 216], [628, 220], [627, 239], [557, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'rec_boxes': [[18, 41, 78, 67], [155, 41, 214, 67], [290, 41, 351, 67], [427, 43, 485, 65], [553, 43, 631, 65], [24, 64, 73, 76], [159, 62, 209, 77], [295, 62, 346, 77], [432, 64, 481, 76], [564, 62, 620, 77], [317, 93, 330, 121], [470, 94, 483, 123], [0, 221, 103, 235], [162, 217, 221, 238], [291, 215, 350, 240], [421, 215, 493, 239], [557, 216, 628, 239], [24, 236, 74, 251], [161, 242, 208, 254], [296, 236, 346, 251], [432, 240, 482, 255], [567, 240, 617, 255], [445, 308, 604, 326]]}}
[15:33:03] [    INFO] [测试3.py:1444] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[600, 17], [626, 17], [626, 35], [600, 35]], [[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 41], [351, 46], [349, 67], [290, 62]], [[429, 43], [485, 47], [484, 65], [427, 61]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[295, 62], [346, 62], [346, 77], [295, 77]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[564, 62], [620, 62], [620, 77], [564, 77]], [[317, 93], [330, 93], [330, 121], [317, 121]], [[470, 94], [483, 94], [483, 123], [470, 123]], [[346, 194], [366, 194], [366, 206], [346, 206]], [[0, 221], [103, 221], [103, 235], [0, 235]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[292, 215], [350, 219], [349, 240], [291, 237]], [[423, 215], [493, 220], [492, 239], [421, 235]], [[558, 216], [628, 220], [627, 239], [557, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[608, 272], [619, 272], [619, 288], [608, 288]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800x800', '800x800', '800×800', '800×1200', 'SA2H', 'BA2H', '56c0261-ff21-494...', '主图1.jpg', '主图1.jpg', '引主图2jpg', '引主图3.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：1:1智能裁剪'], 'rec_scores': [0.9340793490409851, 0.9721274971961975, 0.9678918123245239, 0.9861000180244446, 0.9692646861076355, 0.9743536710739136, 0.9297652840614319, 0.9282951354980469, 0.9743536710739136, 0.9527221918106079, 0.5412565469741821, 0.5683974623680115, 0.9350449442863464, 0.9442705512046814, 0.9789379835128784, 0.9408549666404724, 0.9654053449630737, 0.9506109952926636, 0.9704387784004211, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.9202616810798645], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[157, 41], [214, 45], [212, 67], [155, 63]], [[292, 41], [351, 46], [349, 67], [290, 62]], [[429, 43], [485, 47], [484, 65], [427, 61]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[159, 62], [209, 62], [209, 77], [159, 77]], [[295, 62], [346, 62], [346, 77], [295, 77]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[564, 62], [620, 62], [620, 77], [564, 77]], [[317, 93], [330, 93], [330, 121], [317, 121]], [[470, 94], [483, 94], [483, 123], [470, 123]], [[0, 221], [103, 221], [103, 235], [0, 235]], [[163, 217], [221, 220], [220, 238], [162, 236]], [[292, 215], [350, 219], [349, 240], [291, 237]], [[423, 215], [493, 220], [492, 239], [421, 235]], [[558, 216], [628, 220], [627, 239], [557, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[296, 236], [346, 236], [346, 251], [296, 251]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[567, 240], [617, 240], [617, 255], [567, 255]], [[445, 308], [604, 308], [604, 326], [445, 326]]], 'rec_boxes': [[18, 41, 78, 67], [155, 41, 214, 67], [290, 41, 351, 67], [427, 43, 485, 65], [553, 43, 631, 65], [24, 64, 73, 76], [159, 62, 209, 77], [295, 62, 346, 77], [432, 64, 481, 76], [564, 62, 620, 77], [317, 93, 330, 121], [470, 94, 483, 123], [0, 221, 103, 235], [162, 217, 221, 238], [291, 215, 350, 240], [421, 215, 493, 239], [557, 216, 628, 239], [24, 236, 74, 251], [161, 242, 208, 254], [296, 236, 346, 251], [432, 240, 482, 255], [567, 240, 617, 255], [445, 308, 604, 326]]}
[15:33:03] [    INFO] [测试3.py:1452] - 识别到的文本: ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800x800', '800x800', '800×800', '800×1200', 'SA2H', 'BA2H', '56c0261-ff21-494...', '主图1.jpg', '主图1.jpg', '引主图2jpg', '引主图3.jpg', '800×800', '800×800', '800×800', '800×800', '800x800', '①裁剪宽高比：1:1智能裁剪']
[15:33:03] [    INFO] [测试3.py:1453] - 识别的置信度: [0.9340793490409851, 0.9721274971961975, 0.9678918123245239, 0.9861000180244446, 0.9692646861076355, 0.9743536710739136, 0.9297652840614319, 0.9282951354980469, 0.9743536710739136, 0.9527221918106079, 0.5412565469741821, 0.5683974623680115, 0.9350449442863464, 0.9442705512046814, 0.9789379835128784, 0.9408549666404724, 0.9654053449630737, 0.9506109952926636, 0.9704387784004211, 0.9506109952926636, 0.9506109952926636, 0.9297652840614319, 0.9202616810798645]
[15:33:03] [    INFO] [测试3.py:1454] - 识别的坐标框: [[18, 41, 78, 67], [155, 41, 214, 67], [290, 41, 351, 67], [427, 43, 485, 65], [553, 43, 631, 65], [24, 64, 73, 76], [159, 62, 209, 77], [295, 62, 346, 77], [432, 64, 481, 76], [564, 62, 620, 77], [317, 93, 330, 121], [470, 94, 483, 123], [0, 221, 103, 235], [162, 217, 221, 238], [291, 215, 350, 240], [421, 215, 493, 239], [557, 216, 628, 239], [24, 236, 74, 251], [161, 242, 208, 254], [296, 236, 346, 251], [432, 240, 482, 255], [567, 240, 617, 255], [445, 308, 604, 326]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9340793490409851, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9721274971961975, 原始坐标=[155, 41, 214, 67], 转换后坐标=[[155, 41], [214, 41], [214, 67], [155, 67]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9678918123245239, 原始坐标=[290, 41, 351, 67], 转换后坐标=[[290, 41], [351, 41], [351, 67], [290, 67]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9861000180244446, 原始坐标=[427, 43, 485, 65], 转换后坐标=[[427, 43], [485, 43], [485, 65], [427, 65]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9692646861076355, 原始坐标=[553, 43, 631, 65], 转换后坐标=[[553, 43], [631, 43], [631, 65], [553, 65]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9743536710739136, 原始坐标=[24, 64, 73, 76], 转换后坐标=[[24, 64], [73, 64], [73, 76], [24, 76]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800x800, 置信度=0.9297652840614319, 原始坐标=[159, 62, 209, 77], 转换后坐标=[[159, 62], [209, 62], [209, 77], [159, 77]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800x800, 置信度=0.9282951354980469, 原始坐标=[295, 62, 346, 77], 转换后坐标=[[295, 62], [346, 62], [346, 77], [295, 77]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9743536710739136, 原始坐标=[432, 64, 481, 76], 转换后坐标=[[432, 64], [481, 64], [481, 76], [432, 76]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×1200, 置信度=0.9527221918106079, 原始坐标=[564, 62, 620, 77], 转换后坐标=[[564, 62], [620, 62], [620, 77], [564, 77]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=SA2H, 置信度=0.5412565469741821, 原始坐标=[317, 93, 330, 121], 转换后坐标=[[317, 93], [330, 93], [330, 121], [317, 121]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=BA2H, 置信度=0.5683974623680115, 原始坐标=[470, 94, 483, 123], 转换后坐标=[[470, 94], [483, 94], [483, 123], [470, 123]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=56c0261-ff21-494..., 置信度=0.9350449442863464, 原始坐标=[0, 221, 103, 235], 转换后坐标=[[0, 221], [103, 221], [103, 235], [0, 235]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9442705512046814, 原始坐标=[162, 217, 221, 238], 转换后坐标=[[162, 217], [221, 217], [221, 238], [162, 238]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9789379835128784, 原始坐标=[291, 215, 350, 240], 转换后坐标=[[291, 215], [350, 215], [350, 240], [291, 240]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=引主图2jpg, 置信度=0.9408549666404724, 原始坐标=[421, 215, 493, 239], 转换后坐标=[[421, 215], [493, 215], [493, 239], [421, 239]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=引主图3.jpg, 置信度=0.9654053449630737, 原始坐标=[557, 216, 628, 239], 转换后坐标=[[557, 216], [628, 216], [628, 239], [557, 239]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[24, 236, 74, 251], 转换后坐标=[[24, 236], [74, 236], [74, 251], [24, 251]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9704387784004211, 原始坐标=[161, 242, 208, 254], 转换后坐标=[[161, 242], [208, 242], [208, 254], [161, 254]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[296, 236, 346, 251], 转换后坐标=[[296, 236], [346, 236], [346, 251], [296, 251]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[432, 240, 482, 255], 转换后坐标=[[432, 240], [482, 240], [482, 255], [432, 255]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800x800, 置信度=0.9297652840614319, 原始坐标=[567, 240, 617, 255], 转换后坐标=[[567, 240], [617, 240], [617, 255], [567, 255]]
[15:33:03] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=①裁剪宽高比：1:1智能裁剪, 置信度=0.9202616810798645, 原始坐标=[445, 308, 604, 326], 转换后坐标=[[445, 308], [604, 308], [604, 326], [445, 326]]
[15:33:03] [    INFO] [测试3.py:1478] - 转换完成，共转换23个结果
[15:33:03] [   DEBUG] [测试3.py:1610] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图4.jpg', 0.9340793490409851]], [[[155, 41], [214, 41], [214, 67], [155, 67]], ['主图3.jpg', 0.9721274971961975]], [[[290, 41], [351, 41], [351, 67], [290, 67]], ['主图2.jpg', 0.9678918123245239]], [[[427, 43], [485, 43], [485, 65], [427, 65]], ['主图1.jpg', 0.9861000180244446]], [[[553, 43], [631, 43], [631, 65], [553, 65]], ['800x1200.jpg', 0.9692646861076355]], [[[24, 64], [73, 64], [73, 76], [24, 76]], ['800×800', 0.9743536710739136]], [[[159, 62], [209, 62], [209, 77], [159, 77]], ['800x800', 0.9297652840614319]], [[[295, 62], [346, 62], [346, 77], [295, 77]], ['800x800', 0.9282951354980469]], [[[432, 64], [481, 64], [481, 76], [432, 76]], ['800×800', 0.9743536710739136]], [[[564, 62], [620, 62], [620, 77], [564, 77]], ['800×1200', 0.9527221918106079]], [[[317, 93], [330, 93], [330, 121], [317, 121]], ['SA2H', 0.5412565469741821]], [[[470, 94], [483, 94], [483, 123], [470, 123]], ['BA2H', 0.5683974623680115]], [[[0, 221], [103, 221], [103, 235], [0, 235]], ['56c0261-ff21-494...', 0.9350449442863464]], [[[162, 217], [221, 217], [221, 238], [162, 238]], ['主图1.jpg', 0.9442705512046814]], [[[291, 215], [350, 215], [350, 240], [291, 240]], ['主图1.jpg', 0.9789379835128784]], [[[421, 215], [493, 215], [493, 239], [421, 239]], ['引主图2jpg', 0.9408549666404724]], [[[557, 216], [628, 216], [628, 239], [557, 239]], ['引主图3.jpg', 0.9654053449630737]], [[[24, 236], [74, 236], [74, 251], [24, 251]], ['800×800', 0.9506109952926636]], [[[161, 242], [208, 242], [208, 254], [161, 254]], ['800×800', 0.9704387784004211]], [[[296, 236], [346, 236], [346, 251], [296, 251]], ['800×800', 0.9506109952926636]], [[[432, 240], [482, 240], [482, 255], [432, 255]], ['800×800', 0.9506109952926636]], [[[567, 240], [617, 240], [617, 255], [567, 255]], ['800x800', 0.9297652840614319]], [[[445, 308], [604, 308], [604, 326], [445, 326]], ['①裁剪宽高比：1:1智能裁剪', 0.9202616810798645]]]]
[15:33:03] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9340793490409851
[15:33:03] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (864, 604), 置信度: 0.9721274971961975
[15:33:03] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1000, 604), 置信度: 0.9678918123245239
[15:33:03] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1136, 604), 置信度: 0.9861000180244446
[15:33:03] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (871, 777), 置信度: 0.9442705512046814
[15:33:03] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1000, 777), 置信度: 0.9789379835128784
[15:33:03] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1136, 534)
[15:33:04] [    INFO] [测试3.py:978] - 主图1已经点击过，跳过
[15:33:04] [    INFO] [测试3.py:978] - 主图1已经点击过，跳过
[15:33:04] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1000, 534)
[15:33:05] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (864, 534)
[15:33:06] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[15:33:08] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[15:33:08] [    INFO] [测试3.py:1371] - 点击右侧空白处关闭图片空间...
[15:33:09] [    INFO] [测试3.py:1376] - 点击裁剪按钮...
[15:33:10] [    INFO] [测试3.py:1383] - 开始上传UUID透明图...
[15:33:11] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[15:33:12] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[15:33:16] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[15:33:16] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[15:33:16] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250828_153316.png
[15:33:16] [    INFO] [测试3.py:1094] - 目标UUID: 056c0261-ff21-4945-8358-de4ead56f746.png
[15:33:16] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['056', '56c', '6c0', 'c02', '026', '261']
[15:33:16] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[15:33:18] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250828_153316.json
[15:33:18] [    INFO] [测试3.py:1120] - 识别到 26 个文本块
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (61, 97), 置信度: 0.9657
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (170, 97), 置信度: 0.9614
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (281, 96), 置信度: 0.9726
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (391, 96), 置信度: 0.9801
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (501, 96), 置信度: 0.9845
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (610, 96), 置信度: 0.9594
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (36, 122), 置信度: 0.9218
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (146, 123), 置信度: 0.9415
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (256, 123), 置信度: 0.9676
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (366, 123), 置信度: 0.9906
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200.jpg, 位置: (488, 122), 置信度: 0.9642
[15:33:18] [    INFO] [测试3.py:1126] - 识别到文本块: 056c0261-ff21-., 位置: (609, 123), 置信度: 0.9878
[15:33:18] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'056'在文本'056c0261-ff21-.'中
[15:33:18] [    INFO] [测试3.py:1139] - 计算的点击位置: (1379, 623)
[15:33:18] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250828_153316
[15:33:20] [    INFO] [测试3.py:1400] - 开始上传800x1200图片...
[15:33:21] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[15:33:22] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[15:33:25] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[15:33:25] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[15:33:26] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250828_153326.png
[15:33:26] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[15:33:28] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250828_153326.json
[15:33:28] [    INFO] [测试3.py:1233] - 识别到 27 个文本块
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (63, 100), 置信度: 0.9631
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (172, 100), 置信度: 0.9795
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (283, 100), 置信度: 0.9797
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (392, 100), 置信度: 0.9710
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (502, 100), 置信度: 0.9689
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (612, 100), 置信度: 0.9778
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 主图3.jpg, 位置: (38, 127), 置信度: 0.9381
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 主图4jpg, 位置: (147, 126), 置信度: 0.9884
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 主图3.jpg, 位置: (258, 126), 置信度: 0.9227
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 主图4.jpg, 位置: (368, 127), 置信度: 0.9390
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 主图1.jpg, 位置: (478, 126), 置信度: 0.9500
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 主图2.jpg, 位置: (588, 126), 置信度: 0.9827
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: A2H, 位置: (395, 166), 置信度: 0.7189
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: SA2H, 位置: (518, 165), 置信度: 0.7475
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 3A2, 位置: (617, 165), 置信度: 0.6437
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (63, 242), 置信度: 0.9571
[15:33:28] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (174, 243), 置信度: 0.9882
[15:33:28] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[15:33:28] [    INFO] [测试3.py:1251] - 计算的点击位置: (804, 739)
[15:33:28] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250828_153326
[15:33:30] [    INFO] [测试3.py:1413] - 向下滚动页面...
[15:33:31] [    INFO] [测试3.py:1417] - 文件选择完成，上传流程结束
[15:33:31] [    INFO] [测试3.py:1652] - 已创建测试3完成信号文件
[15:39:26] [    INFO] [测试3.py:80] - ==================================================
[15:39:26] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:39:26] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250828.log
[15:39:26] [    INFO] [测试3.py:83] - ==================================================
[15:39:26] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:39:26] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:39:26] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:39:31] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:39:31] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:39:31] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:39:31] [    INFO] [测试3.py:171] - 详情图: ['10.jpeg', '11.jpeg', '2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[15:39:31] [    INFO] [测试3.py:172] - UUID图片: f24a93b2-ed84-43a4-a1d5-2b475b112391.png
[15:39:31] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 2
[15:39:31] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:39:31] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:39:32] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:39:32] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:39:33] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:39:33] [    INFO] [测试3.py:1291] - 已点击坐标
[15:39:37] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:39:37] [ WARNING] [测试3.py:1309] - 未找到SHANGCHUANG.png，等待1.2秒后重试
[15:39:38] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:39:38] [ WARNING] [测试3.py:1312] - 重试后仍未找到上传按钮，继续执行后续步骤
[15:39:42] [    INFO] [测试3.py:1324] - 开始查找2号文件夹...
[15:39:42] [    INFO] [测试3.py:384] - 开始查找文件夹: 2
[15:39:43] [    INFO] [测试3.py:416] - 文件夹2的目标坐标: (209, 161)
[15:39:43] [    INFO] [测试3.py:421] - 当前鼠标位置: (93, 199)
[15:39:43] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 161)
[15:39:43] [    INFO] [测试3.py:451] - 执行点击 #1
[15:39:43] [    INFO] [测试3.py:451] - 执行点击 #2
[15:39:45] [    INFO] [测试3.py:457] - 等待文件夹打开...
[15:39:45] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\2
[15:39:45] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[15:39:46] [    INFO] [测试3.py:1329] - 移动鼠标到(108,600)位置...
[15:39:47] [    INFO] [测试3.py:635] - 开始选择图片...
[15:39:49] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[15:39:49] [    INFO] [测试3.py:655] - 目标UUID: f24a93b2-ed84-43a4-a1d5-2b475b112391.png
[15:39:49] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['f24', '24a', '4a9', 'a93', '93b', '3b2']
[15:39:49] [    INFO] [测试3.py:1562] - 开始OCR识别图片: logs\debug_files_screenshot.png
[15:39:51] [    INFO] [测试3.py:1579] - 当前识别场景: unknown
[15:39:51] [   DEBUG] [测试3.py:1582] - 正在执行OCR识别...
[15:39:53] [   DEBUG] [测试3.py:1585] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 51,  98],
       ...,
       [ 51, 125]], dtype=int16), array([[158,  92],
       ...,
       [155, 122]], dtype=int16), array([[263,  96],
       ...,
       [263, 125]], dtype=int16), array([[374,  92],
       ...,
       [371, 122]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[589,  91],
       ...,
       [584, 120]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[805,  92],
       ...,
       [802, 122]], dtype=int16), array([[910,  92],
       ...,
       [907, 122]], dtype=int16), array([[1017,   96],
       ...,
       [1017,  125]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1213,   98],
       ...,
       [1213,  120]], dtype=int16), array([[1334,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1441,   92],
       ...,
       [1438,  120]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1657,   96],
       ...,
       [1657,  125]], dtype=int16), array([[1214,  114],
       ...,
       [1214,  138]], dtype=int16), array([[1211,  128],
       ...,
       [1210,  151]], dtype=int16), array([[1254,  155],
       ...,
       [1254,  171]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10,jpeg', '11.jpeg', '800x1200.jpg', 'f24a93b2-ed84', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-43a4-a1d5-2b', '475b112391.pn', 'g'], 'rec_scores': [0.959418773651123, 0.9454733729362488, 0.9695303440093994, 0.9122850298881531, 0.9182271361351013, 0.9887283444404602, 0.9261655211448669, 0.9663837552070618, 0.8957313299179077, 0.9266167283058167, 0.9377331733703613, 0.9994295835494995, 0.978055477142334, 0.9075138568878174, 0.9448552131652832, 0.922603964805603, 0.9979790449142456, 0.9992217421531677, 0.9990211725234985], 'rec_polys': [array([[ 51,  98],
       ...,
       [ 51, 125]], dtype=int16), array([[158,  92],
       ...,
       [155, 122]], dtype=int16), array([[263,  96],
       ...,
       [263, 125]], dtype=int16), array([[374,  92],
       ...,
       [371, 122]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[589,  91],
       ...,
       [584, 120]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[805,  92],
       ...,
       [802, 122]], dtype=int16), array([[910,  92],
       ...,
       [907, 122]], dtype=int16), array([[1017,   96],
       ...,
       [1017,  125]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1213,   98],
       ...,
       [1213,  120]], dtype=int16), array([[1334,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1441,   92],
       ...,
       [1438,  120]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1657,   96],
       ...,
       [1657,  125]], dtype=int16), array([[1214,  114],
       ...,
       [1214,  138]], dtype=int16), array([[1211,  128],
       ...,
       [1210,  151]], dtype=int16), array([[1254,  155],
       ...,
       [1254,  171]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[  51, ...,  125],
       ...,
       [1254, ...,  171]], dtype=int16)}]
[15:39:53] [    INFO] [测试3.py:1553] - OCR结果已保存到: logs\result_20250828_153953_unknown.txt 和 logs\result_20250828_153953_unknown.json
[15:39:53] [    INFO] [测试3.py:1428] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 51,  98],
       ...,
       [ 51, 125]], dtype=int16), array([[158,  92],
       ...,
       [155, 122]], dtype=int16), array([[263,  96],
       ...,
       [263, 125]], dtype=int16), array([[374,  92],
       ...,
       [371, 122]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[589,  91],
       ...,
       [584, 120]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[805,  92],
       ...,
       [802, 122]], dtype=int16), array([[910,  92],
       ...,
       [907, 122]], dtype=int16), array([[1017,   96],
       ...,
       [1017,  125]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1213,   98],
       ...,
       [1213,  120]], dtype=int16), array([[1334,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1441,   92],
       ...,
       [1438,  120]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1657,   96],
       ...,
       [1657,  125]], dtype=int16), array([[1214,  114],
       ...,
       [1214,  138]], dtype=int16), array([[1211,  128],
       ...,
       [1210,  151]], dtype=int16), array([[1254,  155],
       ...,
       [1254,  171]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10,jpeg', '11.jpeg', '800x1200.jpg', 'f24a93b2-ed84', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-43a4-a1d5-2b', '475b112391.pn', 'g'], 'rec_scores': [0.959418773651123, 0.9454733729362488, 0.9695303440093994, 0.9122850298881531, 0.9182271361351013, 0.9887283444404602, 0.9261655211448669, 0.9663837552070618, 0.8957313299179077, 0.9266167283058167, 0.9377331733703613, 0.9994295835494995, 0.978055477142334, 0.9075138568878174, 0.9448552131652832, 0.922603964805603, 0.9979790449142456, 0.9992217421531677, 0.9990211725234985], 'rec_polys': [array([[ 51,  98],
       ...,
       [ 51, 125]], dtype=int16), array([[158,  92],
       ...,
       [155, 122]], dtype=int16), array([[263,  96],
       ...,
       [263, 125]], dtype=int16), array([[374,  92],
       ...,
       [371, 122]], dtype=int16), array([[480,  96],
       ...,
       [480, 125]], dtype=int16), array([[589,  91],
       ...,
       [584, 120]], dtype=int16), array([[698,  98],
       ...,
       [698, 125]], dtype=int16), array([[805,  92],
       ...,
       [802, 122]], dtype=int16), array([[910,  92],
       ...,
       [907, 122]], dtype=int16), array([[1017,   96],
       ...,
       [1017,  125]], dtype=int16), array([[1111,   98],
       ...,
       [1111,  121]], dtype=int16), array([[1213,   98],
       ...,
       [1213,  120]], dtype=int16), array([[1334,   92],
       ...,
       [1332,  121]], dtype=int16), array([[1441,   92],
       ...,
       [1438,  120]], dtype=int16), array([[1549,   92],
       ...,
       [1547,  121]], dtype=int16), array([[1657,   96],
       ...,
       [1657,  125]], dtype=int16), array([[1214,  114],
       ...,
       [1214,  138]], dtype=int16), array([[1211,  128],
       ...,
       [1210,  151]], dtype=int16), array([[1254,  155],
       ...,
       [1254,  171]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[  51, ...,  125],
       ...,
       [1254, ...,  171]], dtype=int16)}
[15:39:53] [    INFO] [测试3.py:1435] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[51, 98], [100, 98], [100, 125], [51, 125]], [[158, 92], [210, 97], [206, 128], [155, 122]], [[263, 96], [317, 96], [317, 125], [263, 125]], [[374, 92], [427, 97], [424, 128], [371, 122]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[589, 91], [641, 100], [637, 128], [584, 120]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[805, 92], [857, 97], [853, 128], [802, 122]], [[910, 92], [969, 97], [966, 128], [907, 122]], [[1017, 96], [1075, 96], [1075, 125], [1017, 125]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1213, 98], [1309, 98], [1309, 120], [1213, 120]], [[1334, 92], [1407, 97], [1405, 126], [1332, 121]], [[1441, 92], [1516, 99], [1513, 128], [1438, 120]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1657, 96], [1731, 96], [1731, 125], [1657, 125]], [[1214, 114], [1309, 114], [1309, 138], [1214, 138]], [[1211, 128], [1311, 132], [1310, 156], [1210, 151]], [[1254, 155], [1269, 155], [1269, 171], [1254, 171]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10,jpeg', '11.jpeg', '800x1200.jpg', 'f24a93b2-ed84', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-43a4-a1d5-2b', '475b112391.pn', 'g'], 'rec_scores': [0.959418773651123, 0.9454733729362488, 0.9695303440093994, 0.9122850298881531, 0.9182271361351013, 0.9887283444404602, 0.9261655211448669, 0.9663837552070618, 0.8957313299179077, 0.9266167283058167, 0.9377331733703613, 0.9994295835494995, 0.978055477142334, 0.9075138568878174, 0.9448552131652832, 0.922603964805603, 0.9979790449142456, 0.9992217421531677, 0.9990211725234985], 'rec_polys': [[[51, 98], [100, 98], [100, 125], [51, 125]], [[158, 92], [210, 97], [206, 128], [155, 122]], [[263, 96], [317, 96], [317, 125], [263, 125]], [[374, 92], [427, 97], [424, 128], [371, 122]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[589, 91], [641, 100], [637, 128], [584, 120]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[805, 92], [857, 97], [853, 128], [802, 122]], [[910, 92], [969, 97], [966, 128], [907, 122]], [[1017, 96], [1075, 96], [1075, 125], [1017, 125]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1213, 98], [1309, 98], [1309, 120], [1213, 120]], [[1334, 92], [1407, 97], [1405, 126], [1332, 121]], [[1441, 92], [1516, 99], [1513, 128], [1438, 120]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1657, 96], [1731, 96], [1731, 125], [1657, 125]], [[1214, 114], [1309, 114], [1309, 138], [1214, 138]], [[1211, 128], [1311, 132], [1310, 156], [1210, 151]], [[1254, 155], [1269, 155], [1269, 171], [1254, 171]]], 'rec_boxes': [[51, 98, 100, 125], [155, 92, 210, 128], [263, 96, 317, 125], [371, 92, 427, 128], [480, 96, 533, 125], [584, 91, 641, 128], [698, 98, 747, 125], [802, 92, 857, 128], [907, 92, 969, 128], [1017, 96, 1075, 125], [1111, 98, 1198, 121], [1213, 98, 1309, 120], [1332, 92, 1407, 126], [1438, 92, 1516, 128], [1547, 92, 1625, 126], [1657, 96, 1731, 125], [1214, 114, 1309, 138], [1210, 128, 1311, 156], [1254, 155, 1269, 171]]}}
[15:39:53] [    INFO] [测试3.py:1444] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[51, 98], [100, 98], [100, 125], [51, 125]], [[158, 92], [210, 97], [206, 128], [155, 122]], [[263, 96], [317, 96], [317, 125], [263, 125]], [[374, 92], [427, 97], [424, 128], [371, 122]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[589, 91], [641, 100], [637, 128], [584, 120]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[805, 92], [857, 97], [853, 128], [802, 122]], [[910, 92], [969, 97], [966, 128], [907, 122]], [[1017, 96], [1075, 96], [1075, 125], [1017, 125]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1213, 98], [1309, 98], [1309, 120], [1213, 120]], [[1334, 92], [1407, 97], [1405, 126], [1332, 121]], [[1441, 92], [1516, 99], [1513, 128], [1438, 120]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1657, 96], [1731, 96], [1731, 125], [1657, 125]], [[1214, 114], [1309, 114], [1309, 138], [1214, 138]], [[1211, 128], [1311, 132], [1310, 156], [1210, 151]], [[1254, 155], [1269, 155], [1269, 171], [1254, 171]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10,jpeg', '11.jpeg', '800x1200.jpg', 'f24a93b2-ed84', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-43a4-a1d5-2b', '475b112391.pn', 'g'], 'rec_scores': [0.959418773651123, 0.9454733729362488, 0.9695303440093994, 0.9122850298881531, 0.9182271361351013, 0.9887283444404602, 0.9261655211448669, 0.9663837552070618, 0.8957313299179077, 0.9266167283058167, 0.9377331733703613, 0.9994295835494995, 0.978055477142334, 0.9075138568878174, 0.9448552131652832, 0.922603964805603, 0.9979790449142456, 0.9992217421531677, 0.9990211725234985], 'rec_polys': [[[51, 98], [100, 98], [100, 125], [51, 125]], [[158, 92], [210, 97], [206, 128], [155, 122]], [[263, 96], [317, 96], [317, 125], [263, 125]], [[374, 92], [427, 97], [424, 128], [371, 122]], [[480, 96], [533, 96], [533, 125], [480, 125]], [[589, 91], [641, 100], [637, 128], [584, 120]], [[698, 98], [747, 98], [747, 125], [698, 125]], [[805, 92], [857, 97], [853, 128], [802, 122]], [[910, 92], [969, 97], [966, 128], [907, 122]], [[1017, 96], [1075, 96], [1075, 125], [1017, 125]], [[1111, 98], [1198, 98], [1198, 121], [1111, 121]], [[1213, 98], [1309, 98], [1309, 120], [1213, 120]], [[1334, 92], [1407, 97], [1405, 126], [1332, 121]], [[1441, 92], [1516, 99], [1513, 128], [1438, 120]], [[1549, 92], [1625, 97], [1623, 126], [1547, 121]], [[1657, 96], [1731, 96], [1731, 125], [1657, 125]], [[1214, 114], [1309, 114], [1309, 138], [1214, 138]], [[1211, 128], [1311, 132], [1310, 156], [1210, 151]], [[1254, 155], [1269, 155], [1269, 171], [1254, 171]]], 'rec_boxes': [[51, 98, 100, 125], [155, 92, 210, 128], [263, 96, 317, 125], [371, 92, 427, 128], [480, 96, 533, 125], [584, 91, 641, 128], [698, 98, 747, 125], [802, 92, 857, 128], [907, 92, 969, 128], [1017, 96, 1075, 125], [1111, 98, 1198, 121], [1213, 98, 1309, 120], [1332, 92, 1407, 126], [1438, 92, 1516, 128], [1547, 92, 1625, 126], [1657, 96, 1731, 125], [1214, 114, 1309, 138], [1210, 128, 1311, 156], [1254, 155, 1269, 171]]}
[15:39:53] [    INFO] [测试3.py:1452] - 识别到的文本: ['2.jpeg', '3.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8,jpeg', '9.jpeg', '10,jpeg', '11.jpeg', '800x1200.jpg', 'f24a93b2-ed84', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg', '-43a4-a1d5-2b', '475b112391.pn', 'g']
[15:39:53] [    INFO] [测试3.py:1453] - 识别的置信度: [0.959418773651123, 0.9454733729362488, 0.9695303440093994, 0.9122850298881531, 0.9182271361351013, 0.9887283444404602, 0.9261655211448669, 0.9663837552070618, 0.8957313299179077, 0.9266167283058167, 0.9377331733703613, 0.9994295835494995, 0.978055477142334, 0.9075138568878174, 0.9448552131652832, 0.922603964805603, 0.9979790449142456, 0.9992217421531677, 0.9990211725234985]
[15:39:53] [    INFO] [测试3.py:1454] - 识别的坐标框: [[51, 98, 100, 125], [155, 92, 210, 128], [263, 96, 317, 125], [371, 92, 427, 128], [480, 96, 533, 125], [584, 91, 641, 128], [698, 98, 747, 125], [802, 92, 857, 128], [907, 92, 969, 128], [1017, 96, 1075, 125], [1111, 98, 1198, 121], [1213, 98, 1309, 120], [1332, 92, 1407, 126], [1438, 92, 1516, 128], [1547, 92, 1625, 126], [1657, 96, 1731, 125], [1214, 114, 1309, 138], [1210, 128, 1311, 156], [1254, 155, 1269, 171]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=2.jpeg, 置信度=0.959418773651123, 原始坐标=[51, 98, 100, 125], 转换后坐标=[[51, 98], [100, 98], [100, 125], [51, 125]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=3.jpeg, 置信度=0.9454733729362488, 原始坐标=[155, 92, 210, 128], 转换后坐标=[[155, 92], [210, 92], [210, 128], [155, 128]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=4.jpeg, 置信度=0.9695303440093994, 原始坐标=[263, 96, 317, 125], 转换后坐标=[[263, 96], [317, 96], [317, 125], [263, 125]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=5.jpeg, 置信度=0.9122850298881531, 原始坐标=[371, 92, 427, 128], 转换后坐标=[[371, 92], [427, 92], [427, 128], [371, 128]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=6.jpeg, 置信度=0.9182271361351013, 原始坐标=[480, 96, 533, 125], 转换后坐标=[[480, 96], [533, 96], [533, 125], [480, 125]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=7.jpeg, 置信度=0.9887283444404602, 原始坐标=[584, 91, 641, 128], 转换后坐标=[[584, 91], [641, 91], [641, 128], [584, 128]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=8,jpeg, 置信度=0.9261655211448669, 原始坐标=[698, 98, 747, 125], 转换后坐标=[[698, 98], [747, 98], [747, 125], [698, 125]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=9.jpeg, 置信度=0.9663837552070618, 原始坐标=[802, 92, 857, 128], 转换后坐标=[[802, 92], [857, 92], [857, 128], [802, 128]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=10,jpeg, 置信度=0.8957313299179077, 原始坐标=[907, 92, 969, 128], 转换后坐标=[[907, 92], [969, 92], [969, 128], [907, 128]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=11.jpeg, 置信度=0.9266167283058167, 原始坐标=[1017, 96, 1075, 125], 转换后坐标=[[1017, 96], [1075, 96], [1075, 125], [1017, 125]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9377331733703613, 原始坐标=[1111, 98, 1198, 121], 转换后坐标=[[1111, 98], [1198, 98], [1198, 121], [1111, 121]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=f24a93b2-ed84, 置信度=0.9994295835494995, 原始坐标=[1213, 98, 1309, 120], 转换后坐标=[[1213, 98], [1309, 98], [1309, 120], [1213, 120]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.978055477142334, 原始坐标=[1332, 92, 1407, 126], 转换后坐标=[[1332, 92], [1407, 92], [1407, 126], [1332, 126]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9075138568878174, 原始坐标=[1438, 92, 1516, 128], 转换后坐标=[[1438, 92], [1516, 92], [1516, 128], [1438, 128]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9448552131652832, 原始坐标=[1547, 92, 1625, 126], 转换后坐标=[[1547, 92], [1625, 92], [1625, 126], [1547, 126]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图4.jpeg, 置信度=0.922603964805603, 原始坐标=[1657, 96, 1731, 125], 转换后坐标=[[1657, 96], [1731, 96], [1731, 125], [1657, 125]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=-43a4-a1d5-2b, 置信度=0.9979790449142456, 原始坐标=[1214, 114, 1309, 138], 转换后坐标=[[1214, 114], [1309, 114], [1309, 138], [1214, 138]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=475b112391.pn, 置信度=0.9992217421531677, 原始坐标=[1210, 128, 1311, 156], 转换后坐标=[[1210, 128], [1311, 128], [1311, 156], [1210, 156]]
[15:39:53] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=g, 置信度=0.9990211725234985, 原始坐标=[1254, 155, 1269, 171], 转换后坐标=[[1254, 155], [1269, 155], [1269, 171], [1254, 171]]
[15:39:53] [    INFO] [测试3.py:1478] - 转换完成，共转换19个结果
[15:39:53] [   DEBUG] [测试3.py:1610] - OCR结果格式转换完成: [[[[[51, 98], [100, 98], [100, 125], [51, 125]], ['2.jpeg', 0.959418773651123]], [[[155, 92], [210, 92], [210, 128], [155, 128]], ['3.jpeg', 0.9454733729362488]], [[[263, 96], [317, 96], [317, 125], [263, 125]], ['4.jpeg', 0.9695303440093994]], [[[371, 92], [427, 92], [427, 128], [371, 128]], ['5.jpeg', 0.9122850298881531]], [[[480, 96], [533, 96], [533, 125], [480, 125]], ['6.jpeg', 0.9182271361351013]], [[[584, 91], [641, 91], [641, 128], [584, 128]], ['7.jpeg', 0.9887283444404602]], [[[698, 98], [747, 98], [747, 125], [698, 125]], ['8,jpeg', 0.9261655211448669]], [[[802, 92], [857, 92], [857, 128], [802, 128]], ['9.jpeg', 0.9663837552070618]], [[[907, 92], [969, 92], [969, 128], [907, 128]], ['10,jpeg', 0.8957313299179077]], [[[1017, 96], [1075, 96], [1075, 125], [1017, 125]], ['11.jpeg', 0.9266167283058167]], [[[1111, 98], [1198, 98], [1198, 121], [1111, 121]], ['800x1200.jpg', 0.9377331733703613]], [[[1213, 98], [1309, 98], [1309, 120], [1213, 120]], ['f24a93b2-ed84', 0.9994295835494995]], [[[1332, 92], [1407, 92], [1407, 126], [1332, 126]], ['主图1.jpeg', 0.978055477142334]], [[[1438, 92], [1516, 92], [1516, 128], [1438, 128]], ['主图2.jpeg', 0.9075138568878174]], [[[1547, 92], [1625, 92], [1625, 126], [1547, 126]], ['主图3.jpeg', 0.9448552131652832]], [[[1657, 96], [1731, 96], [1731, 125], [1657, 125]], ['主图4.jpeg', 0.922603964805603]], [[[1214, 114], [1309, 114], [1309, 138], [1214, 138]], ['-43a4-a1d5-2b', 0.9979790449142456]], [[[1210, 128], [1311, 128], [1311, 156], [1210, 156]], ['475b112391.pn', 0.9992217421531677]], [[[1254, 155], [1269, 155], [1269, 171], [1254, 171]], ['g', 0.9990211725234985]]]]
[15:39:53] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: '2.jpeg', 位置: (235, 211), 置信度: 0.959418773651123
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '2.jpeg'
[15:39:53] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'2.jpeg'不包含任何目标序列
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: '3.jpeg', 位置: (342, 210), 置信度: 0.9454733729362488
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '3.jpeg'
[15:39:53] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'3.jpeg'不包含任何目标序列
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: '4.jpeg', 位置: (450, 210), 置信度: 0.9695303440093994
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4.jpeg'
[15:39:53] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4.jpeg'不包含任何目标序列
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (559, 210), 置信度: 0.9122850298881531
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[15:39:53] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: '6.jpeg', 位置: (666, 210), 置信度: 0.9182271361351013
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6.jpeg'
[15:39:53] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6.jpeg'不包含任何目标序列
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: '7.jpeg', 位置: (772, 209), 置信度: 0.9887283444404602
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7.jpeg'
[15:39:53] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7.jpeg'不包含任何目标序列
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: '8,jpeg', 位置: (882, 211), 置信度: 0.9261655211448669
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8,jpeg'
[15:39:53] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8,jpeg'不包含任何目标序列
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: '9.jpeg', 位置: (989, 210), 置信度: 0.9663837552070618
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9.jpeg'
[15:39:53] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'9.jpeg'不包含任何目标序列
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: '10,jpeg', 位置: (1098, 210), 置信度: 0.8957313299179077
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '10,jpeg'
[15:39:53] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'10,jpeg'不包含任何目标序列
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: '11.jpeg', 位置: (1206, 210), 置信度: 0.9266167283058167
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '11.jpeg'
[15:39:53] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'11.jpeg'不包含任何目标序列
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (1314, 209), 置信度: 0.9377331733703613
[15:39:53] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (1314, 209)
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[15:39:53] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: 'f24a93b2-ed84', 位置: (1421, 209), 置信度: 0.9994295835494995
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'f24a93b2-ed84'
[15:39:53] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'f24'在文本'f24a93b2-ed84'中, 点击位置: (1421, 209)
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1529, 209), 置信度: 0.978055477142334
[15:39:53] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1529, 209)
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[15:39:53] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1637, 210), 置信度: 0.9075138568878174
[15:39:53] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1637, 210)
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[15:39:53] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1746, 209), 置信度: 0.9448552131652832
[15:39:53] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1746, 209)
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[15:39:53] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: '主图4.jpeg', 位置: (1854, 210), 置信度: 0.922603964805603
[15:39:53] [    INFO] [测试3.py:698] - 找到主图: 主图4.jpeg, 点击位置: (1854, 210)
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4.jpeg'
[15:39:53] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4.jpeg'不包含任何目标序列
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: '-43a4-a1d5-2b', 位置: (1421, 226), 置信度: 0.9979790449142456
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '-43a4-a1d5-2b'
[15:39:53] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'-43a4-a1d5-2b'不包含任何目标序列
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: '475b112391.pn', 位置: (1420, 242), 置信度: 0.9992217421531677
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '475b112391.pn'
[15:39:53] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'475b112391.pn'不包含任何目标序列
[15:39:53] [    INFO] [测试3.py:693] - 处理文本块: 'g', 位置: (1421, 263), 置信度: 0.9990211725234985
[15:39:53] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'g'
[15:39:53] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'g'不包含任何目标序列
[15:39:53] [    INFO] [测试3.py:722] - OCR识别统计:
[15:39:53] [    INFO] [测试3.py:723] - - 总文本块数: 19
[15:39:53] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 19
[15:39:53] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[15:39:53] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[15:39:53] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[15:39:53] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[15:39:53] [    INFO] [测试3.py:757] - 点击第1个位置: (1314, 209)
[15:39:54] [    INFO] [测试3.py:757] - 点击第2个位置: (1421, 209)
[15:39:54] [    INFO] [测试3.py:757] - 点击第3个位置: (1529, 209)
[15:39:55] [    INFO] [测试3.py:757] - 点击第4个位置: (1637, 210)
[15:39:55] [    INFO] [测试3.py:757] - 点击第5个位置: (1746, 209)
[15:39:56] [    INFO] [测试3.py:757] - 点击第6个位置: (1854, 210)
[15:39:56] [    INFO] [测试3.py:764] - 释放Ctrl键
[15:39:56] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[15:39:56] [    INFO] [测试3.py:1339] - 点击打开按钮...
[15:39:57] [    INFO] [测试3.py:1343] - 等待图片加载到图片空间...
[15:39:58] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[15:39:59] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[15:40:00] [    INFO] [测试3.py:871] - 检测到'上传中'状态，位置: Box(left=571, top=846, width=55, height=19)，继续等待2秒...
[15:40:02] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[15:40:03] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[15:40:04] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[15:40:04] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[15:40:04] [    INFO] [测试3.py:1562] - 开始OCR识别图片: logs\debug_space_screenshot.png
[15:40:04] [    INFO] [测试3.py:1579] - 当前识别场景: main
[15:40:04] [   DEBUG] [测试3.py:1582] - 正在执行OCR识别...
[15:40:06] [   DEBUG] [测试3.py:1585] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[254, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[254, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[254, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  63]], dtype=int16), array([[553,  42],
       ...,
       [552,  63]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[159,  59],
       ...,
       [158,  76]], dtype=int16), array([[294,  61],
       ...,
       [294,  79]], dtype=int16), array([[430,  59],
       ...,
       [429,  77]], dtype=int16), array([[564,  63],
       ...,
       [564,  78]], dtype=int16), array([[589, 167],
       ...,
       [589, 173]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[162, 216],
       ...,
       [161, 236]], dtype=int16), array([[285, 215],
       ...,
       [284, 237]], dtype=int16), array([[422, 217],
       ...,
       [421, 236]], dtype=int16), array([[561, 223],
       ...,
       [561, 233]], dtype=int16), array([[569, 216],
       ...,
       [568, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[297, 242],
       ...,
       [297, 254]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[568, 242],
       ...,
       [568, 254]], dtype=int16), array([[196, 273],
       ...,
       [196, 288]], dtype=int16), array([[327, 270],
       ...,
       [327, 292]], dtype=int16), array([[399, 270],
       ...,
       [399, 291]], dtype=int16), array([[607, 271],
       ...,
       [607, 290]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800×1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '24a93b2-ed84-43..', '引', '主图4.jpg', '引主图3.jpg', '引主图1.jpg', '司', '主图2.jpg', '800×800', '800×800', '800×800', '800×800', '800×800', '2', '1', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9349105954170227, 0.9546565413475037, 0.9330949783325195, 0.937903106212616, 0.9564383625984192, 0.9729751348495483, 0.9315185546875, 0.9617704749107361, 0.9476412534713745, 0.9444304704666138, 0.9519959688186646, 0.9695284366607666, 0.9655714631080627, 0.9592377543449402, 0.9438055753707886, 0.41955751180648804, 0.9688995480537415, 0.9506109952926636, 0.9704387784004211, 0.9739184379577637, 0.9739184379577637, 0.9721024632453918, 0.39803770184516907, 0.31320804357528687, 0.9352115988731384], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  63]], dtype=int16), array([[553,  42],
       ...,
       [552,  63]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[159,  59],
       ...,
       [158,  76]], dtype=int16), array([[294,  61],
       ...,
       [294,  79]], dtype=int16), array([[430,  59],
       ...,
       [429,  77]], dtype=int16), array([[564,  63],
       ...,
       [564,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[162, 216],
       ...,
       [161, 236]], dtype=int16), array([[285, 215],
       ...,
       [284, 237]], dtype=int16), array([[422, 217],
       ...,
       [421, 236]], dtype=int16), array([[561, 223],
       ...,
       [561, 233]], dtype=int16), array([[569, 216],
       ...,
       [568, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[297, 242],
       ...,
       [297, 254]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[568, 242],
       ...,
       [568, 254]], dtype=int16), array([[327, 270],
       ...,
       [327, 292]], dtype=int16), array([[399, 270],
       ...,
       [399, 291]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [444, ..., 328]], dtype=int16)}]
[15:40:06] [    INFO] [测试3.py:1553] - OCR结果已保存到: logs\result_20250828_154006_main.txt 和 logs\result_20250828_154006_main.json
[15:40:06] [    INFO] [测试3.py:1428] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[254, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[254, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[254, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  63]], dtype=int16), array([[553,  42],
       ...,
       [552,  63]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[159,  59],
       ...,
       [158,  76]], dtype=int16), array([[294,  61],
       ...,
       [294,  79]], dtype=int16), array([[430,  59],
       ...,
       [429,  77]], dtype=int16), array([[564,  63],
       ...,
       [564,  78]], dtype=int16), array([[589, 167],
       ...,
       [589, 173]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[162, 216],
       ...,
       [161, 236]], dtype=int16), array([[285, 215],
       ...,
       [284, 237]], dtype=int16), array([[422, 217],
       ...,
       [421, 236]], dtype=int16), array([[561, 223],
       ...,
       [561, 233]], dtype=int16), array([[569, 216],
       ...,
       [568, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[297, 242],
       ...,
       [297, 254]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[568, 242],
       ...,
       [568, 254]], dtype=int16), array([[196, 273],
       ...,
       [196, 288]], dtype=int16), array([[327, 270],
       ...,
       [327, 292]], dtype=int16), array([[399, 270],
       ...,
       [399, 291]], dtype=int16), array([[607, 271],
       ...,
       [607, 290]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800×1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '24a93b2-ed84-43..', '引', '主图4.jpg', '引主图3.jpg', '引主图1.jpg', '司', '主图2.jpg', '800×800', '800×800', '800×800', '800×800', '800×800', '2', '1', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9349105954170227, 0.9546565413475037, 0.9330949783325195, 0.937903106212616, 0.9564383625984192, 0.9729751348495483, 0.9315185546875, 0.9617704749107361, 0.9476412534713745, 0.9444304704666138, 0.9519959688186646, 0.9695284366607666, 0.9655714631080627, 0.9592377543449402, 0.9438055753707886, 0.41955751180648804, 0.9688995480537415, 0.9506109952926636, 0.9704387784004211, 0.9739184379577637, 0.9739184379577637, 0.9721024632453918, 0.39803770184516907, 0.31320804357528687, 0.9352115988731384], 'rec_polys': [array([[20, 41],
       ...,
       [18, 63]], dtype=int16), array([[156,  41],
       ...,
       [154,  63]], dtype=int16), array([[292,  41],
       ...,
       [291,  63]], dtype=int16), array([[428,  41],
       ...,
       [426,  63]], dtype=int16), array([[553,  42],
       ...,
       [552,  63]], dtype=int16), array([[24, 64],
       ...,
       [24, 76]], dtype=int16), array([[159,  59],
       ...,
       [158,  76]], dtype=int16), array([[294,  61],
       ...,
       [294,  79]], dtype=int16), array([[430,  59],
       ...,
       [429,  77]], dtype=int16), array([[564,  63],
       ...,
       [564,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[162, 216],
       ...,
       [161, 236]], dtype=int16), array([[285, 215],
       ...,
       [284, 237]], dtype=int16), array([[422, 217],
       ...,
       [421, 236]], dtype=int16), array([[561, 223],
       ...,
       [561, 233]], dtype=int16), array([[569, 216],
       ...,
       [568, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[297, 242],
       ...,
       [297, 254]], dtype=int16), array([[433, 242],
       ...,
       [433, 254]], dtype=int16), array([[568, 242],
       ...,
       [568, 254]], dtype=int16), array([[327, 270],
       ...,
       [327, 292]], dtype=int16), array([[399, 270],
       ...,
       [399, 291]], dtype=int16), array([[444, 307],
       ...,
       [444, 328]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [444, ..., 328]], dtype=int16)}
[15:40:06] [    INFO] [测试3.py:1435] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[553, 42], [631, 46], [630, 67], [552, 63]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[159, 59], [210, 62], [209, 78], [158, 76]], [[294, 61], [347, 61], [347, 79], [294, 79]], [[430, 59], [482, 61], [481, 79], [429, 77]], [[564, 63], [621, 63], [621, 78], [564, 78]], [[589, 167], [598, 167], [598, 173], [589, 173]], [[0, 221], [100, 221], [100, 235], [0, 235]], [[154, 223], [167, 223], [167, 233], [154, 233]], [[162, 216], [221, 220], [219, 239], [161, 236]], [[285, 215], [357, 219], [356, 240], [284, 237]], [[422, 217], [493, 221], [492, 239], [421, 236]], [[561, 223], [575, 223], [575, 233], [561, 233]], [[569, 216], [629, 220], [627, 239], [568, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[297, 242], [345, 242], [345, 254], [297, 254]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[568, 242], [616, 242], [616, 254], [568, 254]], [[196, 273], [240, 273], [240, 288], [196, 288]], [[327, 270], [380, 270], [380, 292], [327, 292]], [[399, 270], [515, 270], [515, 291], [399, 291]], [[607, 271], [651, 271], [651, 290], [607, 290]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800×1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '24a93b2-ed84-43..', '引', '主图4.jpg', '引主图3.jpg', '引主图1.jpg', '司', '主图2.jpg', '800×800', '800×800', '800×800', '800×800', '800×800', '2', '1', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9349105954170227, 0.9546565413475037, 0.9330949783325195, 0.937903106212616, 0.9564383625984192, 0.9729751348495483, 0.9315185546875, 0.9617704749107361, 0.9476412534713745, 0.9444304704666138, 0.9519959688186646, 0.9695284366607666, 0.9655714631080627, 0.9592377543449402, 0.9438055753707886, 0.41955751180648804, 0.9688995480537415, 0.9506109952926636, 0.9704387784004211, 0.9739184379577637, 0.9739184379577637, 0.9721024632453918, 0.39803770184516907, 0.31320804357528687, 0.9352115988731384], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[553, 42], [631, 46], [630, 67], [552, 63]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[159, 59], [210, 62], [209, 78], [158, 76]], [[294, 61], [347, 61], [347, 79], [294, 79]], [[430, 59], [482, 61], [481, 79], [429, 77]], [[564, 63], [621, 63], [621, 78], [564, 78]], [[0, 221], [100, 221], [100, 235], [0, 235]], [[154, 223], [167, 223], [167, 233], [154, 233]], [[162, 216], [221, 220], [219, 239], [161, 236]], [[285, 215], [357, 219], [356, 240], [284, 237]], [[422, 217], [493, 221], [492, 239], [421, 236]], [[561, 223], [575, 223], [575, 233], [561, 233]], [[569, 216], [629, 220], [627, 239], [568, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[297, 242], [345, 242], [345, 254], [297, 254]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[568, 242], [616, 242], [616, 254], [568, 254]], [[327, 270], [380, 270], [380, 292], [327, 292]], [[399, 270], [515, 270], [515, 291], [399, 291]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [291, 41, 350, 67], [426, 41, 486, 67], [552, 42, 631, 67], [24, 64, 73, 76], [158, 59, 210, 78], [294, 61, 347, 79], [429, 59, 482, 79], [564, 63, 621, 78], [0, 221, 100, 235], [154, 223, 167, 233], [161, 216, 221, 239], [284, 215, 357, 240], [421, 217, 493, 239], [561, 223, 575, 233], [568, 216, 629, 239], [24, 236, 74, 251], [161, 242, 208, 254], [297, 242, 345, 254], [433, 242, 481, 254], [568, 242, 616, 254], [327, 270, 380, 292], [399, 270, 515, 291], [444, 307, 605, 328]]}}
[15:40:06] [    INFO] [测试3.py:1444] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[553, 42], [631, 46], [630, 67], [552, 63]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[159, 59], [210, 62], [209, 78], [158, 76]], [[294, 61], [347, 61], [347, 79], [294, 79]], [[430, 59], [482, 61], [481, 79], [429, 77]], [[564, 63], [621, 63], [621, 78], [564, 78]], [[589, 167], [598, 167], [598, 173], [589, 173]], [[0, 221], [100, 221], [100, 235], [0, 235]], [[154, 223], [167, 223], [167, 233], [154, 233]], [[162, 216], [221, 220], [219, 239], [161, 236]], [[285, 215], [357, 219], [356, 240], [284, 237]], [[422, 217], [493, 221], [492, 239], [421, 236]], [[561, 223], [575, 223], [575, 233], [561, 233]], [[569, 216], [629, 220], [627, 239], [568, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[297, 242], [345, 242], [345, 254], [297, 254]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[568, 242], [616, 242], [616, 254], [568, 254]], [[196, 273], [240, 273], [240, 288], [196, 288]], [[327, 270], [380, 270], [380, 292], [327, 292]], [[399, 270], [515, 270], [515, 291], [399, 291]], [[607, 271], [651, 271], [651, 290], [607, 290]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800×1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '24a93b2-ed84-43..', '引', '主图4.jpg', '引主图3.jpg', '引主图1.jpg', '司', '主图2.jpg', '800×800', '800×800', '800×800', '800×800', '800×800', '2', '1', '①裁剪宽高比:1:1 智能裁剪'], 'rec_scores': [0.9349105954170227, 0.9546565413475037, 0.9330949783325195, 0.937903106212616, 0.9564383625984192, 0.9729751348495483, 0.9315185546875, 0.9617704749107361, 0.9476412534713745, 0.9444304704666138, 0.9519959688186646, 0.9695284366607666, 0.9655714631080627, 0.9592377543449402, 0.9438055753707886, 0.41955751180648804, 0.9688995480537415, 0.9506109952926636, 0.9704387784004211, 0.9739184379577637, 0.9739184379577637, 0.9721024632453918, 0.39803770184516907, 0.31320804357528687, 0.9352115988731384], 'rec_polys': [[[20, 41], [78, 45], [77, 67], [18, 63]], [[156, 41], [214, 45], [212, 67], [154, 63]], [[292, 41], [350, 45], [349, 67], [291, 63]], [[428, 41], [486, 45], [484, 67], [426, 63]], [[553, 42], [631, 46], [630, 67], [552, 63]], [[24, 64], [73, 64], [73, 76], [24, 76]], [[159, 59], [210, 62], [209, 78], [158, 76]], [[294, 61], [347, 61], [347, 79], [294, 79]], [[430, 59], [482, 61], [481, 79], [429, 77]], [[564, 63], [621, 63], [621, 78], [564, 78]], [[0, 221], [100, 221], [100, 235], [0, 235]], [[154, 223], [167, 223], [167, 233], [154, 233]], [[162, 216], [221, 220], [219, 239], [161, 236]], [[285, 215], [357, 219], [356, 240], [284, 237]], [[422, 217], [493, 221], [492, 239], [421, 236]], [[561, 223], [575, 223], [575, 233], [561, 233]], [[569, 216], [629, 220], [627, 239], [568, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[297, 242], [345, 242], [345, 254], [297, 254]], [[433, 242], [481, 242], [481, 254], [433, 254]], [[568, 242], [616, 242], [616, 254], [568, 254]], [[327, 270], [380, 270], [380, 292], [327, 292]], [[399, 270], [515, 270], [515, 291], [399, 291]], [[444, 307], [605, 307], [605, 328], [444, 328]]], 'rec_boxes': [[18, 41, 78, 67], [154, 41, 214, 67], [291, 41, 350, 67], [426, 41, 486, 67], [552, 42, 631, 67], [24, 64, 73, 76], [158, 59, 210, 78], [294, 61, 347, 79], [429, 59, 482, 79], [564, 63, 621, 78], [0, 221, 100, 235], [154, 223, 167, 233], [161, 216, 221, 239], [284, 215, 357, 240], [421, 217, 493, 239], [561, 223, 575, 233], [568, 216, 629, 239], [24, 236, 74, 251], [161, 242, 208, 254], [297, 242, 345, 254], [433, 242, 481, 254], [568, 242, 616, 254], [327, 270, 380, 292], [399, 270, 515, 291], [444, 307, 605, 328]]}
[15:40:06] [    INFO] [测试3.py:1452] - 识别到的文本: ['主图4.jpg', '主图3.jpg', '主图2.jpg', '主图1.jpg', '800×1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '24a93b2-ed84-43..', '引', '主图4.jpg', '引主图3.jpg', '引主图1.jpg', '司', '主图2.jpg', '800×800', '800×800', '800×800', '800×800', '800×800', '2', '1', '①裁剪宽高比:1:1 智能裁剪']
[15:40:06] [    INFO] [测试3.py:1453] - 识别的置信度: [0.9349105954170227, 0.9546565413475037, 0.9330949783325195, 0.937903106212616, 0.9564383625984192, 0.9729751348495483, 0.9315185546875, 0.9617704749107361, 0.9476412534713745, 0.9444304704666138, 0.9519959688186646, 0.9695284366607666, 0.9655714631080627, 0.9592377543449402, 0.9438055753707886, 0.41955751180648804, 0.9688995480537415, 0.9506109952926636, 0.9704387784004211, 0.9739184379577637, 0.9739184379577637, 0.9721024632453918, 0.39803770184516907, 0.31320804357528687, 0.9352115988731384]
[15:40:06] [    INFO] [测试3.py:1454] - 识别的坐标框: [[18, 41, 78, 67], [154, 41, 214, 67], [291, 41, 350, 67], [426, 41, 486, 67], [552, 42, 631, 67], [24, 64, 73, 76], [158, 59, 210, 78], [294, 61, 347, 79], [429, 59, 482, 79], [564, 63, 621, 78], [0, 221, 100, 235], [154, 223, 167, 233], [161, 216, 221, 239], [284, 215, 357, 240], [421, 217, 493, 239], [561, 223, 575, 233], [568, 216, 629, 239], [24, 236, 74, 251], [161, 242, 208, 254], [297, 242, 345, 254], [433, 242, 481, 254], [568, 242, 616, 254], [327, 270, 380, 292], [399, 270, 515, 291], [444, 307, 605, 328]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9349105954170227, 原始坐标=[18, 41, 78, 67], 转换后坐标=[[18, 41], [78, 41], [78, 67], [18, 67]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9546565413475037, 原始坐标=[154, 41, 214, 67], 转换后坐标=[[154, 41], [214, 41], [214, 67], [154, 67]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9330949783325195, 原始坐标=[291, 41, 350, 67], 转换后坐标=[[291, 41], [350, 41], [350, 67], [291, 67]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图1.jpg, 置信度=0.937903106212616, 原始坐标=[426, 41, 486, 67], 转换后坐标=[[426, 41], [486, 41], [486, 67], [426, 67]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×1200.jpg, 置信度=0.9564383625984192, 原始坐标=[552, 42, 631, 67], 转换后坐标=[[552, 42], [631, 42], [631, 67], [552, 67]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9729751348495483, 原始坐标=[24, 64, 73, 76], 转换后坐标=[[24, 64], [73, 64], [73, 76], [24, 76]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9315185546875, 原始坐标=[158, 59, 210, 78], 转换后坐标=[[158, 59], [210, 59], [210, 78], [158, 78]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9617704749107361, 原始坐标=[294, 61, 347, 79], 转换后坐标=[[294, 61], [347, 61], [347, 79], [294, 79]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9476412534713745, 原始坐标=[429, 59, 482, 79], 转换后坐标=[[429, 59], [482, 59], [482, 79], [429, 79]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×1200, 置信度=0.9444304704666138, 原始坐标=[564, 63, 621, 78], 转换后坐标=[[564, 63], [621, 63], [621, 78], [564, 78]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=24a93b2-ed84-43.., 置信度=0.9519959688186646, 原始坐标=[0, 221, 100, 235], 转换后坐标=[[0, 221], [100, 221], [100, 235], [0, 235]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=引, 置信度=0.9695284366607666, 原始坐标=[154, 223, 167, 233], 转换后坐标=[[154, 223], [167, 223], [167, 233], [154, 233]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9655714631080627, 原始坐标=[161, 216, 221, 239], 转换后坐标=[[161, 216], [221, 216], [221, 239], [161, 239]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=引主图3.jpg, 置信度=0.9592377543449402, 原始坐标=[284, 215, 357, 240], 转换后坐标=[[284, 215], [357, 215], [357, 240], [284, 240]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=引主图1.jpg, 置信度=0.9438055753707886, 原始坐标=[421, 217, 493, 239], 转换后坐标=[[421, 217], [493, 217], [493, 239], [421, 239]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=司, 置信度=0.41955751180648804, 原始坐标=[561, 223, 575, 233], 转换后坐标=[[561, 223], [575, 223], [575, 233], [561, 233]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图2.jpg, 置信度=0.9688995480537415, 原始坐标=[568, 216, 629, 239], 转换后坐标=[[568, 216], [629, 216], [629, 239], [568, 239]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[24, 236, 74, 251], 转换后坐标=[[24, 236], [74, 236], [74, 251], [24, 251]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9704387784004211, 原始坐标=[161, 242, 208, 254], 转换后坐标=[[161, 242], [208, 242], [208, 254], [161, 254]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9739184379577637, 原始坐标=[297, 242, 345, 254], 转换后坐标=[[297, 242], [345, 242], [345, 254], [297, 254]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9739184379577637, 原始坐标=[433, 242, 481, 254], 转换后坐标=[[433, 242], [481, 242], [481, 254], [433, 254]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9721024632453918, 原始坐标=[568, 242, 616, 254], 转换后坐标=[[568, 242], [616, 242], [616, 254], [568, 254]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=2, 置信度=0.39803770184516907, 原始坐标=[327, 270, 380, 292], 转换后坐标=[[327, 270], [380, 270], [380, 292], [327, 292]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=1, 置信度=0.31320804357528687, 原始坐标=[399, 270, 515, 291], 转换后坐标=[[399, 270], [515, 270], [515, 291], [399, 291]]
[15:40:06] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=①裁剪宽高比:1:1 智能裁剪, 置信度=0.9352115988731384, 原始坐标=[444, 307, 605, 328], 转换后坐标=[[444, 307], [605, 307], [605, 328], [444, 328]]
[15:40:06] [    INFO] [测试3.py:1478] - 转换完成，共转换25个结果
[15:40:06] [   DEBUG] [测试3.py:1610] - OCR结果格式转换完成: [[[[[18, 41], [78, 41], [78, 67], [18, 67]], ['主图4.jpg', 0.9349105954170227]], [[[154, 41], [214, 41], [214, 67], [154, 67]], ['主图3.jpg', 0.9546565413475037]], [[[291, 41], [350, 41], [350, 67], [291, 67]], ['主图2.jpg', 0.9330949783325195]], [[[426, 41], [486, 41], [486, 67], [426, 67]], ['主图1.jpg', 0.937903106212616]], [[[552, 42], [631, 42], [631, 67], [552, 67]], ['800×1200.jpg', 0.9564383625984192]], [[[24, 64], [73, 64], [73, 76], [24, 76]], ['800×800', 0.9729751348495483]], [[[158, 59], [210, 59], [210, 78], [158, 78]], ['800×800', 0.9315185546875]], [[[294, 61], [347, 61], [347, 79], [294, 79]], ['800×800', 0.9617704749107361]], [[[429, 59], [482, 59], [482, 79], [429, 79]], ['800×800', 0.9476412534713745]], [[[564, 63], [621, 63], [621, 78], [564, 78]], ['800×1200', 0.9444304704666138]], [[[0, 221], [100, 221], [100, 235], [0, 235]], ['24a93b2-ed84-43..', 0.9519959688186646]], [[[154, 223], [167, 223], [167, 233], [154, 233]], ['引', 0.9695284366607666]], [[[161, 216], [221, 216], [221, 239], [161, 239]], ['主图4.jpg', 0.9655714631080627]], [[[284, 215], [357, 215], [357, 240], [284, 240]], ['引主图3.jpg', 0.9592377543449402]], [[[421, 217], [493, 217], [493, 239], [421, 239]], ['引主图1.jpg', 0.9438055753707886]], [[[561, 223], [575, 223], [575, 233], [561, 233]], ['司', 0.41955751180648804]], [[[568, 216], [629, 216], [629, 239], [568, 239]], ['主图2.jpg', 0.9688995480537415]], [[[24, 236], [74, 236], [74, 251], [24, 251]], ['800×800', 0.9506109952926636]], [[[161, 242], [208, 242], [208, 254], [161, 254]], ['800×800', 0.9704387784004211]], [[[297, 242], [345, 242], [345, 254], [297, 254]], ['800×800', 0.9739184379577637]], [[[433, 242], [481, 242], [481, 254], [433, 254]], ['800×800', 0.9739184379577637]], [[[568, 242], [616, 242], [616, 254], [568, 254]], ['800×800', 0.9721024632453918]], [[[327, 270], [380, 270], [380, 292], [327, 292]], ['2', 0.39803770184516907]], [[[399, 270], [515, 270], [515, 291], [399, 291]], ['1', 0.31320804357528687]], [[[444, 307], [605, 307], [605, 328], [444, 328]], ['①裁剪宽高比:1:1 智能裁剪', 0.9352115988731384]]]]
[15:40:06] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9349105954170227
[15:40:06] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (864, 604), 置信度: 0.9546565413475037
[15:40:06] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1000, 604), 置信度: 0.9330949783325195
[15:40:06] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1136, 604), 置信度: 0.937903106212616
[15:40:06] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (871, 777), 置信度: 0.9655714631080627
[15:40:06] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (1278, 777), 置信度: 0.9688995480537415
[15:40:06] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1136, 534)
[15:40:08] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (1000, 534)
[15:40:09] [    INFO] [测试3.py:978] - 主图2已经点击过，跳过
[15:40:09] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (864, 534)
[15:40:10] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[15:40:11] [    INFO] [测试3.py:973] - 已达到最大点击次数(4次)，停止点击
[15:40:11] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[15:40:11] [    INFO] [测试3.py:1371] - 点击右侧空白处关闭图片空间...
[15:40:12] [    INFO] [测试3.py:1376] - 点击裁剪按钮...
[15:40:13] [    INFO] [测试3.py:1383] - 开始上传UUID透明图...
[15:40:15] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[15:40:15] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[15:40:19] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[15:40:19] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[15:40:19] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250828_154019.png
[15:40:19] [    INFO] [测试3.py:1094] - 目标UUID: f24a93b2-ed84-43a4-a1d5-2b475b112391.png
[15:40:19] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['f24', '24a', '4a9', 'a93', '93b', '3b2']
[15:40:19] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[15:40:21] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250828_154019.json
[15:40:21] [    INFO] [测试3.py:1120] - 识别到 23 个文本块
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (71, 96), 置信度: 0.9348
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (170, 96), 置信度: 0.9446
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (281, 96), 置信度: 0.9746
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (391, 96), 置信度: 0.9778
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (500, 97), 置信度: 0.9698
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (610, 97), 置信度: 0.9805
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (36, 122), 置信度: 0.9850
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (146, 123), 置信度: 0.9113
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (256, 123), 置信度: 0.9415
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (366, 123), 置信度: 0.9489
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (476, 122), 置信度: 0.9874
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200.jpg, 位置: (598, 122), 置信度: 0.9837
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (60, 239), 置信度: 0.9866
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (171, 238), 置信度: 0.9674
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (281, 238), 置信度: 0.9760
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (390, 238), 置信度: 0.9793
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (500, 238), 置信度: 0.9793
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: 900x960, 位置: (610, 238), 置信度: 0.9492
[15:40:21] [    INFO] [测试3.py:1126] - 识别到文本块: f24a93b2-ed84...主图1.jpg, 位置: (91, 264), 置信度: 0.9393
[15:40:21] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'f24'在文本'f24a93b2-ed84...主图1.jpg'中
[15:40:21] [    INFO] [测试3.py:1139] - 计算的点击位置: (861, 764)
[15:40:21] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250828_154019
[15:40:23] [    INFO] [测试3.py:1400] - 开始上传800x1200图片...
[15:40:24] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[15:40:25] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[15:40:28] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[15:40:28] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[15:40:28] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250828_154028.png
[15:40:28] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[15:40:30] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250828_154028.json
[15:40:30] [    INFO] [测试3.py:1233] - 识别到 23 个文本块
[15:40:30] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (62, 99), 置信度: 0.8859
[15:40:30] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (172, 100), 置信度: 0.9761
[15:40:30] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (282, 101), 置信度: 0.9666
[15:40:30] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (392, 100), 置信度: 0.9766
[15:40:30] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (502, 100), 置信度: 0.9782
[15:40:30] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (612, 100), 置信度: 0.9759
[15:40:30] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[15:40:30] [    INFO] [测试3.py:1251] - 计算的点击位置: (1242, 596)
[15:40:30] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250828_154028
[15:40:32] [    INFO] [测试3.py:1413] - 向下滚动页面...
[15:40:33] [    INFO] [测试3.py:1417] - 文件选择完成，上传流程结束
[15:40:33] [    INFO] [测试3.py:1652] - 已创建测试3完成信号文件
[15:45:10] [    INFO] [测试3.py:80] - ==================================================
[15:45:10] [    INFO] [测试3.py:81] - 日志系统初始化完成
[15:45:10] [    INFO] [测试3.py:82] - 日志文件路径: logs\测试3_20250828.log
[15:45:10] [    INFO] [测试3.py:83] - ==================================================
[15:45:10] [    INFO] [测试3.py:101] - 测试3.py开始执行...
[15:45:10] [    INFO] [测试3.py:113] - pyautogui设置完成
[15:45:10] [    INFO] [测试3.py:228] - 开始初始化PaddleOCR...
[15:45:16] [    INFO] [测试3.py:281] - 初始化PaddleOCR完成
[15:45:16] [    INFO] [测试3.py:169] - 成功加载图片配置:
[15:45:16] [    INFO] [测试3.py:170] - 主图: ['800x1200.jpg', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4.jpeg']
[15:45:16] [    INFO] [测试3.py:171] - 详情图: ['10.jpeg', '11.jpeg', '4.jpeg', '5.jpeg', '6.jpeg', '7.jpeg', '8.jpeg', '9.jpeg']
[15:45:16] [    INFO] [测试3.py:172] - UUID图片: e7bbaed6-54de-4040-8765-dbedd2ff03bf.png
[15:45:16] [    INFO] [测试3.py:191] - 从current_foldername.json读取文件夹名称: 3
[15:45:16] [    INFO] [测试3.py:213] - 初始鼠标位置: (320, 123)
[15:45:16] [    INFO] [测试3.py:1274] - 开始上传流程...
[15:45:16] [    INFO] [测试3.py:1283] - 移动到指定坐标: (505, 306)
[15:45:17] [    INFO] [测试3.py:1287] - 已移动到目标坐标，等待0.5秒...
[15:45:17] [    INFO] [测试3.py:1289] - 准备点击坐标...
[15:45:17] [    INFO] [测试3.py:1291] - 已点击坐标
[15:45:21] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:45:21] [ WARNING] [测试3.py:1309] - 未找到SHANGCHUANG.png，等待1.2秒后重试
[15:45:22] [   ERROR] [测试3.py:364] - 查找点击图片出错: 
[15:45:22] [ WARNING] [测试3.py:1312] - 重试后仍未找到上传按钮，继续执行后续步骤
[15:45:26] [    INFO] [测试3.py:1324] - 开始查找3号文件夹...
[15:45:26] [    INFO] [测试3.py:384] - 开始查找文件夹: 3
[15:45:27] [    INFO] [测试3.py:416] - 文件夹3的目标坐标: (209, 183)
[15:45:27] [    INFO] [测试3.py:421] - 当前鼠标位置: (93, 199)
[15:45:27] [    INFO] [测试3.py:444] - 鼠标已移动到文件夹位置: (209, 183)
[15:45:27] [    INFO] [测试3.py:451] - 执行点击 #1
[15:45:28] [    INFO] [测试3.py:451] - 执行点击 #2
[15:45:29] [    INFO] [测试3.py:457] - 等待文件夹打开...
[15:45:29] [    INFO] [测试3.py:464] - 设置当前文件夹路径: 商品信息\3
[15:45:29] [    INFO] [测试3.py:467] - 移动鼠标到(108,600)位置...
[15:45:30] [    INFO] [测试3.py:1329] - 移动鼠标到(108,600)位置...
[15:45:31] [    INFO] [测试3.py:635] - 开始选择图片...
[15:45:33] [    INFO] [测试3.py:644] - 截图区域: 左上角(160, 100), 宽度1740, 高度800
[15:45:33] [    INFO] [测试3.py:655] - 目标UUID: e7bbaed6-54de-4040-8765-dbedd2ff03bf.png
[15:45:33] [    INFO] [测试3.py:656] - 生成的UUID匹配序列: ['e7b', '7bb', 'bba', 'bae', 'aed', 'ed6']
[15:45:33] [    INFO] [测试3.py:1562] - 开始OCR识别图片: logs\debug_files_screenshot.png
[15:45:35] [    INFO] [测试3.py:1579] - 当前识别场景: unknown
[15:45:35] [   DEBUG] [测试3.py:1582] - 正在执行OCR识别...
[15:45:37] [   DEBUG] [测试3.py:1585] - OCR原始返回结果: [{'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[145,  36],
       ...,
       [145,  54]], dtype=int16), array([[ 51,  98],
       ...,
       [ 51, 121]], dtype=int16), array([[158,  92],
       ...,
       [155, 121]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 119]], dtype=int16), array([[482,  96],
       ...,
       [479, 119]], dtype=int16), array([[591,  96],
       ...,
       [588, 119]], dtype=int16), array([[696,  96],
       ...,
       [693, 119]], dtype=int16), array([[802,  92],
       ...,
       [798, 120]], dtype=int16), array([[895,  98],
       ...,
       [895, 121]], dtype=int16), array([[999,  98],
       ...,
       [999, 120]], dtype=int16), array([[1117,   96],
       ...,
       [1116,  119]], dtype=int16), array([[1227,   94],
       ...,
       [1224,  117]], dtype=int16), array([[1333,   94],
       ...,
       [1332,  121]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[998, 110],
       ...,
       [996, 134]], dtype=int16), array([[996, 128],
       ...,
       [995, 151]], dtype=int16), array([[1039,  154],
       ...,
       [1039,  171]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['4.jpeg', '5.jpeg', '6,jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '800x1200.jpg', 'e7bbaed6-54d', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', 'e-4040-8765-d', 'bedd2ff03bf.pn', 'g'], 'rec_scores': [0.9939669966697693, 0.9417853355407715, 0.93913334608078, 0.9854061007499695, 0.988820493221283, 0.9855602383613586, 0.994547963142395, 0.8855990171432495, 0.923505961894989, 0.9994044899940491, 0.9721140265464783, 0.9917568564414978, 0.9388467073440552, 0.9319921135902405, 0.9782481789588928, 0.9133968353271484, 0.9956187605857849], 'rec_polys': [array([[ 51,  98],
       ...,
       [ 51, 121]], dtype=int16), array([[158,  92],
       ...,
       [155, 121]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 119]], dtype=int16), array([[482,  96],
       ...,
       [479, 119]], dtype=int16), array([[591,  96],
       ...,
       [588, 119]], dtype=int16), array([[696,  96],
       ...,
       [693, 119]], dtype=int16), array([[802,  92],
       ...,
       [798, 120]], dtype=int16), array([[895,  98],
       ...,
       [895, 121]], dtype=int16), array([[999,  98],
       ...,
       [999, 120]], dtype=int16), array([[1117,   96],
       ...,
       [1116,  119]], dtype=int16), array([[1227,   94],
       ...,
       [1224,  117]], dtype=int16), array([[1333,   94],
       ...,
       [1332,  121]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[998, 110],
       ...,
       [996, 134]], dtype=int16), array([[996, 128],
       ...,
       [995, 151]], dtype=int16), array([[1039,  154],
       ...,
       [1039,  171]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[  51, ...,  121],
       ...,
       [1039, ...,  171]], dtype=int16)}]
[15:45:37] [    INFO] [测试3.py:1553] - OCR结果已保存到: logs\result_20250828_154537_unknown.txt 和 logs\result_20250828_154537_unknown.json
[15:45:37] [    INFO] [测试3.py:1428] - OCR原始结果: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[255, ..., 255],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[145,  36],
       ...,
       [145,  54]], dtype=int16), array([[ 51,  98],
       ...,
       [ 51, 121]], dtype=int16), array([[158,  92],
       ...,
       [155, 121]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 119]], dtype=int16), array([[482,  96],
       ...,
       [479, 119]], dtype=int16), array([[591,  96],
       ...,
       [588, 119]], dtype=int16), array([[696,  96],
       ...,
       [693, 119]], dtype=int16), array([[802,  92],
       ...,
       [798, 120]], dtype=int16), array([[895,  98],
       ...,
       [895, 121]], dtype=int16), array([[999,  98],
       ...,
       [999, 120]], dtype=int16), array([[1117,   96],
       ...,
       [1116,  119]], dtype=int16), array([[1227,   94],
       ...,
       [1224,  117]], dtype=int16), array([[1333,   94],
       ...,
       [1332,  121]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[998, 110],
       ...,
       [996, 134]], dtype=int16), array([[996, 128],
       ...,
       [995, 151]], dtype=int16), array([[1039,  154],
       ...,
       [1039,  171]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['4.jpeg', '5.jpeg', '6,jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '800x1200.jpg', 'e7bbaed6-54d', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', 'e-4040-8765-d', 'bedd2ff03bf.pn', 'g'], 'rec_scores': [0.9939669966697693, 0.9417853355407715, 0.93913334608078, 0.9854061007499695, 0.988820493221283, 0.9855602383613586, 0.994547963142395, 0.8855990171432495, 0.923505961894989, 0.9994044899940491, 0.9721140265464783, 0.9917568564414978, 0.9388467073440552, 0.9319921135902405, 0.9782481789588928, 0.9133968353271484, 0.9956187605857849], 'rec_polys': [array([[ 51,  98],
       ...,
       [ 51, 121]], dtype=int16), array([[158,  92],
       ...,
       [155, 121]], dtype=int16), array([[266,  98],
       ...,
       [266, 125]], dtype=int16), array([[375,  96],
       ...,
       [372, 119]], dtype=int16), array([[482,  96],
       ...,
       [479, 119]], dtype=int16), array([[591,  96],
       ...,
       [588, 119]], dtype=int16), array([[696,  96],
       ...,
       [693, 119]], dtype=int16), array([[802,  92],
       ...,
       [798, 120]], dtype=int16), array([[895,  98],
       ...,
       [895, 121]], dtype=int16), array([[999,  98],
       ...,
       [999, 120]], dtype=int16), array([[1117,   96],
       ...,
       [1116,  119]], dtype=int16), array([[1227,   94],
       ...,
       [1224,  117]], dtype=int16), array([[1333,   94],
       ...,
       [1332,  121]], dtype=int16), array([[1442,   96],
       ...,
       [1440,  119]], dtype=int16), array([[998, 110],
       ...,
       [996, 134]], dtype=int16), array([[996, 128],
       ...,
       [995, 151]], dtype=int16), array([[1039,  154],
       ...,
       [1039,  171]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[  51, ...,  121],
       ...,
       [1039, ...,  171]], dtype=int16)}
[15:45:37] [    INFO] [测试3.py:1435] - OCR结果json: {'res': {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[145, 36], [169, 36], [169, 54], [145, 54]], [[51, 98], [98, 98], [98, 121], [51, 121]], [[158, 92], [206, 97], [203, 126], [155, 121]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [421, 124], [372, 119]], [[482, 96], [530, 101], [528, 124], [479, 119]], [[591, 96], [637, 101], [634, 124], [588, 119]], [[696, 96], [749, 101], [747, 124], [693, 119]], [[802, 92], [860, 99], [857, 128], [798, 120]], [[895, 98], [981, 98], [981, 121], [895, 121]], [[999, 98], [1095, 98], [1095, 120], [999, 120]], [[1117, 96], [1190, 100], [1188, 124], [1116, 119]], [[1227, 94], [1299, 101], [1296, 124], [1224, 117]], [[1333, 94], [1407, 99], [1405, 126], [1332, 121]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[998, 110], [1095, 115], [1094, 138], [996, 134]], [[996, 128], [1095, 132], [1094, 156], [995, 151]], [[1039, 154], [1057, 154], [1057, 171], [1039, 171]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['4.jpeg', '5.jpeg', '6,jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '800x1200.jpg', 'e7bbaed6-54d', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', 'e-4040-8765-d', 'bedd2ff03bf.pn', 'g'], 'rec_scores': [0.9939669966697693, 0.9417853355407715, 0.93913334608078, 0.9854061007499695, 0.988820493221283, 0.9855602383613586, 0.994547963142395, 0.8855990171432495, 0.923505961894989, 0.9994044899940491, 0.9721140265464783, 0.9917568564414978, 0.9388467073440552, 0.9319921135902405, 0.9782481789588928, 0.9133968353271484, 0.9956187605857849], 'rec_polys': [[[51, 98], [98, 98], [98, 121], [51, 121]], [[158, 92], [206, 97], [203, 126], [155, 121]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [421, 124], [372, 119]], [[482, 96], [530, 101], [528, 124], [479, 119]], [[591, 96], [637, 101], [634, 124], [588, 119]], [[696, 96], [749, 101], [747, 124], [693, 119]], [[802, 92], [860, 99], [857, 128], [798, 120]], [[895, 98], [981, 98], [981, 121], [895, 121]], [[999, 98], [1095, 98], [1095, 120], [999, 120]], [[1117, 96], [1190, 100], [1188, 124], [1116, 119]], [[1227, 94], [1299, 101], [1296, 124], [1224, 117]], [[1333, 94], [1407, 99], [1405, 126], [1332, 121]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[998, 110], [1095, 115], [1094, 138], [996, 134]], [[996, 128], [1095, 132], [1094, 156], [995, 151]], [[1039, 154], [1057, 154], [1057, 171], [1039, 171]]], 'rec_boxes': [[51, 98, 98, 121], [155, 92, 206, 126], [266, 98, 315, 125], [372, 96, 423, 124], [479, 96, 530, 124], [588, 96, 637, 124], [693, 96, 749, 124], [798, 92, 860, 128], [895, 98, 981, 121], [999, 98, 1095, 120], [1116, 96, 1190, 124], [1224, 94, 1299, 124], [1332, 94, 1407, 126], [1440, 96, 1514, 124], [996, 110, 1095, 138], [995, 128, 1095, 156], [1039, 154, 1057, 171]]}}
[15:45:37] [    INFO] [测试3.py:1444] - res_data内容: {'input_path': 'logs\\debug_files_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[145, 36], [169, 36], [169, 54], [145, 54]], [[51, 98], [98, 98], [98, 121], [51, 121]], [[158, 92], [206, 97], [203, 126], [155, 121]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [421, 124], [372, 119]], [[482, 96], [530, 101], [528, 124], [479, 119]], [[591, 96], [637, 101], [634, 124], [588, 119]], [[696, 96], [749, 101], [747, 124], [693, 119]], [[802, 92], [860, 99], [857, 128], [798, 120]], [[895, 98], [981, 98], [981, 121], [895, 121]], [[999, 98], [1095, 98], [1095, 120], [999, 120]], [[1117, 96], [1190, 100], [1188, 124], [1116, 119]], [[1227, 94], [1299, 101], [1296, 124], [1224, 117]], [[1333, 94], [1407, 99], [1405, 126], [1332, 121]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[998, 110], [1095, 115], [1094, 138], [996, 134]], [[996, 128], [1095, 132], [1094, 156], [995, 151]], [[1039, 154], [1057, 154], [1057, 171], [1039, 171]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['4.jpeg', '5.jpeg', '6,jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '800x1200.jpg', 'e7bbaed6-54d', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', 'e-4040-8765-d', 'bedd2ff03bf.pn', 'g'], 'rec_scores': [0.9939669966697693, 0.9417853355407715, 0.93913334608078, 0.9854061007499695, 0.988820493221283, 0.9855602383613586, 0.994547963142395, 0.8855990171432495, 0.923505961894989, 0.9994044899940491, 0.9721140265464783, 0.9917568564414978, 0.9388467073440552, 0.9319921135902405, 0.9782481789588928, 0.9133968353271484, 0.9956187605857849], 'rec_polys': [[[51, 98], [98, 98], [98, 121], [51, 121]], [[158, 92], [206, 97], [203, 126], [155, 121]], [[266, 98], [315, 98], [315, 125], [266, 125]], [[375, 96], [423, 101], [421, 124], [372, 119]], [[482, 96], [530, 101], [528, 124], [479, 119]], [[591, 96], [637, 101], [634, 124], [588, 119]], [[696, 96], [749, 101], [747, 124], [693, 119]], [[802, 92], [860, 99], [857, 128], [798, 120]], [[895, 98], [981, 98], [981, 121], [895, 121]], [[999, 98], [1095, 98], [1095, 120], [999, 120]], [[1117, 96], [1190, 100], [1188, 124], [1116, 119]], [[1227, 94], [1299, 101], [1296, 124], [1224, 117]], [[1333, 94], [1407, 99], [1405, 126], [1332, 121]], [[1442, 96], [1514, 100], [1512, 124], [1440, 119]], [[998, 110], [1095, 115], [1094, 138], [996, 134]], [[996, 128], [1095, 132], [1094, 156], [995, 151]], [[1039, 154], [1057, 154], [1057, 171], [1039, 171]]], 'rec_boxes': [[51, 98, 98, 121], [155, 92, 206, 126], [266, 98, 315, 125], [372, 96, 423, 124], [479, 96, 530, 124], [588, 96, 637, 124], [693, 96, 749, 124], [798, 92, 860, 128], [895, 98, 981, 121], [999, 98, 1095, 120], [1116, 96, 1190, 124], [1224, 94, 1299, 124], [1332, 94, 1407, 126], [1440, 96, 1514, 124], [996, 110, 1095, 138], [995, 128, 1095, 156], [1039, 154, 1057, 171]]}
[15:45:37] [    INFO] [测试3.py:1452] - 识别到的文本: ['4.jpeg', '5.jpeg', '6,jpeg', '7.jpeg', '8.jpeg', '9.jpeg', '10.jpeg', '11.jpeg', '800x1200.jpg', 'e7bbaed6-54d', '主图1.jpeg', '主图2.jpeg', '主图3.jpeg', '主图4jpeg', 'e-4040-8765-d', 'bedd2ff03bf.pn', 'g']
[15:45:37] [    INFO] [测试3.py:1453] - 识别的置信度: [0.9939669966697693, 0.9417853355407715, 0.93913334608078, 0.9854061007499695, 0.988820493221283, 0.9855602383613586, 0.994547963142395, 0.8855990171432495, 0.923505961894989, 0.9994044899940491, 0.9721140265464783, 0.9917568564414978, 0.9388467073440552, 0.9319921135902405, 0.9782481789588928, 0.9133968353271484, 0.9956187605857849]
[15:45:37] [    INFO] [测试3.py:1454] - 识别的坐标框: [[51, 98, 98, 121], [155, 92, 206, 126], [266, 98, 315, 125], [372, 96, 423, 124], [479, 96, 530, 124], [588, 96, 637, 124], [693, 96, 749, 124], [798, 92, 860, 128], [895, 98, 981, 121], [999, 98, 1095, 120], [1116, 96, 1190, 124], [1224, 94, 1299, 124], [1332, 94, 1407, 126], [1440, 96, 1514, 124], [996, 110, 1095, 138], [995, 128, 1095, 156], [1039, 154, 1057, 171]]
[15:45:37] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=4.jpeg, 置信度=0.9939669966697693, 原始坐标=[51, 98, 98, 121], 转换后坐标=[[51, 98], [98, 98], [98, 121], [51, 121]]
[15:45:37] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=5.jpeg, 置信度=0.9417853355407715, 原始坐标=[155, 92, 206, 126], 转换后坐标=[[155, 92], [206, 92], [206, 126], [155, 126]]
[15:45:37] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=6,jpeg, 置信度=0.93913334608078, 原始坐标=[266, 98, 315, 125], 转换后坐标=[[266, 98], [315, 98], [315, 125], [266, 125]]
[15:45:37] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=7.jpeg, 置信度=0.9854061007499695, 原始坐标=[372, 96, 423, 124], 转换后坐标=[[372, 96], [423, 96], [423, 124], [372, 124]]
[15:45:37] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=8.jpeg, 置信度=0.988820493221283, 原始坐标=[479, 96, 530, 124], 转换后坐标=[[479, 96], [530, 96], [530, 124], [479, 124]]
[15:45:37] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=9.jpeg, 置信度=0.9855602383613586, 原始坐标=[588, 96, 637, 124], 转换后坐标=[[588, 96], [637, 96], [637, 124], [588, 124]]
[15:45:37] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=10.jpeg, 置信度=0.994547963142395, 原始坐标=[693, 96, 749, 124], 转换后坐标=[[693, 96], [749, 96], [749, 124], [693, 124]]
[15:45:37] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=11.jpeg, 置信度=0.8855990171432495, 原始坐标=[798, 92, 860, 128], 转换后坐标=[[798, 92], [860, 92], [860, 128], [798, 128]]
[15:45:37] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.923505961894989, 原始坐标=[895, 98, 981, 121], 转换后坐标=[[895, 98], [981, 98], [981, 121], [895, 121]]
[15:45:37] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=e7bbaed6-54d, 置信度=0.9994044899940491, 原始坐标=[999, 98, 1095, 120], 转换后坐标=[[999, 98], [1095, 98], [1095, 120], [999, 120]]
[15:45:37] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图1.jpeg, 置信度=0.9721140265464783, 原始坐标=[1116, 96, 1190, 124], 转换后坐标=[[1116, 96], [1190, 96], [1190, 124], [1116, 124]]
[15:45:37] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图2.jpeg, 置信度=0.9917568564414978, 原始坐标=[1224, 94, 1299, 124], 转换后坐标=[[1224, 94], [1299, 94], [1299, 124], [1224, 124]]
[15:45:37] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图3.jpeg, 置信度=0.9388467073440552, 原始坐标=[1332, 94, 1407, 126], 转换后坐标=[[1332, 94], [1407, 94], [1407, 126], [1332, 126]]
[15:45:37] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图4jpeg, 置信度=0.9319921135902405, 原始坐标=[1440, 96, 1514, 124], 转换后坐标=[[1440, 96], [1514, 96], [1514, 124], [1440, 124]]
[15:45:37] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=e-4040-8765-d, 置信度=0.9782481789588928, 原始坐标=[996, 110, 1095, 138], 转换后坐标=[[996, 110], [1095, 110], [1095, 138], [996, 138]]
[15:45:37] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=bedd2ff03bf.pn, 置信度=0.9133968353271484, 原始坐标=[995, 128, 1095, 156], 转换后坐标=[[995, 128], [1095, 128], [1095, 156], [995, 156]]
[15:45:37] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=g, 置信度=0.9956187605857849, 原始坐标=[1039, 154, 1057, 171], 转换后坐标=[[1039, 154], [1057, 154], [1057, 171], [1039, 171]]
[15:45:37] [    INFO] [测试3.py:1478] - 转换完成，共转换17个结果
[15:45:37] [   DEBUG] [测试3.py:1610] - OCR结果格式转换完成: [[[[[51, 98], [98, 98], [98, 121], [51, 121]], ['4.jpeg', 0.9939669966697693]], [[[155, 92], [206, 92], [206, 126], [155, 126]], ['5.jpeg', 0.9417853355407715]], [[[266, 98], [315, 98], [315, 125], [266, 125]], ['6,jpeg', 0.93913334608078]], [[[372, 96], [423, 96], [423, 124], [372, 124]], ['7.jpeg', 0.9854061007499695]], [[[479, 96], [530, 96], [530, 124], [479, 124]], ['8.jpeg', 0.988820493221283]], [[[588, 96], [637, 96], [637, 124], [588, 124]], ['9.jpeg', 0.9855602383613586]], [[[693, 96], [749, 96], [749, 124], [693, 124]], ['10.jpeg', 0.994547963142395]], [[[798, 92], [860, 92], [860, 128], [798, 128]], ['11.jpeg', 0.8855990171432495]], [[[895, 98], [981, 98], [981, 121], [895, 121]], ['800x1200.jpg', 0.923505961894989]], [[[999, 98], [1095, 98], [1095, 120], [999, 120]], ['e7bbaed6-54d', 0.9994044899940491]], [[[1116, 96], [1190, 96], [1190, 124], [1116, 124]], ['主图1.jpeg', 0.9721140265464783]], [[[1224, 94], [1299, 94], [1299, 124], [1224, 124]], ['主图2.jpeg', 0.9917568564414978]], [[[1332, 94], [1407, 94], [1407, 126], [1332, 126]], ['主图3.jpeg', 0.9388467073440552]], [[[1440, 96], [1514, 96], [1514, 124], [1440, 124]], ['主图4jpeg', 0.9319921135902405]], [[[996, 110], [1095, 110], [1095, 138], [996, 138]], ['e-4040-8765-d', 0.9782481789588928]], [[[995, 128], [1095, 128], [1095, 156], [995, 156]], ['bedd2ff03bf.pn', 0.9133968353271484]], [[[1039, 154], [1057, 154], [1057, 171], [1039, 171]], ['g', 0.9956187605857849]]]]
[15:45:37] [    INFO] [测试3.py:667] - PaddleOCR识别完成
[15:45:37] [    INFO] [测试3.py:693] - 处理文本块: '4.jpeg', 位置: (234, 209), 置信度: 0.9939669966697693
[15:45:37] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '4.jpeg'
[15:45:37] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'4.jpeg'不包含任何目标序列
[15:45:37] [    INFO] [测试3.py:693] - 处理文本块: '5.jpeg', 位置: (340, 209), 置信度: 0.9417853355407715
[15:45:37] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '5.jpeg'
[15:45:37] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'5.jpeg'不包含任何目标序列
[15:45:37] [    INFO] [测试3.py:693] - 处理文本块: '6,jpeg', 位置: (450, 211), 置信度: 0.93913334608078
[15:45:37] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '6,jpeg'
[15:45:37] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'6,jpeg'不包含任何目标序列
[15:45:37] [    INFO] [测试3.py:693] - 处理文本块: '7.jpeg', 位置: (557, 210), 置信度: 0.9854061007499695
[15:45:37] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '7.jpeg'
[15:45:37] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'7.jpeg'不包含任何目标序列
[15:45:37] [    INFO] [测试3.py:693] - 处理文本块: '8.jpeg', 位置: (664, 210), 置信度: 0.988820493221283
[15:45:37] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '8.jpeg'
[15:45:37] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'8.jpeg'不包含任何目标序列
[15:45:37] [    INFO] [测试3.py:693] - 处理文本块: '9.jpeg', 位置: (772, 210), 置信度: 0.9855602383613586
[15:45:37] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '9.jpeg'
[15:45:37] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'9.jpeg'不包含任何目标序列
[15:45:37] [    INFO] [测试3.py:693] - 处理文本块: '10.jpeg', 位置: (881, 210), 置信度: 0.994547963142395
[15:45:37] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '10.jpeg'
[15:45:37] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'10.jpeg'不包含任何目标序列
[15:45:37] [    INFO] [测试3.py:693] - 处理文本块: '11.jpeg', 位置: (989, 210), 置信度: 0.8855990171432495
[15:45:37] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '11.jpeg'
[15:45:37] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'11.jpeg'不包含任何目标序列
[15:45:37] [    INFO] [测试3.py:693] - 处理文本块: '800x1200.jpg', 位置: (1098, 209), 置信度: 0.923505961894989
[15:45:37] [    INFO] [测试3.py:703] - 找到800x1200相关: 800x1200.jpg, 点击位置: (1098, 209)
[15:45:37] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '800x1200.jpg'
[15:45:37] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'800x1200.jpg'不包含任何目标序列
[15:45:37] [    INFO] [测试3.py:693] - 处理文本块: 'e7bbaed6-54d', 位置: (1207, 209), 置信度: 0.9994044899940491
[15:45:37] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'e7bbaed6-54d'
[15:45:37] [    INFO] [测试3.py:715] - UUID匹配成功: 序列'e7b'在文本'e7bbaed6-54d'中, 点击位置: (1207, 209)
[15:45:37] [    INFO] [测试3.py:693] - 处理文本块: '主图1.jpeg', 位置: (1313, 210), 置信度: 0.9721140265464783
[15:45:37] [    INFO] [测试3.py:698] - 找到主图: 主图1.jpeg, 点击位置: (1313, 210)
[15:45:37] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图1.jpeg'
[15:45:37] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图1.jpeg'不包含任何目标序列
[15:45:37] [    INFO] [测试3.py:693] - 处理文本块: '主图2.jpeg', 位置: (1421, 209), 置信度: 0.9917568564414978
[15:45:37] [    INFO] [测试3.py:698] - 找到主图: 主图2.jpeg, 点击位置: (1421, 209)
[15:45:37] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图2.jpeg'
[15:45:37] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图2.jpeg'不包含任何目标序列
[15:45:37] [    INFO] [测试3.py:693] - 处理文本块: '主图3.jpeg', 位置: (1529, 210), 置信度: 0.9388467073440552
[15:45:37] [    INFO] [测试3.py:698] - 找到主图: 主图3.jpeg, 点击位置: (1529, 210)
[15:45:37] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图3.jpeg'
[15:45:37] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图3.jpeg'不包含任何目标序列
[15:45:37] [    INFO] [测试3.py:693] - 处理文本块: '主图4jpeg', 位置: (1637, 210), 置信度: 0.9319921135902405
[15:45:37] [    INFO] [测试3.py:698] - 找到主图: 主图4jpeg, 点击位置: (1637, 210)
[15:45:37] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: '主图4jpeg'
[15:45:37] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'主图4jpeg'不包含任何目标序列
[15:45:37] [    INFO] [测试3.py:693] - 处理文本块: 'e-4040-8765-d', 位置: (1205, 224), 置信度: 0.9782481789588928
[15:45:37] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'e-4040-8765-d'
[15:45:37] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'e-4040-8765-d'不包含任何目标序列
[15:45:37] [    INFO] [测试3.py:693] - 处理文本块: 'bedd2ff03bf.pn', 位置: (1205, 242), 置信度: 0.9133968353271484
[15:45:37] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'bedd2ff03bf.pn'
[15:45:37] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'bedd2ff03bf.pn'不包含任何目标序列
[15:45:37] [    INFO] [测试3.py:693] - 处理文本块: 'g', 位置: (1208, 262), 置信度: 0.9956187605857849
[15:45:37] [    INFO] [测试3.py:708] - 开始匹配UUID - 文本: 'g'
[15:45:37] [    INFO] [测试3.py:719] - UUID匹配失败: 文本'g'不包含任何目标序列
[15:45:37] [    INFO] [测试3.py:722] - OCR识别统计:
[15:45:37] [    INFO] [测试3.py:723] - - 总文本块数: 17
[15:45:37] [    INFO] [测试3.py:724] - - UUID匹配尝试次数: 17
[15:45:37] [    INFO] [测试3.py:725] - - UUID成功匹配次数: 1
[15:45:37] [    INFO] [测试3.py:726] - - 待点击位置数: 6
[15:45:37] [    INFO] [测试3.py:741] - 去重后待点击位置数: 6
[15:45:37] [    INFO] [测试3.py:753] - 按下Ctrl键进行多选
[15:45:37] [    INFO] [测试3.py:757] - 点击第1个位置: (1098, 209)
[15:45:38] [    INFO] [测试3.py:757] - 点击第2个位置: (1207, 209)
[15:45:38] [    INFO] [测试3.py:757] - 点击第3个位置: (1313, 210)
[15:45:39] [    INFO] [测试3.py:757] - 点击第4个位置: (1421, 209)
[15:45:40] [    INFO] [测试3.py:757] - 点击第5个位置: (1529, 210)
[15:45:40] [    INFO] [测试3.py:757] - 点击第6个位置: (1637, 210)
[15:45:41] [    INFO] [测试3.py:764] - 释放Ctrl键
[15:45:41] [    INFO] [测试3.py:766] - 完成点击操作，共点击了6个位置
[15:45:41] [    INFO] [测试3.py:1339] - 点击打开按钮...
[15:45:41] [    INFO] [测试3.py:1343] - 等待图片加载到图片空间...
[15:45:42] [    INFO] [测试3.py:804] - 开始在图片空间按顺序选择主图...
[15:45:43] [    INFO] [测试3.py:808] - 等待1秒后开始检测上传状态...
[15:45:45] [    INFO] [测试3.py:871] - 检测到'上传中'状态，位置: Box(left=571, top=846, width=55, height=19)，继续等待2秒...
[15:45:47] [    INFO] [测试3.py:887] - 未检测到'上传中'状态（ImageNotFoundException），尝试点击'完成'按钮
[15:45:47] [    INFO] [测试3.py:890] - 成功点击'完成'按钮
[15:45:48] [    INFO] [测试3.py:919] - 截取图片空间区域: (680, 550, 670, 370)
[15:45:48] [    INFO] [测试3.py:930] - 期望找到的主图数量: 4
[15:45:48] [    INFO] [测试3.py:1562] - 开始OCR识别图片: logs\debug_space_screenshot.png
[15:45:48] [    INFO] [测试3.py:1579] - 当前识别场景: main
[15:45:48] [   DEBUG] [测试3.py:1582] - 正在执行OCR识别...
[15:45:51] [   DEBUG] [测试3.py:1585] - OCR原始返回结果: [{'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[238, ..., 234],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[238, ..., 234],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[238, ..., 234],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 6,  5],
       ...,
       [ 6, 15]], dtype=int16), array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[157,  42],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[429,  42],
       ...,
       [427,  61]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[161,  64],
       ...,
       [161,  76]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[564,  63],
       ...,
       [564,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[163, 220],
       ...,
       [163, 238]], dtype=int16), array([[286, 218],
       ...,
       [285, 236]], dtype=int16), array([[422, 218],
       ...,
       [422, 236]], dtype=int16), array([[559, 216],
       ...,
       [558, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[295, 240],
       ...,
       [295, 255]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[  0, 270],
       ...,
       [  0, 290]], dtype=int16), array([[131, 272],
       ...,
       [131, 289]], dtype=int16), array([[274, 272],
       ...,
       [274, 286]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '7bbaed6-54de-40..', '引', '主图4.jpg', '引主图1.jpg', '引主图3.jpg', '引主图2.jpg', '800×800', '800×800', '800x800', '800×800', '800x800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9656604528427124, 0.980584979057312, 0.9802374839782715, 0.9837214350700378, 0.9692646861076355, 0.9437593221664429, 0.9704387784004211, 0.9743536710739136, 0.9743536710739136, 0.9444304704666138, 0.9458206295967102, 0.9882751703262329, 0.9826886057853699, 0.9778520464897156, 0.9716701507568359, 0.975218653678894, 0.9506109952926636, 0.9704387784004211, 0.9282951354980469, 0.9506109952926636, 0.9282951354980469, 0.9380257725715637, 0.9996420741081238], 'rec_polys': [array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[157,  42],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[429,  42],
       ...,
       [427,  61]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[161,  64],
       ...,
       [161,  76]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[564,  63],
       ...,
       [564,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[163, 220],
       ...,
       [163, 238]], dtype=int16), array([[286, 218],
       ...,
       [285, 236]], dtype=int16), array([[422, 218],
       ...,
       [422, 236]], dtype=int16), array([[559, 216],
       ...,
       [558, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[295, 240],
       ...,
       [295, 255]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [547, ..., 327]], dtype=int16)}]
[15:45:51] [    INFO] [测试3.py:1553] - OCR结果已保存到: logs\result_20250828_154551_main.txt 和 logs\result_20250828_154551_main.json
[15:45:51] [    INFO] [测试3.py:1428] - OCR原始结果: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'input_img': array([[[238, ..., 234],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1, 'rot_img': array([[[238, ..., 234],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8), 'output_img': array([[[238, ..., 234],
        ...,
        [255, ..., 255]],

       ...,

       [[255, ..., 255],
        ...,
        [255, ..., 255]]], dtype=uint8)}, 'dt_polys': [array([[ 6,  5],
       ...,
       [ 6, 15]], dtype=int16), array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[157,  42],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[429,  42],
       ...,
       [427,  61]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[161,  64],
       ...,
       [161,  76]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[564,  63],
       ...,
       [564,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[163, 220],
       ...,
       [163, 238]], dtype=int16), array([[286, 218],
       ...,
       [285, 236]], dtype=int16), array([[422, 218],
       ...,
       [422, 236]], dtype=int16), array([[559, 216],
       ...,
       [558, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[295, 240],
       ...,
       [295, 255]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[  0, 270],
       ...,
       [  0, 290]], dtype=int16), array([[131, 272],
       ...,
       [131, 289]], dtype=int16), array([[274, 272],
       ...,
       [274, 286]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '7bbaed6-54de-40..', '引', '主图4.jpg', '引主图1.jpg', '引主图3.jpg', '引主图2.jpg', '800×800', '800×800', '800x800', '800×800', '800x800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9656604528427124, 0.980584979057312, 0.9802374839782715, 0.9837214350700378, 0.9692646861076355, 0.9437593221664429, 0.9704387784004211, 0.9743536710739136, 0.9743536710739136, 0.9444304704666138, 0.9458206295967102, 0.9882751703262329, 0.9826886057853699, 0.9778520464897156, 0.9716701507568359, 0.975218653678894, 0.9506109952926636, 0.9704387784004211, 0.9282951354980469, 0.9506109952926636, 0.9282951354980469, 0.9380257725715637, 0.9996420741081238], 'rec_polys': [array([[20, 42],
       ...,
       [18, 63]], dtype=int16), array([[157,  42],
       ...,
       [155,  63]], dtype=int16), array([[292,  42],
       ...,
       [291,  63]], dtype=int16), array([[429,  42],
       ...,
       [427,  61]], dtype=int16), array([[554,  43],
       ...,
       [553,  61]], dtype=int16), array([[23, 63],
       ...,
       [23, 78]], dtype=int16), array([[161,  64],
       ...,
       [161,  76]], dtype=int16), array([[296,  64],
       ...,
       [296,  76]], dtype=int16), array([[432,  64],
       ...,
       [432,  76]], dtype=int16), array([[564,  63],
       ...,
       [564,  78]], dtype=int16), array([[  0, 221],
       ...,
       [  0, 235]], dtype=int16), array([[154, 223],
       ...,
       [154, 233]], dtype=int16), array([[163, 220],
       ...,
       [163, 238]], dtype=int16), array([[286, 218],
       ...,
       [285, 236]], dtype=int16), array([[422, 218],
       ...,
       [422, 236]], dtype=int16), array([[559, 216],
       ...,
       [558, 236]], dtype=int16), array([[ 24, 236],
       ...,
       [ 24, 251]], dtype=int16), array([[161, 242],
       ...,
       [161, 254]], dtype=int16), array([[295, 240],
       ...,
       [295, 255]], dtype=int16), array([[432, 240],
       ...,
       [432, 255]], dtype=int16), array([[567, 240],
       ...,
       [567, 255]], dtype=int16), array([[445, 308],
       ...,
       [445, 326]], dtype=int16), array([[547, 308],
       ...,
       [547, 327]], dtype=int16)], 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'rec_boxes': array([[ 18, ...,  67],
       ...,
       [547, ..., 327]], dtype=int16)}
[15:45:51] [    INFO] [测试3.py:1435] - OCR结果json: {'res': {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[6, 5], [41, 5], [41, 15], [6, 15]], [[20, 42], [78, 46], [77, 67], [18, 63]], [[157, 42], [214, 46], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[429, 42], [485, 47], [483, 66], [427, 61]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[161, 64], [208, 64], [208, 76], [161, 76]], [[296, 64], [345, 64], [345, 76], [296, 76]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[564, 63], [621, 63], [621, 78], [564, 78]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[154, 223], [168, 223], [168, 233], [154, 233]], [[163, 220], [220, 220], [220, 238], [163, 238]], [[286, 218], [356, 220], [356, 238], [285, 236]], [[422, 218], [493, 220], [492, 238], [422, 236]], [[559, 216], [629, 220], [628, 239], [558, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[295, 240], [346, 240], [346, 255], [295, 255]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[567, 240], [618, 240], [618, 255], [567, 255]], [[0, 270], [109, 270], [109, 290], [0, 290]], [[131, 272], [242, 272], [242, 289], [131, 289]], [[274, 272], [357, 272], [357, 286], [274, 286]], [[445, 308], [550, 308], [550, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '7bbaed6-54de-40..', '引', '主图4.jpg', '引主图1.jpg', '引主图3.jpg', '引主图2.jpg', '800×800', '800×800', '800x800', '800×800', '800x800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9656604528427124, 0.980584979057312, 0.9802374839782715, 0.9837214350700378, 0.9692646861076355, 0.9437593221664429, 0.9704387784004211, 0.9743536710739136, 0.9743536710739136, 0.9444304704666138, 0.9458206295967102, 0.9882751703262329, 0.9826886057853699, 0.9778520464897156, 0.9716701507568359, 0.975218653678894, 0.9506109952926636, 0.9704387784004211, 0.9282951354980469, 0.9506109952926636, 0.9282951354980469, 0.9380257725715637, 0.9996420741081238], 'rec_polys': [[[20, 42], [78, 46], [77, 67], [18, 63]], [[157, 42], [214, 46], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[429, 42], [485, 47], [483, 66], [427, 61]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[161, 64], [208, 64], [208, 76], [161, 76]], [[296, 64], [345, 64], [345, 76], [296, 76]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[564, 63], [621, 63], [621, 78], [564, 78]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[154, 223], [168, 223], [168, 233], [154, 233]], [[163, 220], [220, 220], [220, 238], [163, 238]], [[286, 218], [356, 220], [356, 238], [285, 236]], [[422, 218], [493, 220], [492, 238], [422, 236]], [[559, 216], [629, 220], [628, 239], [558, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[295, 240], [346, 240], [346, 255], [295, 255]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[567, 240], [618, 240], [618, 255], [567, 255]], [[445, 308], [550, 308], [550, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'rec_boxes': [[18, 42, 78, 67], [155, 42, 214, 67], [291, 42, 350, 67], [427, 42, 485, 66], [553, 43, 631, 65], [23, 63, 74, 78], [161, 64, 208, 76], [296, 64, 345, 76], [432, 64, 481, 76], [564, 63, 621, 78], [0, 221, 101, 235], [154, 223, 168, 233], [163, 220, 220, 238], [285, 218, 356, 238], [422, 218, 493, 238], [558, 216, 629, 239], [24, 236, 74, 251], [161, 242, 208, 254], [295, 240, 346, 255], [432, 240, 482, 255], [567, 240, 618, 255], [445, 308, 550, 326], [547, 308, 604, 327]]}}
[15:45:51] [    INFO] [测试3.py:1444] - res_data内容: {'input_path': 'logs\\debug_space_screenshot.png', 'page_index': None, 'model_settings': {'use_doc_preprocessor': True, 'use_textline_orientation': False}, 'doc_preprocessor_res': {'input_path': None, 'page_index': None, 'model_settings': {'use_doc_orientation_classify': False, 'use_doc_unwarping': False}, 'angle': -1}, 'dt_polys': [[[6, 5], [41, 5], [41, 15], [6, 15]], [[20, 42], [78, 46], [77, 67], [18, 63]], [[157, 42], [214, 46], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[429, 42], [485, 47], [483, 66], [427, 61]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[161, 64], [208, 64], [208, 76], [161, 76]], [[296, 64], [345, 64], [345, 76], [296, 76]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[564, 63], [621, 63], [621, 78], [564, 78]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[154, 223], [168, 223], [168, 233], [154, 233]], [[163, 220], [220, 220], [220, 238], [163, 238]], [[286, 218], [356, 220], [356, 238], [285, 236]], [[422, 218], [493, 220], [492, 238], [422, 236]], [[559, 216], [629, 220], [628, 239], [558, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[295, 240], [346, 240], [346, 255], [295, 255]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[567, 240], [618, 240], [618, 255], [567, 255]], [[0, 270], [109, 270], [109, 290], [0, 290]], [[131, 272], [242, 272], [242, 289], [131, 289]], [[274, 272], [357, 272], [357, 286], [274, 286]], [[445, 308], [550, 308], [550, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'text_det_params': {'limit_side_len': 960, 'limit_type': 'max', 'thresh': 0.3, 'max_side_limit': 4000, 'box_thresh': 0.5, 'unclip_ratio': 2.0}, 'text_type': 'general', 'textline_orientation_angles': [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], 'text_rec_score_thresh': 0.3, 'rec_texts': ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '7bbaed6-54de-40..', '引', '主图4.jpg', '引主图1.jpg', '引主图3.jpg', '引主图2.jpg', '800×800', '800×800', '800x800', '800×800', '800x800', '①裁剪宽高比：11', '智能裁剪'], 'rec_scores': [0.9656604528427124, 0.980584979057312, 0.9802374839782715, 0.9837214350700378, 0.9692646861076355, 0.9437593221664429, 0.9704387784004211, 0.9743536710739136, 0.9743536710739136, 0.9444304704666138, 0.9458206295967102, 0.9882751703262329, 0.9826886057853699, 0.9778520464897156, 0.9716701507568359, 0.975218653678894, 0.9506109952926636, 0.9704387784004211, 0.9282951354980469, 0.9506109952926636, 0.9282951354980469, 0.9380257725715637, 0.9996420741081238], 'rec_polys': [[[20, 42], [78, 46], [77, 67], [18, 63]], [[157, 42], [214, 46], [212, 67], [155, 63]], [[292, 42], [350, 46], [349, 67], [291, 63]], [[429, 42], [485, 47], [483, 66], [427, 61]], [[554, 43], [631, 47], [631, 65], [553, 61]], [[23, 63], [74, 63], [74, 78], [23, 78]], [[161, 64], [208, 64], [208, 76], [161, 76]], [[296, 64], [345, 64], [345, 76], [296, 76]], [[432, 64], [481, 64], [481, 76], [432, 76]], [[564, 63], [621, 63], [621, 78], [564, 78]], [[0, 221], [101, 221], [101, 235], [0, 235]], [[154, 223], [168, 223], [168, 233], [154, 233]], [[163, 220], [220, 220], [220, 238], [163, 238]], [[286, 218], [356, 220], [356, 238], [285, 236]], [[422, 218], [493, 220], [492, 238], [422, 236]], [[559, 216], [629, 220], [628, 239], [558, 236]], [[24, 236], [74, 236], [74, 251], [24, 251]], [[161, 242], [208, 242], [208, 254], [161, 254]], [[295, 240], [346, 240], [346, 255], [295, 255]], [[432, 240], [482, 240], [482, 255], [432, 255]], [[567, 240], [618, 240], [618, 255], [567, 255]], [[445, 308], [550, 308], [550, 326], [445, 326]], [[547, 308], [604, 308], [604, 327], [547, 327]]], 'rec_boxes': [[18, 42, 78, 67], [155, 42, 214, 67], [291, 42, 350, 67], [427, 42, 485, 66], [553, 43, 631, 65], [23, 63, 74, 78], [161, 64, 208, 76], [296, 64, 345, 76], [432, 64, 481, 76], [564, 63, 621, 78], [0, 221, 101, 235], [154, 223, 168, 233], [163, 220, 220, 238], [285, 218, 356, 238], [422, 218, 493, 238], [558, 216, 629, 239], [24, 236, 74, 251], [161, 242, 208, 254], [295, 240, 346, 255], [432, 240, 482, 255], [567, 240, 618, 255], [445, 308, 550, 326], [547, 308, 604, 327]]}
[15:45:51] [    INFO] [测试3.py:1452] - 识别到的文本: ['主图4.jpg', '主图2.jpg', '主图3.jpg', '主图1.jpg', '800x1200.jpg', '800×800', '800×800', '800×800', '800×800', '800×1200', '7bbaed6-54de-40..', '引', '主图4.jpg', '引主图1.jpg', '引主图3.jpg', '引主图2.jpg', '800×800', '800×800', '800x800', '800×800', '800x800', '①裁剪宽高比：11', '智能裁剪']
[15:45:51] [    INFO] [测试3.py:1453] - 识别的置信度: [0.9656604528427124, 0.980584979057312, 0.9802374839782715, 0.9837214350700378, 0.9692646861076355, 0.9437593221664429, 0.9704387784004211, 0.9743536710739136, 0.9743536710739136, 0.9444304704666138, 0.9458206295967102, 0.9882751703262329, 0.9826886057853699, 0.9778520464897156, 0.9716701507568359, 0.975218653678894, 0.9506109952926636, 0.9704387784004211, 0.9282951354980469, 0.9506109952926636, 0.9282951354980469, 0.9380257725715637, 0.9996420741081238]
[15:45:51] [    INFO] [测试3.py:1454] - 识别的坐标框: [[18, 42, 78, 67], [155, 42, 214, 67], [291, 42, 350, 67], [427, 42, 485, 66], [553, 43, 631, 65], [23, 63, 74, 78], [161, 64, 208, 76], [296, 64, 345, 76], [432, 64, 481, 76], [564, 63, 621, 78], [0, 221, 101, 235], [154, 223, 168, 233], [163, 220, 220, 238], [285, 218, 356, 238], [422, 218, 493, 238], [558, 216, 629, 239], [24, 236, 74, 251], [161, 242, 208, 254], [295, 240, 346, 255], [432, 240, 482, 255], [567, 240, 618, 255], [445, 308, 550, 326], [547, 308, 604, 327]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9656604528427124, 原始坐标=[18, 42, 78, 67], 转换后坐标=[[18, 42], [78, 42], [78, 67], [18, 67]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图2.jpg, 置信度=0.980584979057312, 原始坐标=[155, 42, 214, 67], 转换后坐标=[[155, 42], [214, 42], [214, 67], [155, 67]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图3.jpg, 置信度=0.9802374839782715, 原始坐标=[291, 42, 350, 67], 转换后坐标=[[291, 42], [350, 42], [350, 67], [291, 67]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图1.jpg, 置信度=0.9837214350700378, 原始坐标=[427, 42, 485, 66], 转换后坐标=[[427, 42], [485, 42], [485, 66], [427, 66]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800x1200.jpg, 置信度=0.9692646861076355, 原始坐标=[553, 43, 631, 65], 转换后坐标=[[553, 43], [631, 43], [631, 65], [553, 65]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9437593221664429, 原始坐标=[23, 63, 74, 78], 转换后坐标=[[23, 63], [74, 63], [74, 78], [23, 78]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9704387784004211, 原始坐标=[161, 64, 208, 76], 转换后坐标=[[161, 64], [208, 64], [208, 76], [161, 76]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9743536710739136, 原始坐标=[296, 64, 345, 76], 转换后坐标=[[296, 64], [345, 64], [345, 76], [296, 76]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9743536710739136, 原始坐标=[432, 64, 481, 76], 转换后坐标=[[432, 64], [481, 64], [481, 76], [432, 76]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×1200, 置信度=0.9444304704666138, 原始坐标=[564, 63, 621, 78], 转换后坐标=[[564, 63], [621, 63], [621, 78], [564, 78]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=7bbaed6-54de-40.., 置信度=0.9458206295967102, 原始坐标=[0, 221, 101, 235], 转换后坐标=[[0, 221], [101, 221], [101, 235], [0, 235]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=引, 置信度=0.9882751703262329, 原始坐标=[154, 223, 168, 233], 转换后坐标=[[154, 223], [168, 223], [168, 233], [154, 233]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=主图4.jpg, 置信度=0.9826886057853699, 原始坐标=[163, 220, 220, 238], 转换后坐标=[[163, 220], [220, 220], [220, 238], [163, 238]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=引主图1.jpg, 置信度=0.9778520464897156, 原始坐标=[285, 218, 356, 238], 转换后坐标=[[285, 218], [356, 218], [356, 238], [285, 238]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=引主图3.jpg, 置信度=0.9716701507568359, 原始坐标=[422, 218, 493, 238], 转换后坐标=[[422, 218], [493, 218], [493, 238], [422, 238]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=引主图2.jpg, 置信度=0.975218653678894, 原始坐标=[558, 216, 629, 239], 转换后坐标=[[558, 216], [629, 216], [629, 239], [558, 239]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[24, 236, 74, 251], 转换后坐标=[[24, 236], [74, 236], [74, 251], [24, 251]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9704387784004211, 原始坐标=[161, 242, 208, 254], 转换后坐标=[[161, 242], [208, 242], [208, 254], [161, 254]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800x800, 置信度=0.9282951354980469, 原始坐标=[295, 240, 346, 255], 转换后坐标=[[295, 240], [346, 240], [346, 255], [295, 255]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800×800, 置信度=0.9506109952926636, 原始坐标=[432, 240, 482, 255], 转换后坐标=[[432, 240], [482, 240], [482, 255], [432, 255]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=800x800, 置信度=0.9282951354980469, 原始坐标=[567, 240, 618, 255], 转换后坐标=[[567, 240], [618, 240], [618, 255], [567, 255]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=①裁剪宽高比：11, 置信度=0.9380257725715637, 原始坐标=[445, 308, 550, 326], 转换后坐标=[[445, 308], [550, 308], [550, 326], [445, 326]]
[15:45:51] [    INFO] [测试3.py:1473] - 成功转换结果: 文本=智能裁剪, 置信度=0.9996420741081238, 原始坐标=[547, 308, 604, 327], 转换后坐标=[[547, 308], [604, 308], [604, 327], [547, 327]]
[15:45:51] [    INFO] [测试3.py:1478] - 转换完成，共转换23个结果
[15:45:51] [   DEBUG] [测试3.py:1610] - OCR结果格式转换完成: [[[[[18, 42], [78, 42], [78, 67], [18, 67]], ['主图4.jpg', 0.9656604528427124]], [[[155, 42], [214, 42], [214, 67], [155, 67]], ['主图2.jpg', 0.980584979057312]], [[[291, 42], [350, 42], [350, 67], [291, 67]], ['主图3.jpg', 0.9802374839782715]], [[[427, 42], [485, 42], [485, 66], [427, 66]], ['主图1.jpg', 0.9837214350700378]], [[[553, 43], [631, 43], [631, 65], [553, 65]], ['800x1200.jpg', 0.9692646861076355]], [[[23, 63], [74, 63], [74, 78], [23, 78]], ['800×800', 0.9437593221664429]], [[[161, 64], [208, 64], [208, 76], [161, 76]], ['800×800', 0.9704387784004211]], [[[296, 64], [345, 64], [345, 76], [296, 76]], ['800×800', 0.9743536710739136]], [[[432, 64], [481, 64], [481, 76], [432, 76]], ['800×800', 0.9743536710739136]], [[[564, 63], [621, 63], [621, 78], [564, 78]], ['800×1200', 0.9444304704666138]], [[[0, 221], [101, 221], [101, 235], [0, 235]], ['7bbaed6-54de-40..', 0.9458206295967102]], [[[154, 223], [168, 223], [168, 233], [154, 233]], ['引', 0.9882751703262329]], [[[163, 220], [220, 220], [220, 238], [163, 238]], ['主图4.jpg', 0.9826886057853699]], [[[285, 218], [356, 218], [356, 238], [285, 238]], ['引主图1.jpg', 0.9778520464897156]], [[[422, 218], [493, 218], [493, 238], [422, 238]], ['引主图3.jpg', 0.9716701507568359]], [[[558, 216], [629, 216], [629, 239], [558, 239]], ['引主图2.jpg', 0.975218653678894]], [[[24, 236], [74, 236], [74, 251], [24, 251]], ['800×800', 0.9506109952926636]], [[[161, 242], [208, 242], [208, 254], [161, 254]], ['800×800', 0.9704387784004211]], [[[295, 240], [346, 240], [346, 255], [295, 255]], ['800x800', 0.9282951354980469]], [[[432, 240], [482, 240], [482, 255], [432, 255]], ['800×800', 0.9506109952926636]], [[[567, 240], [618, 240], [618, 255], [567, 255]], ['800x800', 0.9282951354980469]], [[[445, 308], [550, 308], [550, 326], [445, 326]], ['①裁剪宽高比：11', 0.9380257725715637]], [[[547, 308], [604, 308], [604, 327], [547, 327]], ['智能裁剪', 0.9996420741081238]]]]
[15:45:51] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (728, 604), 置信度: 0.9656604528427124
[15:45:51] [    INFO] [测试3.py:959] - 找到主图: 主图2.jpg, 位置: (864, 604), 置信度: 0.980584979057312
[15:45:51] [    INFO] [测试3.py:959] - 找到主图: 主图3.jpg, 位置: (1000, 604), 置信度: 0.9802374839782715
[15:45:51] [    INFO] [测试3.py:959] - 找到主图: 主图1.jpg, 位置: (1136, 604), 置信度: 0.9837214350700378
[15:45:51] [    INFO] [测试3.py:959] - 找到主图: 主图4.jpg, 位置: (871, 779), 置信度: 0.9826886057853699
[15:45:51] [    INFO] [测试3.py:984] - 点击主图1, 坐标: (1136, 534)
[15:45:52] [    INFO] [测试3.py:984] - 点击主图2, 坐标: (864, 534)
[15:45:53] [    INFO] [测试3.py:984] - 点击主图3, 坐标: (1000, 534)
[15:45:55] [    INFO] [测试3.py:984] - 点击主图4, 坐标: (728, 534)
[15:45:56] [    INFO] [测试3.py:973] - 已达到最大点击次数(4次)，停止点击
[15:45:56] [    INFO] [测试3.py:992] - 主图选择完成，成功点击了 4/4 张主图（最多点击4次）
[15:45:56] [    INFO] [测试3.py:1371] - 点击右侧空白处关闭图片空间...
[15:45:57] [    INFO] [测试3.py:1376] - 点击裁剪按钮...
[15:45:58] [    INFO] [测试3.py:1383] - 开始上传UUID透明图...
[15:45:59] [    INFO] [测试3.py:1036] - 开始在图片空间选择UUID图片...
[15:46:00] [    INFO] [测试3.py:1040] - 初始化新的OCR引擎...
[15:46:04] [    INFO] [测试3.py:1057] - OCR引擎初始化成功
[15:46:04] [    INFO] [测试3.py:1068] - 截取UUID图片空间区域: (770, 550, 710, 290)
[15:46:04] [    INFO] [测试3.py:1086] - 截图已保存为: logs\debug_uuid_screenshot_20250828_154604.png
[15:46:04] [    INFO] [测试3.py:1094] - 目标UUID: e7bbaed6-54de-4040-8765-dbedd2ff03bf.png
[15:46:04] [    INFO] [测试3.py:1095] - 生成的UUID匹配序列: ['e7b', '7bb', 'bba', 'bae', 'aed', 'ed6']
[15:46:04] [    INFO] [测试3.py:1101] - 正在进行文字识别...
[15:46:06] [    INFO] [测试3.py:1116] - 原始JSON结果已保存到: logs\result_uuid_20250828_154604.json
[15:46:06] [    INFO] [测试3.py:1120] - 识别到 23 个文本块
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (61, 96), 置信度: 0.9788
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (170, 96), 置信度: 0.9777
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (280, 96), 置信度: 0.9826
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (391, 97), 置信度: 0.9694
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (500, 96), 置信度: 0.9759
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (619, 95), 置信度: 0.9586
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4.jpg, 位置: (36, 122), 置信度: 0.9218
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (146, 123), 置信度: 0.9415
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (256, 122), 置信度: 0.9912
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 主图4jpg, 位置: (366, 122), 置信度: 0.9879
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 主图2.jpg, 位置: (476, 123), 置信度: 0.9908
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 主图3.jpg, 位置: (585, 123), 置信度: 0.9675
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (60, 238), 置信度: 0.9727
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200, 位置: (170, 238), 置信度: 0.9748
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (280, 238), 置信度: 0.9734
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (391, 239), 置信度: 0.9800
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (500, 238), 置信度: 0.9762
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 800x800, 位置: (611, 239), 置信度: 0.9679
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 主图1.jpg, 位置: (36, 265), 置信度: 0.9745
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: 800x1200.jpg, 位置: (158, 264), 置信度: 0.9811
[15:46:06] [    INFO] [测试3.py:1126] - 识别到文本块: e7bbaed6-54d..主图4.jpg, 位置: (311, 264), 置信度: 0.9003
[15:46:06] [    INFO] [测试3.py:1133] - UUID匹配成功: 序列'e7b'在文本'e7bbaed6-54d..主图4.jpg'中
[15:46:06] [    INFO] [测试3.py:1139] - 计算的点击位置: (1081, 764)
[15:46:06] [    INFO] [测试3.py:1143] - 可视化结果已保存为: output_uuid_20250828_154604
[15:46:08] [    INFO] [测试3.py:1400] - 开始上传800x1200图片...
[15:46:09] [    INFO] [测试3.py:1161] - 开始在图片空间选择800x1200图片...
[15:46:10] [    INFO] [测试3.py:1165] - 初始化新的OCR引擎...
[15:46:13] [    INFO] [测试3.py:1182] - OCR引擎初始化成功
[15:46:13] [    INFO] [测试3.py:1193] - 截取800x1200图片空间区域: (630, 546, 710, 290)
[15:46:14] [    INFO] [测试3.py:1211] - 截图已保存为: logs\debug_800x1200_screenshot_20250828_154614.png
[15:46:14] [    INFO] [测试3.py:1214] - 正在进行文字识别...
[15:46:15] [    INFO] [测试3.py:1229] - 原始JSON结果已保存到: logs\result_800x1200_20250828_154614.json
[15:46:15] [    INFO] [测试3.py:1233] - 识别到 23 个文本块
[15:46:15] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (62, 100), 置信度: 0.9826
[15:46:15] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (173, 101), 置信度: 0.9803
[15:46:15] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (283, 101), 置信度: 0.9866
[15:46:15] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (393, 101), 置信度: 0.9758
[15:46:15] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (502, 101), 置信度: 0.9529
[15:46:15] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (613, 100), 置信度: 0.9626
[15:46:15] [    INFO] [测试3.py:1239] - 识别到文本块: 主图2.jpg, 位置: (38, 126), 置信度: 0.9850
[15:46:15] [    INFO] [测试3.py:1239] - 识别到文本块: 主图4jpg, 位置: (147, 126), 置信度: 0.9884
[15:46:15] [    INFO] [测试3.py:1239] - 识别到文本块: 主图3.jpg, 位置: (258, 127), 置信度: 0.9549
[15:46:15] [    INFO] [测试3.py:1239] - 识别到文本块: 主图4.jpg, 位置: (368, 127), 置信度: 0.9164
[15:46:15] [    INFO] [测试3.py:1239] - 识别到文本块: 主图3.jpg, 位置: (477, 127), 置信度: 0.9384
[15:46:15] [    INFO] [测试3.py:1239] - 识别到文本块: 主图2.jpg, 位置: (588, 127), 置信度: 0.9646
[15:46:15] [    INFO] [测试3.py:1239] - 识别到文本块: 800x800, 位置: (62, 242), 置信度: 0.9727
[15:46:15] [    INFO] [测试3.py:1239] - 识别到文本块: 800x1200, 位置: (172, 243), 置信度: 0.9508
[15:46:15] [    INFO] [测试3.py:1245] - 800x1200匹配成功: 文本='800x1200'
[15:46:15] [    INFO] [测试3.py:1251] - 计算的点击位置: (802, 739)
[15:46:16] [    INFO] [测试3.py:1255] - 可视化结果已保存为: output_800x1200_20250828_154614
[15:46:17] [    INFO] [测试3.py:1413] - 向下滚动页面...
[15:46:18] [    INFO] [测试3.py:1417] - 文件选择完成，上传流程结束
[15:46:18] [    INFO] [测试3.py:1652] - 已创建测试3完成信号文件
