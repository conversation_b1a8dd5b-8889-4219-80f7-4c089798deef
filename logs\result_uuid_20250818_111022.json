{"res": {"input_path": "logs\\debug_uuid_screenshot_20250818_111022.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[31, 86], [90, 86], [90, 108], [31, 108]], [[142, 88], [199, 88], [199, 107], [142, 107]], [[252, 88], [309, 88], [309, 107], [252, 107]], [[361, 86], [421, 86], [421, 108], [361, 108]], [[471, 86], [529, 86], [529, 108], [471, 108]], [[577, 86], [644, 86], [644, 108], [577, 108]], [[5, 109], [69, 112], [68, 137], [4, 135]], [[115, 108], [179, 112], [177, 139], [113, 134]], [[225, 109], [289, 112], [288, 137], [224, 135]], [[333, 109], [399, 112], [398, 137], [332, 135]], [[445, 110], [642, 112], [641, 136], [445, 134]], [[32, 229], [89, 229], [89, 248], [32, 248]], [[142, 229], [198, 229], [198, 248], [142, 248]], [[253, 231], [307, 231], [307, 247], [253, 247]], [[359, 229], [424, 229], [424, 248], [359, 248]], [[469, 229], [532, 229], [532, 248], [469, 248]], [[584, 231], [637, 231], [637, 247], [584, 247]], [[6, 252], [45, 256], [43, 280], [3, 276]], [[116, 251], [155, 256], [152, 280], [113, 275]], [[226, 251], [265, 255], [263, 280], [223, 276]], [[335, 251], [375, 255], [373, 280], [333, 276]], [[445, 251], [485, 254], [483, 280], [443, 276]], [[556, 251], [618, 255], [616, 278], [554, 274]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图2.jpg", "主图1.jpg", "主图3.jpg", "主图4.jpg", "d6161e31-ca11800x1200.jpg", "900x900", "900x920", "900x917", "900x1200", "900x1200", "800x800", "5.jpg", "4.jpg", "3.jpg", "2.jpg", "1.jpg", "主图4jpg"], "rec_scores": [0.930206298828125, 0.9773393869400024, 0.9879322052001953, 0.9324544072151184, 0.9672664403915405, 0.957818865776062, 0.9690300226211548, 0.9255523085594177, 0.941493809223175, 0.9384844899177551, 0.9416552186012268, 0.9637838006019592, 0.9574792981147766, 0.9752369523048401, 0.9972032308578491, 0.994392991065979, 0.9743029475212097, 0.9965837597846985, 0.9921063184738159, 0.9921211004257202, 0.9964987635612488, 0.8986754417419434, 0.9879143238067627], "rec_polys": [[[31, 86], [90, 86], [90, 108], [31, 108]], [[142, 88], [199, 88], [199, 107], [142, 107]], [[252, 88], [309, 88], [309, 107], [252, 107]], [[361, 86], [421, 86], [421, 108], [361, 108]], [[471, 86], [529, 86], [529, 108], [471, 108]], [[577, 86], [644, 86], [644, 108], [577, 108]], [[5, 109], [69, 112], [68, 137], [4, 135]], [[115, 108], [179, 112], [177, 139], [113, 134]], [[225, 109], [289, 112], [288, 137], [224, 135]], [[333, 109], [399, 112], [398, 137], [332, 135]], [[445, 110], [642, 112], [641, 136], [445, 134]], [[32, 229], [89, 229], [89, 248], [32, 248]], [[142, 229], [198, 229], [198, 248], [142, 248]], [[253, 231], [307, 231], [307, 247], [253, 247]], [[359, 229], [424, 229], [424, 248], [359, 248]], [[469, 229], [532, 229], [532, 248], [469, 248]], [[584, 231], [637, 231], [637, 247], [584, 247]], [[6, 252], [45, 256], [43, 280], [3, 276]], [[116, 251], [155, 256], [152, 280], [113, 275]], [[226, 251], [265, 255], [263, 280], [223, 276]], [[335, 251], [375, 255], [373, 280], [333, 276]], [[445, 251], [485, 254], [483, 280], [443, 276]], [[556, 251], [618, 255], [616, 278], [554, 274]]], "rec_boxes": [[31, 86, 90, 108], [142, 88, 199, 107], [252, 88, 309, 107], [361, 86, 421, 108], [471, 86, 529, 108], [577, 86, 644, 108], [4, 109, 69, 137], [113, 108, 179, 139], [224, 109, 289, 137], [332, 109, 399, 137], [445, 110, 642, 136], [32, 229, 89, 248], [142, 229, 198, 248], [253, 231, 307, 247], [359, 229, 424, 248], [469, 229, 532, 248], [584, 231, 637, 247], [3, 252, 45, 280], [113, 251, 155, 280], [223, 251, 265, 280], [333, 251, 375, 280], [443, 251, 485, 280], [554, 251, 618, 278]]}}