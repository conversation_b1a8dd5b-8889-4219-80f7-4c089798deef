{"res": {"input_path": "logs\\debug_uuid_screenshot_20250821_092705.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[295, 24], [318, 24], [318, 33], [295, 33]], [[33, 89], [88, 89], [88, 105], [33, 105]], [[143, 89], [198, 89], [198, 105], [143, 105]], [[254, 89], [310, 89], [310, 105], [254, 105]], [[363, 89], [418, 89], [418, 105], [363, 105]], [[473, 89], [527, 89], [527, 105], [473, 105]], [[579, 89], [641, 89], [641, 105], [579, 105]], [[6, 110], [67, 113], [66, 135], [5, 133]], [[116, 109], [177, 113], [175, 136], [114, 132]], [[227, 110], [287, 114], [285, 136], [225, 132]], [[336, 109], [397, 113], [395, 136], [334, 132]], [[446, 112], [641, 114], [640, 135], [446, 133]], [[33, 231], [88, 231], [88, 247], [33, 247]], [[143, 231], [198, 231], [198, 247], [143, 247]], [[253, 231], [307, 231], [307, 247], [253, 247]], [[363, 231], [418, 231], [418, 247], [363, 247]], [[474, 231], [527, 231], [527, 247], [474, 247]], [[584, 231], [637, 231], [637, 247], [584, 247]], [[6, 251], [51, 257], [49, 280], [3, 275]], [[117, 252], [161, 257], [159, 280], [114, 275]], [[225, 252], [272, 256], [270, 280], [223, 275]], [[336, 251], [381, 256], [379, 279], [333, 274]], [[446, 252], [491, 255], [489, 278], [444, 275]], [[555, 251], [601, 256], [598, 279], [553, 274]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图1.jpg", "主图4jpg", "主图3.jpg", "主图2.jpg", "960c4a49-4939.. 800x1200.jpg", "900x900", "900x900", "900x900", "900x900", "900x900", "900x900", "18,jpg", "17.jpg", "15.jpg", "16.jpg", "14.jpg", "13.jpg"], "rec_scores": [0.9563843011856079, 0.9657577872276306, 0.9624605178833008, 0.9813454747200012, 0.9616243243217468, 0.9839886426925659, 0.9613441824913025, 0.9884434342384338, 0.9643496870994568, 0.9792566895484924, 0.93319171667099, 0.959489643573761, 0.9706511497497559, 0.9657512307167053, 0.9729866981506348, 0.9743746519088745, 0.9546972513198853, 0.9179022312164307, 0.8782933354377747, 0.9467832446098328, 0.9909622073173523, 0.9621488451957703, 0.9858967661857605], "rec_polys": [[[33, 89], [88, 89], [88, 105], [33, 105]], [[143, 89], [198, 89], [198, 105], [143, 105]], [[254, 89], [310, 89], [310, 105], [254, 105]], [[363, 89], [418, 89], [418, 105], [363, 105]], [[473, 89], [527, 89], [527, 105], [473, 105]], [[579, 89], [641, 89], [641, 105], [579, 105]], [[6, 110], [67, 113], [66, 135], [5, 133]], [[116, 109], [177, 113], [175, 136], [114, 132]], [[227, 110], [287, 114], [285, 136], [225, 132]], [[336, 109], [397, 113], [395, 136], [334, 132]], [[446, 112], [641, 114], [640, 135], [446, 133]], [[33, 231], [88, 231], [88, 247], [33, 247]], [[143, 231], [198, 231], [198, 247], [143, 247]], [[253, 231], [307, 231], [307, 247], [253, 247]], [[363, 231], [418, 231], [418, 247], [363, 247]], [[474, 231], [527, 231], [527, 247], [474, 247]], [[584, 231], [637, 231], [637, 247], [584, 247]], [[6, 251], [51, 257], [49, 280], [3, 275]], [[117, 252], [161, 257], [159, 280], [114, 275]], [[225, 252], [272, 256], [270, 280], [223, 275]], [[336, 251], [381, 256], [379, 279], [333, 274]], [[446, 252], [491, 255], [489, 278], [444, 275]], [[555, 251], [601, 256], [598, 279], [553, 274]]], "rec_boxes": [[33, 89, 88, 105], [143, 89, 198, 105], [254, 89, 310, 105], [363, 89, 418, 105], [473, 89, 527, 105], [579, 89, 641, 105], [5, 110, 67, 135], [114, 109, 177, 136], [225, 110, 287, 136], [334, 109, 397, 136], [446, 112, 641, 135], [33, 231, 88, 247], [143, 231, 198, 247], [253, 231, 307, 247], [363, 231, 418, 247], [474, 231, 527, 247], [584, 231, 637, 247], [3, 251, 51, 280], [114, 252, 161, 280], [223, 252, 272, 280], [333, 251, 381, 279], [444, 252, 491, 278], [553, 251, 601, 279]]}}