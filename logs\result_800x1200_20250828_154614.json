{"res": {"input_path": "logs\\debug_800x1200_screenshot_20250828_154614.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[34, 91], [91, 91], [91, 110], [34, 110]], [[145, 93], [201, 93], [201, 109], [145, 109]], [[256, 93], [310, 93], [310, 109], [256, 109]], [[366, 93], [420, 93], [420, 109], [366, 109]], [[476, 93], [529, 93], [529, 109], [476, 109]], [[585, 91], [641, 91], [641, 110], [585, 110]], [[8, 113], [70, 117], [69, 140], [7, 136]], [[118, 113], [179, 117], [177, 140], [116, 136]], [[229, 114], [290, 118], [288, 140], [227, 136]], [[338, 114], [400, 118], [398, 140], [336, 136]], [[448, 114], [509, 118], [507, 140], [446, 136]], [[558, 114], [620, 118], [618, 140], [556, 136]], [[34, 233], [91, 233], [91, 252], [34, 252]], [[137, 232], [207, 232], [207, 254], [137, 254]], [[254, 230], [311, 233], [310, 253], [253, 250]], [[365, 235], [421, 235], [421, 251], [365, 251]], [[474, 233], [531, 233], [531, 252], [474, 252]], [[584, 233], [640, 233], [640, 252], [584, 252]], [[8, 256], [69, 259], [68, 281], [7, 279]], [[119, 256], [202, 260], [201, 281], [118, 278]], [[228, 256], [399, 259], [398, 281], [228, 278]], [[448, 255], [510, 259], [508, 282], [446, 278]], [[558, 255], [619, 259], [617, 282], [556, 278]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x800", "主图2.jpg", "主图4jpg", "主图3.jpg", "主图4.jpg", "主图3.jpg", "主图2.jpg", "800x800", "800x1200", "800x800", "800x800", "800x800", "800x800", "主图1.jpg", "800x1200.jpg", "e7bbaed6-54d..主图3.jpg", "主图2.jpg", "主图4jpg"], "rec_scores": [0.9825901985168457, 0.9802833199501038, 0.9865900874137878, 0.975809633731842, 0.9529095888137817, 0.9626168012619019, 0.9850023984909058, 0.9884271025657654, 0.9548755288124084, 0.9163684248924255, 0.9383974075317383, 0.9645977020263672, 0.9726826548576355, 0.9507664442062378, 0.9494290947914124, 0.970280110836029, 0.9781584143638611, 0.9769505262374878, 0.9613441824913025, 0.9810711741447449, 0.9028609395027161, 0.9826864004135132, 0.9886454939842224], "rec_polys": [[[34, 91], [91, 91], [91, 110], [34, 110]], [[145, 93], [201, 93], [201, 109], [145, 109]], [[256, 93], [310, 93], [310, 109], [256, 109]], [[366, 93], [420, 93], [420, 109], [366, 109]], [[476, 93], [529, 93], [529, 109], [476, 109]], [[585, 91], [641, 91], [641, 110], [585, 110]], [[8, 113], [70, 117], [69, 140], [7, 136]], [[118, 113], [179, 117], [177, 140], [116, 136]], [[229, 114], [290, 118], [288, 140], [227, 136]], [[338, 114], [400, 118], [398, 140], [336, 136]], [[448, 114], [509, 118], [507, 140], [446, 136]], [[558, 114], [620, 118], [618, 140], [556, 136]], [[34, 233], [91, 233], [91, 252], [34, 252]], [[137, 232], [207, 232], [207, 254], [137, 254]], [[254, 230], [311, 233], [310, 253], [253, 250]], [[365, 235], [421, 235], [421, 251], [365, 251]], [[474, 233], [531, 233], [531, 252], [474, 252]], [[584, 233], [640, 233], [640, 252], [584, 252]], [[8, 256], [69, 259], [68, 281], [7, 279]], [[119, 256], [202, 260], [201, 281], [118, 278]], [[228, 256], [399, 259], [398, 281], [228, 278]], [[448, 255], [510, 259], [508, 282], [446, 278]], [[558, 255], [619, 259], [617, 282], [556, 278]]], "rec_boxes": [[34, 91, 91, 110], [145, 93, 201, 109], [256, 93, 310, 109], [366, 93, 420, 109], [476, 93, 529, 109], [585, 91, 641, 110], [7, 113, 70, 140], [116, 113, 179, 140], [227, 114, 290, 140], [336, 114, 400, 140], [446, 114, 509, 140], [556, 114, 620, 140], [34, 233, 91, 252], [137, 232, 207, 254], [253, 230, 311, 253], [365, 235, 421, 251], [474, 233, 531, 252], [584, 233, 640, 252], [7, 256, 69, 281], [118, 256, 202, 281], [228, 256, 399, 281], [446, 255, 510, 282], [556, 255, 619, 282]]}}