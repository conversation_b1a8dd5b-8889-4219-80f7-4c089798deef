{"res": {"input_path": "logs\\debug_800x1200_screenshot_20250822_171914.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[35, 93], [90, 93], [90, 109], [35, 109]], [[143, 91], [202, 91], [202, 110], [143, 110]], [[254, 91], [312, 91], [312, 110], [254, 110]], [[363, 87], [422, 90], [421, 112], [362, 109]], [[474, 92], [531, 92], [531, 111], [474, 111]], [[579, 90], [646, 90], [646, 112], [579, 112]], [[8, 113], [69, 117], [68, 140], [7, 136]], [[118, 113], [179, 117], [177, 140], [116, 136]], [[228, 113], [290, 117], [288, 140], [226, 136]], [[338, 113], [400, 117], [398, 140], [336, 136]], [[448, 115], [643, 117], [642, 139], [448, 137]], [[30, 233], [95, 233], [95, 252], [30, 252]], [[140, 233], [206, 233], [206, 252], [140, 252]], [[251, 233], [317, 233], [317, 252], [251, 252]], [[361, 233], [426, 233], [426, 252], [361, 252]], [[470, 232], [537, 232], [537, 254], [470, 254]], [[581, 233], [644, 233], [644, 252], [581, 252]], [[8, 256], [47, 259], [45, 284], [5, 280]], [[117, 256], [157, 259], [155, 284], [115, 280]], [[227, 256], [267, 259], [265, 284], [225, 280]], [[338, 256], [377, 259], [375, 284], [336, 280]], [[448, 255], [487, 260], [484, 284], [445, 279]], [[558, 256], [597, 259], [595, 284], [556, 280]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800×800", "800x800", "800x1200", "主图2.jpg", "主图3.jpg", "主图1.jpg", "主图4jpg", "c5b36672-c317... 800x1200.jpg", "900x1200", "900x1200", "900x1200", "900x1200", "900x1200", "900x1200", "9.jpg", "8.jpg", "7.jpg", "6.jpg", "4.jpg", "5.jpg"], "rec_scores": [0.9632061719894409, 0.9724928736686707, 0.9679413437843323, 0.9294008016586304, 0.9850487112998962, 0.9704707860946655, 0.9836010932922363, 0.9173563122749329, 0.9499945640563965, 0.9879143238067627, 0.8797011375427246, 0.9837999939918518, 0.9822750687599182, 0.9934276342391968, 0.9951374530792236, 0.9584088921546936, 0.9926226735115051, 0.9874563217163086, 0.9969016313552856, 0.990766167640686, 0.9968516230583191, 0.9921063184738159, 0.995775580406189], "rec_polys": [[[35, 93], [90, 93], [90, 109], [35, 109]], [[143, 91], [202, 91], [202, 110], [143, 110]], [[254, 91], [312, 91], [312, 110], [254, 110]], [[363, 87], [422, 90], [421, 112], [362, 109]], [[474, 92], [531, 92], [531, 111], [474, 111]], [[579, 90], [646, 90], [646, 112], [579, 112]], [[8, 113], [69, 117], [68, 140], [7, 136]], [[118, 113], [179, 117], [177, 140], [116, 136]], [[228, 113], [290, 117], [288, 140], [226, 136]], [[338, 113], [400, 117], [398, 140], [336, 136]], [[448, 115], [643, 117], [642, 139], [448, 137]], [[30, 233], [95, 233], [95, 252], [30, 252]], [[140, 233], [206, 233], [206, 252], [140, 252]], [[251, 233], [317, 233], [317, 252], [251, 252]], [[361, 233], [426, 233], [426, 252], [361, 252]], [[470, 232], [537, 232], [537, 254], [470, 254]], [[581, 233], [644, 233], [644, 252], [581, 252]], [[8, 256], [47, 259], [45, 284], [5, 280]], [[117, 256], [157, 259], [155, 284], [115, 280]], [[227, 256], [267, 259], [265, 284], [225, 280]], [[338, 256], [377, 259], [375, 284], [336, 280]], [[448, 255], [487, 260], [484, 284], [445, 279]], [[558, 256], [597, 259], [595, 284], [556, 280]]], "rec_boxes": [[35, 93, 90, 109], [143, 91, 202, 110], [254, 91, 312, 110], [362, 87, 422, 112], [474, 92, 531, 111], [579, 90, 646, 112], [7, 113, 69, 140], [116, 113, 179, 140], [226, 113, 290, 140], [336, 113, 400, 140], [448, 115, 643, 139], [30, 233, 95, 252], [140, 233, 206, 252], [251, 233, 317, 252], [361, 233, 426, 252], [470, 232, 537, 254], [581, 233, 644, 252], [5, 256, 47, 284], [115, 256, 157, 284], [225, 256, 267, 284], [336, 256, 377, 284], [445, 255, 487, 284], [556, 256, 597, 284]]}}