[15:31:56] [    INFO] - === PyAutoGUI初始设置 ===
[15:31:56] [    INFO] - FAILSAFE: True
[15:31:56] [    INFO] - PAUSE: 0.1
[15:31:56] [    INFO] - MINIMUM_DURATION: 0.1
[15:31:56] [    INFO] - MINIMUM_SLEEP: 0.05
[15:31:56] [    INFO] - =====================
[15:31:56] [    INFO] - 初始鼠标位置: (996, 300)
[15:31:57] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[15:31:57] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150, 140]
[15:31:57] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150, 140]
[15:31:57] [    INFO] - 初始鼠标位置: (960, 540)
[15:31:57] [    INFO] - 即将开始删除码数操作...
[15:31:58] [    INFO] - 开始删除指定尺码...
[15:31:59] [    INFO] - 准备删除尺码: 170
[15:31:59] [    INFO] - 执行删除: 170码
[15:32:01] [    INFO] - 准备删除尺码: 160
[15:32:01] [    INFO] - 执行删除: 160码
[15:32:03] [    INFO] - 准备删除尺码: 150
[15:32:04] [    INFO] - 执行删除: 150码
[15:32:05] [    INFO] - 准备删除尺码: 140
[15:32:06] [    INFO] - 执行删除: 140码
[15:32:08] [    INFO] - 开始执行向下滚动...
[15:32:08] [    INFO] - 当前鼠标位置: (830, 559)
[15:32:08] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[15:32:08] [    INFO] - 等待0.5秒...
[15:32:09] [    INFO] - 开始滚动...
[15:32:09] [    INFO] - 滚动完成，等待1秒...
[15:32:10] [    INFO] - 滚动后鼠标位置: (1600, 510)
[15:32:10] [    INFO] - 滚动操作完成
[15:32:10] [    INFO] - 开始查找图片: assets\images\piliang.png
[15:32:10] [    INFO] - 使用的置信度: 0.98
[15:32:10] [    INFO] - 查找前鼠标位置: (1600, 510)
[15:32:10] [    INFO] - 图片匹配的最高置信度: 1.000
[15:32:10] [    INFO] - 找到图片，位置信息: Box(left=912, top=311, width=71, height=25)
[15:32:10] [    INFO] - 将点击图片左上角附近位置: (917, 316)
[15:32:11] [    INFO] - 点击后鼠标位置: (917, 316)
[15:32:11] [    INFO] - 等待0.5秒确保弹窗显示完全
[15:32:11] [    INFO] - 随机数生成结果: 0.6170896970443882
[15:32:11] [    INFO] - 本次执行将按照先点击上市，后输入数量的顺序进行
[15:32:11] [    INFO] - 执行顺序：先点击上市，后输入数量
[15:32:11] [    INFO] - 开始执行点击上市操作
[15:32:11] [    INFO] - 开始查找图片: assets\images\shangshi.png
[15:32:11] [    INFO] - 使用的置信度: 0.75
[15:32:11] [    INFO] - 查找前鼠标位置: (917, 316)
[15:32:11] [    INFO] - 图片匹配的最高置信度: 1.000
[15:32:11] [    INFO] - 找到图片，位置信息: Box(left=513, top=570, width=54, height=23)
[15:32:11] [    INFO] - 将点击图片左上角附近位置: (518, 575)
[15:32:12] [    INFO] - 点击后鼠标位置: (518, 575)
[15:32:13] [    INFO] - 开始查找图片: assets\images\yuefen.png
[15:32:13] [    INFO] - 使用的置信度: 0.75
[15:32:13] [    INFO] - 查找前鼠标位置: (518, 575)
[15:32:13] [    INFO] - 图片匹配的最高置信度: 1.000
[15:32:13] [    INFO] - 找到图片，位置信息: Box(left=599, top=758, width=39, height=24)
[15:32:13] [    INFO] - 将点击图片左上角附近位置: (604, 763)
[15:32:13] [    INFO] - 点击后鼠标位置: (604, 763)
[15:32:14] [    INFO] - 开始执行输入数量操作
[15:32:14] [    INFO] - 开始查找图片: assets\images\shuliang.png
[15:32:14] [    INFO] - 使用的置信度: 0.75
[15:32:14] [    INFO] - 查找前鼠标位置: (604, 763)
[15:32:14] [    INFO] - 图片匹配的最高置信度: 1.000
[15:32:14] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[15:32:14] [    INFO] - 将点击图片中心位置: (966, 483)
[15:32:14] [    INFO] - 点击后鼠标位置: (966, 483)
[15:32:15] [    INFO] - 开始查找图片: assets\images\tianchong.png
[15:32:15] [    INFO] - 使用的置信度: 0.98
[15:32:15] [    INFO] - 查找前鼠标位置: (966, 483)
[15:32:15] [    INFO] - 图片匹配的最高置信度: 1.000
[15:32:15] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[15:32:15] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[15:32:16] [    INFO] - 点击后鼠标位置: (1258, 855)
[15:32:16] [    INFO] - 开始查找图片: assets\images\tuwen.png
[15:32:16] [    INFO] - 使用的置信度: 0.98
[15:32:16] [    INFO] - 查找前鼠标位置: (1258, 855)
[15:32:16] [    INFO] - 图片匹配的最高置信度: 1.000
[15:32:16] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[15:32:16] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[15:32:17] [    INFO] - 点击后鼠标位置: (320, 123)
[15:32:17] [    INFO] - 所有操作已处理完成
[15:32:17] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150, 140]
[15:32:17] [    INFO] - 已创建完成信号文件
[15:39:02] [    INFO] - === PyAutoGUI初始设置 ===
[15:39:02] [    INFO] - FAILSAFE: True
[15:39:02] [    INFO] - PAUSE: 0.1
[15:39:02] [    INFO] - MINIMUM_DURATION: 0.1
[15:39:02] [    INFO] - MINIMUM_SLEEP: 0.05
[15:39:02] [    INFO] - =====================
[15:39:02] [    INFO] - 初始鼠标位置: (555, 300)
[15:39:02] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[15:39:03] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150]
[15:39:03] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150]
[15:39:03] [    INFO] - 初始鼠标位置: (960, 540)
[15:39:03] [    INFO] - 即将开始删除码数操作...
[15:39:04] [    INFO] - 开始删除指定尺码...
[15:39:04] [    INFO] - 准备删除尺码: 170
[15:39:05] [    INFO] - 执行删除: 170码
[15:39:06] [    INFO] - 准备删除尺码: 160
[15:39:07] [    INFO] - 执行删除: 160码
[15:39:08] [    INFO] - 准备删除尺码: 150
[15:39:09] [    INFO] - 执行删除: 150码
[15:39:12] [    INFO] - 开始执行向下滚动...
[15:39:12] [    INFO] - 当前鼠标位置: (1234, 558)
[15:39:12] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[15:39:12] [    INFO] - 等待0.5秒...
[15:39:12] [    INFO] - 开始滚动...
[15:39:12] [    INFO] - 滚动完成，等待1秒...
[15:39:13] [    INFO] - 滚动后鼠标位置: (1600, 510)
[15:39:13] [    INFO] - 滚动操作完成
[15:39:13] [    INFO] - 开始查找图片: assets\images\piliang.png
[15:39:13] [    INFO] - 使用的置信度: 0.98
[15:39:13] [    INFO] - 查找前鼠标位置: (1600, 510)
[15:39:14] [    INFO] - 图片匹配的最高置信度: 1.000
[15:39:14] [    INFO] - 找到图片，位置信息: Box(left=912, top=365, width=71, height=25)
[15:39:14] [    INFO] - 将点击图片左上角附近位置: (917, 370)
[15:39:14] [    INFO] - 点击后鼠标位置: (917, 370)
[15:39:15] [    INFO] - 等待0.5秒确保弹窗显示完全
[15:39:15] [    INFO] - 随机数生成结果: 0.21036189758487145
[15:39:15] [    INFO] - 本次执行将按照先输入数量，后点击上市的顺序进行
[15:39:15] [    INFO] - 执行顺序：先输入数量，后点击上市
[15:39:15] [    INFO] - 开始执行输入数量操作
[15:39:15] [    INFO] - 开始查找图片: assets\images\shuliang.png
[15:39:15] [    INFO] - 使用的置信度: 0.75
[15:39:15] [    INFO] - 查找前鼠标位置: (917, 370)
[15:39:15] [    INFO] - 图片匹配的最高置信度: 1.000
[15:39:15] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[15:39:15] [    INFO] - 将点击图片中心位置: (966, 483)
[15:39:15] [    INFO] - 点击后鼠标位置: (966, 483)
[15:39:16] [    INFO] - 开始执行点击上市操作
[15:39:16] [    INFO] - 开始查找图片: assets\images\shangshi.png
[15:39:16] [    INFO] - 使用的置信度: 0.75
[15:39:16] [    INFO] - 查找前鼠标位置: (966, 483)
[15:39:16] [    INFO] - 图片匹配的最高置信度: 1.000
[15:39:16] [    INFO] - 找到图片，位置信息: Box(left=513, top=570, width=54, height=23)
[15:39:16] [    INFO] - 将点击图片左上角附近位置: (518, 575)
[15:39:16] [    INFO] - 点击后鼠标位置: (518, 575)
[15:39:17] [    INFO] - 开始查找图片: assets\images\yuefen.png
[15:39:17] [    INFO] - 使用的置信度: 0.75
[15:39:17] [    INFO] - 查找前鼠标位置: (518, 575)
[15:39:17] [    INFO] - 图片匹配的最高置信度: 1.000
[15:39:17] [    INFO] - 找到图片，位置信息: Box(left=599, top=758, width=39, height=24)
[15:39:17] [    INFO] - 将点击图片左上角附近位置: (604, 763)
[15:39:17] [    INFO] - 点击后鼠标位置: (604, 763)
[15:39:18] [    INFO] - 开始查找图片: assets\images\tianchong.png
[15:39:18] [    INFO] - 使用的置信度: 0.98
[15:39:18] [    INFO] - 查找前鼠标位置: (604, 763)
[15:39:18] [    INFO] - 图片匹配的最高置信度: 1.000
[15:39:19] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[15:39:19] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[15:39:19] [    INFO] - 点击后鼠标位置: (1258, 855)
[15:39:19] [    INFO] - 开始查找图片: assets\images\tuwen.png
[15:39:19] [    INFO] - 使用的置信度: 0.98
[15:39:19] [    INFO] - 查找前鼠标位置: (1258, 855)
[15:39:20] [    INFO] - 图片匹配的最高置信度: 1.000
[15:39:20] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[15:39:20] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[15:39:20] [    INFO] - 点击后鼠标位置: (320, 123)
[15:39:20] [    INFO] - 所有操作已处理完成
[15:39:20] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150]
[15:39:20] [    INFO] - 已创建完成信号文件
[15:44:44] [    INFO] - === PyAutoGUI初始设置 ===
[15:44:44] [    INFO] - FAILSAFE: True
[15:44:44] [    INFO] - PAUSE: 0.1
[15:44:44] [    INFO] - MINIMUM_DURATION: 0.1
[15:44:44] [    INFO] - MINIMUM_SLEEP: 0.05
[15:44:44] [    INFO] - =====================
[15:44:44] [    INFO] - 初始鼠标位置: (555, 299)
[15:44:44] [    INFO] - 平滑移动鼠标到屏幕中心: (960, 540)
[15:44:45] [    INFO] - 从文件读取到需要删除的尺码: [170, 160, 150, 140]
[15:44:45] [    INFO] - 原始需要删除的尺码列表: [170, 160, 150, 140]
[15:44:45] [    INFO] - 初始鼠标位置: (960, 540)
[15:44:45] [    INFO] - 即将开始删除码数操作...
[15:44:46] [    INFO] - 开始删除指定尺码...
[15:44:46] [    INFO] - 准备删除尺码: 170
[15:44:47] [    INFO] - 执行删除: 170码
[15:44:48] [    INFO] - 准备删除尺码: 160
[15:44:49] [    INFO] - 执行删除: 160码
[15:44:50] [    INFO] - 准备删除尺码: 150
[15:44:51] [    INFO] - 执行删除: 150码
[15:44:52] [    INFO] - 准备删除尺码: 140
[15:44:53] [    INFO] - 执行删除: 140码
[15:44:56] [    INFO] - 开始执行向下滚动...
[15:44:56] [    INFO] - 当前鼠标位置: (830, 559)
[15:44:56] [    INFO] - 移动鼠标到指定位置: (1600, 510)
[15:44:56] [    INFO] - 等待0.5秒...
[15:44:56] [    INFO] - 开始滚动...
[15:44:56] [    INFO] - 滚动完成，等待1秒...
[15:44:57] [    INFO] - 滚动后鼠标位置: (1600, 510)
[15:44:57] [    INFO] - 滚动操作完成
[15:44:57] [    INFO] - 开始查找图片: assets\images\piliang.png
[15:44:57] [    INFO] - 使用的置信度: 0.98
[15:44:57] [    INFO] - 查找前鼠标位置: (1600, 510)
[15:44:58] [    INFO] - 图片匹配的最高置信度: 1.000
[15:44:58] [    INFO] - 找到图片，位置信息: Box(left=912, top=310, width=71, height=25)
[15:44:58] [    INFO] - 将点击图片左上角附近位置: (917, 315)
[15:44:58] [    INFO] - 点击后鼠标位置: (917, 315)
[15:44:59] [    INFO] - 等待0.5秒确保弹窗显示完全
[15:44:59] [    INFO] - 随机数生成结果: 0.6573602737849478
[15:44:59] [    INFO] - 本次执行将按照先点击上市，后输入数量的顺序进行
[15:44:59] [    INFO] - 执行顺序：先点击上市，后输入数量
[15:44:59] [    INFO] - 开始执行点击上市操作
[15:44:59] [    INFO] - 开始查找图片: assets\images\shangshi.png
[15:44:59] [    INFO] - 使用的置信度: 0.75
[15:44:59] [    INFO] - 查找前鼠标位置: (917, 315)
[15:44:59] [    INFO] - 图片匹配的最高置信度: 1.000
[15:44:59] [    INFO] - 找到图片，位置信息: Box(left=513, top=570, width=54, height=23)
[15:44:59] [    INFO] - 将点击图片左上角附近位置: (518, 575)
[15:44:59] [    INFO] - 点击后鼠标位置: (518, 575)
[15:45:00] [    INFO] - 开始查找图片: assets\images\yuefen.png
[15:45:00] [    INFO] - 使用的置信度: 0.75
[15:45:00] [    INFO] - 查找前鼠标位置: (518, 575)
[15:45:00] [    INFO] - 图片匹配的最高置信度: 1.000
[15:45:00] [    INFO] - 找到图片，位置信息: Box(left=599, top=758, width=39, height=24)
[15:45:00] [    INFO] - 将点击图片左上角附近位置: (604, 763)
[15:45:00] [    INFO] - 点击后鼠标位置: (604, 763)
[15:45:01] [    INFO] - 开始执行输入数量操作
[15:45:01] [    INFO] - 开始查找图片: assets\images\shuliang.png
[15:45:01] [    INFO] - 使用的置信度: 0.75
[15:45:01] [    INFO] - 查找前鼠标位置: (604, 763)
[15:45:01] [    INFO] - 图片匹配的最高置信度: 1.000
[15:45:01] [    INFO] - 找到图片，位置信息: Box(left=930, top=439, width=72, height=88)
[15:45:01] [    INFO] - 将点击图片中心位置: (966, 483)
[15:45:02] [    INFO] - 点击后鼠标位置: (966, 483)
[15:45:02] [    INFO] - 开始查找图片: assets\images\tianchong.png
[15:45:02] [    INFO] - 使用的置信度: 0.98
[15:45:02] [    INFO] - 查找前鼠标位置: (966, 483)
[15:45:03] [    INFO] - 图片匹配的最高置信度: 1.000
[15:45:03] [    INFO] - 找到图片，位置信息: Box(left=1253, top=850, width=71, height=27)
[15:45:03] [    INFO] - 将点击图片左上角附近位置: (1258, 855)
[15:45:03] [    INFO] - 点击后鼠标位置: (1258, 855)
[15:45:03] [    INFO] - 开始查找图片: assets\images\tuwen.png
[15:45:03] [    INFO] - 使用的置信度: 0.98
[15:45:03] [    INFO] - 查找前鼠标位置: (1258, 855)
[15:45:04] [    INFO] - 图片匹配的最高置信度: 1.000
[15:45:04] [    INFO] - 找到图片，位置信息: Box(left=315, top=118, width=71, height=33)
[15:45:04] [    INFO] - 将点击图片左上角附近位置: (320, 123)
[15:45:04] [    INFO] - 点击后鼠标位置: (320, 123)
[15:45:04] [    INFO] - 所有操作已处理完成
[15:45:04] [    INFO] - 码数删除完成，处理的尺码: [170, 160, 150, 140]
[15:45:04] [    INFO] - 已创建完成信号文件
