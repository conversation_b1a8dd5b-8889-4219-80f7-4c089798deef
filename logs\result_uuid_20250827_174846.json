{"res": {"input_path": "logs\\debug_uuid_screenshot_20250827_174846.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[142, 87], [199, 87], [199, 106], [142, 106]], [[252, 83], [309, 86], [308, 108], [251, 105]], [[362, 87], [419, 87], [419, 106], [362, 106]], [[472, 88], [528, 88], [528, 107], [472, 107]], [[577, 86], [643, 86], [643, 108], [577, 108]], [[6, 110], [68, 113], [67, 135], [5, 133]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[226, 109], [288, 113], [286, 136], [224, 132]], [[336, 109], [398, 113], [396, 136], [334, 132]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[33, 231], [88, 231], [88, 247], [33, 247]], [[143, 231], [198, 231], [198, 247], [143, 247]], [[249, 231], [313, 231], [313, 247], [249, 247]], [[359, 231], [422, 231], [422, 247], [359, 247]], [[473, 229], [528, 229], [528, 248], [473, 248]], [[582, 231], [637, 231], [637, 247], [582, 247]], [[5, 251], [45, 256], [43, 280], [2, 275]], [[115, 252], [155, 255], [153, 280], [113, 276]], [[225, 251], [265, 254], [263, 280], [223, 276]], [[335, 252], [375, 255], [373, 280], [333, 276]], [[445, 251], [485, 255], [483, 280], [443, 276]], [[555, 251], [595, 255], [592, 280], [553, 276]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图4.jpg", "主图2.jpg", "主图3.jpg", "主图1.jpg", "cacb7d7b-80f2.. 800x1200.jpg", "900x900", "900x900", "900x1200", "900x1200", "900x900", "900x900", "7.jpg", "6.jpg", "4.jpg", "5.jpg", "2.jpg", "3.jpg"], "rec_scores": [0.9643765091896057, 0.9693625569343567, 0.9233837127685547, 0.9704003930091858, 0.9757771492004395, 0.9648613333702087, 0.8673664927482605, 0.9690300226211548, 0.9226723313331604, 0.9499945640563965, 0.8993047475814819, 0.9544232487678528, 0.9844313859939575, 0.9891282320022583, 0.9878009557723999, 0.9649323225021362, 0.963640570640564, 0.9900747537612915, 0.9963321685791016, 0.9806004762649536, 0.9966920614242554, 0.9964987635612488, 0.9942895770072937], "rec_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[142, 87], [199, 87], [199, 106], [142, 106]], [[252, 83], [309, 86], [308, 108], [251, 105]], [[362, 87], [419, 87], [419, 106], [362, 106]], [[472, 88], [528, 88], [528, 107], [472, 107]], [[577, 86], [643, 86], [643, 108], [577, 108]], [[6, 110], [68, 113], [67, 135], [5, 133]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[226, 109], [288, 113], [286, 136], [224, 132]], [[336, 109], [398, 113], [396, 136], [334, 132]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[33, 231], [88, 231], [88, 247], [33, 247]], [[143, 231], [198, 231], [198, 247], [143, 247]], [[249, 231], [313, 231], [313, 247], [249, 247]], [[359, 231], [422, 231], [422, 247], [359, 247]], [[473, 229], [528, 229], [528, 248], [473, 248]], [[582, 231], [637, 231], [637, 247], [582, 247]], [[5, 251], [45, 256], [43, 280], [2, 275]], [[115, 252], [155, 255], [153, 280], [113, 276]], [[225, 251], [265, 254], [263, 280], [223, 276]], [[335, 252], [375, 255], [373, 280], [333, 276]], [[445, 251], [485, 255], [483, 280], [443, 276]], [[555, 251], [595, 255], [592, 280], [553, 276]]], "rec_boxes": [[32, 87, 89, 106], [142, 87, 199, 106], [251, 83, 309, 108], [362, 87, 419, 106], [472, 88, 528, 107], [577, 86, 643, 108], [5, 110, 68, 135], [114, 109, 179, 137], [224, 109, 288, 136], [334, 109, 398, 136], [446, 111, 641, 135], [33, 231, 88, 247], [143, 231, 198, 247], [249, 231, 313, 247], [359, 231, 422, 247], [473, 229, 528, 248], [582, 231, 637, 247], [2, 251, 45, 280], [113, 252, 155, 280], [223, 251, 265, 280], [333, 252, 375, 280], [443, 251, 485, 280], [553, 251, 595, 280]]}}