{"res": {"input_path": "logs\\debug_uuid_screenshot_20250824_174030.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 87], [90, 87], [90, 106], [32, 106]], [[142, 87], [200, 87], [200, 106], [142, 106]], [[251, 86], [311, 86], [311, 108], [251, 108]], [[361, 86], [421, 86], [421, 108], [361, 108]], [[471, 86], [530, 86], [530, 108], [471, 108]], [[578, 87], [642, 87], [642, 106], [578, 106]], [[6, 110], [67, 113], [66, 136], [5, 134]], [[116, 109], [178, 112], [177, 137], [115, 135]], [[226, 108], [288, 112], [286, 136], [224, 132]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[28, 229], [93, 229], [93, 248], [28, 248]], [[139, 229], [204, 229], [204, 248], [139, 248]], [[248, 229], [313, 229], [313, 248], [248, 248]], [[358, 229], [423, 229], [423, 248], [358, 248]], [[469, 229], [534, 229], [534, 248], [469, 248]], [[579, 229], [642, 229], [642, 248], [579, 248]], [[5, 252], [52, 255], [50, 278], [4, 275]], [[117, 251], [162, 255], [160, 280], [114, 275]], [[225, 251], [272, 255], [270, 280], [223, 275]], [[334, 250], [382, 254], [380, 281], [332, 276]], [[445, 251], [485, 254], [483, 280], [443, 276]], [[555, 251], [602, 255], [600, 279], [553, 274]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图3.jpg", "主图4.jpg", "主图2.jpg", "主图1.jpg", "08741f44-f08b-.800x1200.jpg", "900x1200", "900x1200", "900x1200", "900x1200", "900x1200", "900x1200", "14.jpg", "12.jpg", "13.jpg", "11.jpg", "9.jpg", "10.jpg"], "rec_scores": [0.960908055305481, 0.9618664979934692, 0.9547110795974731, 0.9673557281494141, 0.9698489308357239, 0.968810498714447, 0.9639464616775513, 0.969749927520752, 0.9908202886581421, 0.9488903284072876, 0.9015847444534302, 0.9846407771110535, 0.9919883012771606, 0.9866886138916016, 0.9880240559577942, 0.99153733253479, 0.9868231415748596, 0.9910295009613037, 0.9922928214073181, 0.9947006702423096, 0.9483898282051086, 0.960169792175293, 0.951492965221405], "rec_polys": [[[32, 87], [90, 87], [90, 106], [32, 106]], [[142, 87], [200, 87], [200, 106], [142, 106]], [[251, 86], [311, 86], [311, 108], [251, 108]], [[361, 86], [421, 86], [421, 108], [361, 108]], [[471, 86], [530, 86], [530, 108], [471, 108]], [[578, 87], [642, 87], [642, 106], [578, 106]], [[6, 110], [67, 113], [66, 136], [5, 134]], [[116, 109], [178, 112], [177, 137], [115, 135]], [[226, 108], [288, 112], [286, 136], [224, 132]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[28, 229], [93, 229], [93, 248], [28, 248]], [[139, 229], [204, 229], [204, 248], [139, 248]], [[248, 229], [313, 229], [313, 248], [248, 248]], [[358, 229], [423, 229], [423, 248], [358, 248]], [[469, 229], [534, 229], [534, 248], [469, 248]], [[579, 229], [642, 229], [642, 248], [579, 248]], [[5, 252], [52, 255], [50, 278], [4, 275]], [[117, 251], [162, 255], [160, 280], [114, 275]], [[225, 251], [272, 255], [270, 280], [223, 275]], [[334, 250], [382, 254], [380, 281], [332, 276]], [[445, 251], [485, 254], [483, 280], [443, 276]], [[555, 251], [602, 255], [600, 279], [553, 274]]], "rec_boxes": [[32, 87, 90, 106], [142, 87, 200, 106], [251, 86, 311, 108], [361, 86, 421, 108], [471, 86, 530, 108], [578, 87, 642, 106], [5, 110, 67, 136], [115, 109, 178, 137], [224, 108, 288, 136], [333, 109, 399, 137], [446, 111, 641, 135], [28, 229, 93, 248], [139, 229, 204, 248], [248, 229, 313, 248], [358, 229, 423, 248], [469, 229, 534, 248], [579, 229, 642, 248], [4, 252, 52, 278], [114, 251, 162, 280], [223, 251, 272, 280], [332, 250, 382, 281], [443, 251, 485, 280], [553, 251, 602, 279]]}}