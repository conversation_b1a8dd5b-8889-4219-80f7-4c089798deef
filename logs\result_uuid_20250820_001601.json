{"res": {"input_path": "logs\\debug_uuid_screenshot_20250820_001601.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[142, 84], [200, 87], [199, 107], [141, 104]], [[252, 87], [310, 87], [310, 106], [252, 106]], [[362, 87], [420, 87], [420, 106], [362, 106]], [[473, 89], [528, 89], [528, 105], [473, 105]], [[577, 86], [643, 86], [643, 108], [577, 108]], [[6, 109], [68, 113], [67, 136], [5, 132]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[226, 109], [288, 113], [286, 136], [224, 132]], [[336, 109], [398, 113], [396, 136], [334, 132]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[32, 229], [89, 229], [89, 248], [32, 248]], [[142, 229], [200, 229], [200, 248], [142, 248]], [[253, 231], [308, 231], [308, 247], [253, 247]], [[362, 229], [419, 229], [419, 248], [362, 248]], [[474, 231], [527, 231], [527, 247], [474, 247]], [[584, 231], [635, 231], [635, 247], [584, 247]], [[6, 251], [52, 256], [50, 280], [3, 275]], [[116, 252], [161, 255], [159, 278], [114, 275]], [[226, 251], [265, 255], [263, 280], [223, 276]], [[335, 252], [375, 255], [373, 280], [333, 276]], [[445, 252], [485, 255], [483, 280], [443, 276]], [[555, 252], [595, 255], [593, 280], [553, 276]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图2.jpg", "主图3.jpg", "主图4jpg", "主图1.jpg", "f725ed19-3822... 800x1200.jpg", "900x900", "900x900", "900x900", "900x900", "900x886", "900x897", "11,jpg", "10.jpg", "8.jpg", "9.jpg", "6.jpg", "7.jpg"], "rec_scores": [0.9612919092178345, 0.949938178062439, 0.9711866974830627, 0.9771575927734375, 0.9845039248466492, 0.9717302918434143, 0.9850023984909058, 0.941493809223175, 0.9879143238067627, 0.9499945640563965, 0.8885253667831421, 0.9626809358596802, 0.9473604559898376, 0.9474560618400574, 0.9639809727668762, 0.9783911108970642, 0.9848924875259399, 0.9349330067634583, 0.9182401299476624, 0.9943461418151855, 0.9945552945137024, 0.9963321685791016, 0.990766167640686], "rec_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[142, 84], [200, 87], [199, 107], [141, 104]], [[252, 87], [310, 87], [310, 106], [252, 106]], [[362, 87], [420, 87], [420, 106], [362, 106]], [[473, 89], [528, 89], [528, 105], [473, 105]], [[577, 86], [643, 86], [643, 108], [577, 108]], [[6, 109], [68, 113], [67, 136], [5, 132]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[226, 109], [288, 113], [286, 136], [224, 132]], [[336, 109], [398, 113], [396, 136], [334, 132]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[32, 229], [89, 229], [89, 248], [32, 248]], [[142, 229], [200, 229], [200, 248], [142, 248]], [[253, 231], [308, 231], [308, 247], [253, 247]], [[362, 229], [419, 229], [419, 248], [362, 248]], [[474, 231], [527, 231], [527, 247], [474, 247]], [[584, 231], [635, 231], [635, 247], [584, 247]], [[6, 251], [52, 256], [50, 280], [3, 275]], [[116, 252], [161, 255], [159, 278], [114, 275]], [[226, 251], [265, 255], [263, 280], [223, 276]], [[335, 252], [375, 255], [373, 280], [333, 276]], [[445, 252], [485, 255], [483, 280], [443, 276]], [[555, 252], [595, 255], [593, 280], [553, 276]]], "rec_boxes": [[32, 87, 89, 106], [141, 84, 200, 107], [252, 87, 310, 106], [362, 87, 420, 106], [473, 89, 528, 105], [577, 86, 643, 108], [5, 109, 68, 136], [114, 109, 179, 137], [224, 109, 288, 136], [334, 109, 398, 136], [446, 111, 641, 135], [32, 229, 89, 248], [142, 229, 200, 248], [253, 231, 308, 247], [362, 229, 419, 248], [474, 231, 527, 247], [584, 231, 635, 247], [3, 251, 52, 280], [114, 252, 161, 278], [223, 251, 265, 280], [333, 252, 375, 280], [443, 252, 485, 280], [553, 252, 595, 280]]}}