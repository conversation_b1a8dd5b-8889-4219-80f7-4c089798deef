{"res": {"input_path": "logs\\debug_800x1200_screenshot_20250826_174752.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[35, 93], [90, 93], [90, 109], [35, 109]], [[144, 91], [202, 91], [202, 110], [144, 110]], [[254, 91], [312, 91], [312, 110], [254, 110]], [[363, 90], [421, 90], [421, 109], [363, 109]], [[474, 91], [530, 91], [530, 110], [474, 110]], [[578, 90], [644, 90], [644, 109], [578, 109]], [[8, 114], [70, 118], [69, 140], [7, 136]], [[118, 113], [179, 117], [177, 140], [116, 136]], [[228, 113], [290, 117], [288, 140], [226, 136]], [[338, 113], [400, 117], [398, 140], [336, 136]], [[448, 115], [643, 117], [642, 139], [448, 137]], [[30, 233], [95, 233], [95, 252], [30, 252]], [[140, 232], [207, 232], [207, 254], [140, 254]], [[251, 233], [316, 233], [316, 252], [251, 252]], [[361, 233], [425, 233], [425, 252], [361, 252]], [[468, 233], [536, 233], [536, 252], [468, 252]], [[580, 232], [645, 232], [645, 254], [580, 254]], [[8, 256], [47, 259], [45, 284], [5, 280]], [[117, 256], [157, 259], [155, 284], [115, 280]], [[228, 255], [267, 259], [265, 284], [225, 280]], [[338, 256], [377, 259], [375, 284], [336, 280]], [[448, 255], [487, 259], [485, 284], [446, 280]], [[558, 259], [596, 259], [596, 282], [558, 282]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图3.jpg", "主图2.jpg", "主图1.jpg", "主图4jpg", "f9d93fba-4bb. 800x1200.jpg", "900x1200", "900x1200", "900x1200", "900x1200", "900x1200", "900x1200", "8.jpg", "7.jpg", "6.jpg", "5.jpg", "4.jpg", "3.jpg"], "rec_scores": [0.9701887965202332, 0.9634299278259277, 0.9663345217704773, 0.9614769220352173, 0.9701954126358032, 0.9745342135429382, 0.9249511361122131, 0.9792531728744507, 0.9499945640563965, 0.9879143238067627, 0.8947385549545288, 0.9751057624816895, 0.9851639270782471, 0.984366238117218, 0.9850397109985352, 0.9757574796676636, 0.9704362154006958, 0.9944008588790894, 0.990766167640686, 0.9880026578903198, 0.995775580406189, 0.9662503004074097, 0.9847325086593628], "rec_polys": [[[35, 93], [90, 93], [90, 109], [35, 109]], [[144, 91], [202, 91], [202, 110], [144, 110]], [[254, 91], [312, 91], [312, 110], [254, 110]], [[363, 90], [421, 90], [421, 109], [363, 109]], [[474, 91], [530, 91], [530, 110], [474, 110]], [[578, 90], [644, 90], [644, 109], [578, 109]], [[8, 114], [70, 118], [69, 140], [7, 136]], [[118, 113], [179, 117], [177, 140], [116, 136]], [[228, 113], [290, 117], [288, 140], [226, 136]], [[338, 113], [400, 117], [398, 140], [336, 136]], [[448, 115], [643, 117], [642, 139], [448, 137]], [[30, 233], [95, 233], [95, 252], [30, 252]], [[140, 232], [207, 232], [207, 254], [140, 254]], [[251, 233], [316, 233], [316, 252], [251, 252]], [[361, 233], [425, 233], [425, 252], [361, 252]], [[468, 233], [536, 233], [536, 252], [468, 252]], [[580, 232], [645, 232], [645, 254], [580, 254]], [[8, 256], [47, 259], [45, 284], [5, 280]], [[117, 256], [157, 259], [155, 284], [115, 280]], [[228, 255], [267, 259], [265, 284], [225, 280]], [[338, 256], [377, 259], [375, 284], [336, 280]], [[448, 255], [487, 259], [485, 284], [446, 280]], [[558, 259], [596, 259], [596, 282], [558, 282]]], "rec_boxes": [[35, 93, 90, 109], [144, 91, 202, 110], [254, 91, 312, 110], [363, 90, 421, 109], [474, 91, 530, 110], [578, 90, 644, 109], [7, 114, 70, 140], [116, 113, 179, 140], [226, 113, 290, 140], [336, 113, 400, 140], [448, 115, 643, 139], [30, 233, 95, 252], [140, 232, 207, 254], [251, 233, 316, 252], [361, 233, 425, 252], [468, 233, 536, 252], [580, 232, 645, 254], [5, 256, 47, 284], [115, 256, 157, 284], [225, 255, 267, 284], [336, 256, 377, 284], [446, 255, 487, 284], [558, 259, 596, 282]]}}