{"res": {"input_path": "logs\\debug_uuid_screenshot_20250824_170005.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[33, 89], [88, 89], [88, 105], [33, 105]], [[142, 87], [200, 87], [200, 106], [142, 106]], [[252, 87], [310, 87], [310, 106], [252, 106]], [[361, 86], [421, 86], [421, 108], [361, 108]], [[470, 86], [531, 86], [531, 108], [470, 108]], [[577, 86], [643, 86], [643, 108], [577, 108]], [[6, 109], [68, 113], [67, 136], [5, 132]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[225, 108], [289, 112], [287, 138], [223, 134]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[32, 229], [91, 229], [91, 248], [32, 248]], [[143, 229], [199, 229], [199, 248], [143, 248]], [[252, 228], [310, 228], [310, 250], [252, 250]], [[362, 229], [419, 229], [419, 248], [362, 248]], [[473, 229], [529, 229], [529, 248], [473, 248]], [[584, 231], [637, 231], [637, 247], [584, 247]], [[6, 251], [45, 255], [43, 280], [3, 276]], [[115, 252], [155, 255], [153, 280], [113, 276]], [[226, 251], [265, 255], [263, 280], [223, 276]], [[335, 252], [375, 255], [373, 280], [333, 276]], [[445, 251], [485, 254], [483, 280], [443, 276]], [[556, 251], [618, 255], [616, 278], [554, 274]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图4.jpg", "主图3.jpg", "主图2.jpg", "主图1.jpg", "aafd2c2a-4629.. 800x1200.jpg", "900x900", "900x900", "900x900", "900x900", "900x900", "800x800", "6.jpg", "5.jpg", "4.jpg", "3.jpg", "1.jpg", "主图3.jpg"], "rec_scores": [0.9713403582572937, 0.9769381284713745, 0.9628368020057678, 0.9356493353843689, 0.9483903646469116, 0.9609195590019226, 0.9218339920043945, 0.941493809223175, 0.894110381603241, 0.9488903284072876, 0.8770894408226013, 0.9449585676193237, 0.9462906718254089, 0.9480436444282532, 0.942105233669281, 0.9509153366088867, 0.9711683988571167, 0.9880026578903198, 0.9966920614242554, 0.9902734756469727, 0.9970253705978394, 0.8986754417419434, 0.9226723313331604], "rec_polys": [[[33, 89], [88, 89], [88, 105], [33, 105]], [[142, 87], [200, 87], [200, 106], [142, 106]], [[252, 87], [310, 87], [310, 106], [252, 106]], [[361, 86], [421, 86], [421, 108], [361, 108]], [[470, 86], [531, 86], [531, 108], [470, 108]], [[577, 86], [643, 86], [643, 108], [577, 108]], [[6, 109], [68, 113], [67, 136], [5, 132]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[225, 108], [289, 112], [287, 138], [223, 134]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[32, 229], [91, 229], [91, 248], [32, 248]], [[143, 229], [199, 229], [199, 248], [143, 248]], [[252, 228], [310, 228], [310, 250], [252, 250]], [[362, 229], [419, 229], [419, 248], [362, 248]], [[473, 229], [529, 229], [529, 248], [473, 248]], [[584, 231], [637, 231], [637, 247], [584, 247]], [[6, 251], [45, 255], [43, 280], [3, 276]], [[115, 252], [155, 255], [153, 280], [113, 276]], [[226, 251], [265, 255], [263, 280], [223, 276]], [[335, 252], [375, 255], [373, 280], [333, 276]], [[445, 251], [485, 254], [483, 280], [443, 276]], [[556, 251], [618, 255], [616, 278], [554, 274]]], "rec_boxes": [[33, 89, 88, 105], [142, 87, 200, 106], [252, 87, 310, 106], [361, 86, 421, 108], [470, 86, 531, 108], [577, 86, 643, 108], [5, 109, 68, 136], [114, 109, 179, 137], [223, 108, 289, 138], [333, 109, 399, 137], [446, 111, 641, 135], [32, 229, 91, 248], [143, 229, 199, 248], [252, 228, 310, 250], [362, 229, 419, 248], [473, 229, 529, 248], [584, 231, 637, 247], [3, 251, 45, 280], [113, 252, 155, 280], [223, 251, 265, 280], [333, 252, 375, 280], [443, 251, 485, 280], [554, 251, 618, 278]]}}