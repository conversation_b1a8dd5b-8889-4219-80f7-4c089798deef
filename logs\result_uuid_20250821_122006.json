{"res": {"input_path": "logs\\debug_uuid_screenshot_20250821_122006.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[34, 23], [133, 23], [133, 82], [34, 82]], [[339, 38], [419, 38], [419, 75], [339, 75]], [[31, 87], [90, 87], [90, 106], [31, 106]], [[141, 87], [200, 87], [200, 106], [141, 106]], [[253, 89], [309, 89], [309, 105], [253, 105]], [[362, 88], [420, 88], [420, 107], [362, 107]], [[472, 87], [528, 87], [528, 106], [472, 106]], [[579, 89], [641, 89], [641, 105], [579, 105]], [[5, 109], [69, 112], [68, 137], [4, 135]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[225, 109], [289, 112], [288, 137], [224, 135]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[31, 229], [91, 229], [91, 248], [31, 248]], [[141, 229], [199, 229], [199, 248], [141, 248]], [[251, 228], [311, 228], [311, 250], [251, 250]], [[358, 228], [421, 228], [421, 250], [358, 250]], [[472, 229], [529, 229], [529, 248], [472, 248]], [[582, 229], [638, 229], [638, 248], [582, 248]], [[6, 251], [52, 255], [50, 280], [3, 275]], [[116, 251], [162, 255], [160, 280], [113, 275]], [[225, 249], [273, 254], [271, 281], [222, 276]], [[335, 251], [375, 254], [373, 280], [333, 276]], [[445, 251], [485, 254], [483, 280], [443, 276]], [[555, 252], [595, 255], [593, 280], [553, 276]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图4.jpg", "主图2.jpg", "主图3.jpg", "主图1.jpg", "186c1862-9663 8001200.jpg", "900x900", "900x900", "900x900", "900x900", "900x900", "900x900", "12.jpg", "11.jpg", "10,jpg", "9.jpg", "8.jpg", "7.jpg"], "rec_scores": [0.9276787638664246, 0.9319024085998535, 0.964849591255188, 0.9762051701545715, 0.97828209400177, 0.9835405945777893, 0.9113383293151855, 0.9690300226211548, 0.941493809223175, 0.9488903284072876, 0.933769941329956, 0.9542133212089539, 0.9476502537727356, 0.9314253926277161, 0.8862391114234924, 0.9277456998825073, 0.933587908744812, 0.9901242852210999, 0.9589633345603943, 0.9333004951477051, 0.960169792175293, 0.9813787341117859, 0.990766167640686], "rec_polys": [[[31, 87], [90, 87], [90, 106], [31, 106]], [[141, 87], [200, 87], [200, 106], [141, 106]], [[253, 89], [309, 89], [309, 105], [253, 105]], [[362, 88], [420, 88], [420, 107], [362, 107]], [[472, 87], [528, 87], [528, 106], [472, 106]], [[579, 89], [641, 89], [641, 105], [579, 105]], [[5, 109], [69, 112], [68, 137], [4, 135]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[225, 109], [289, 112], [288, 137], [224, 135]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[31, 229], [91, 229], [91, 248], [31, 248]], [[141, 229], [199, 229], [199, 248], [141, 248]], [[251, 228], [311, 228], [311, 250], [251, 250]], [[358, 228], [421, 228], [421, 250], [358, 250]], [[472, 229], [529, 229], [529, 248], [472, 248]], [[582, 229], [638, 229], [638, 248], [582, 248]], [[6, 251], [52, 255], [50, 280], [3, 275]], [[116, 251], [162, 255], [160, 280], [113, 275]], [[225, 249], [273, 254], [271, 281], [222, 276]], [[335, 251], [375, 254], [373, 280], [333, 276]], [[445, 251], [485, 254], [483, 280], [443, 276]], [[555, 252], [595, 255], [593, 280], [553, 276]]], "rec_boxes": [[31, 87, 90, 106], [141, 87, 200, 106], [253, 89, 309, 105], [362, 88, 420, 107], [472, 87, 528, 106], [579, 89, 641, 105], [4, 109, 69, 137], [114, 109, 179, 137], [224, 109, 289, 137], [333, 109, 399, 137], [446, 111, 641, 135], [31, 229, 91, 248], [141, 229, 199, 248], [251, 228, 311, 250], [358, 228, 421, 250], [472, 229, 529, 248], [582, 229, 638, 248], [3, 251, 52, 280], [113, 251, 162, 280], [222, 249, 273, 281], [333, 251, 375, 280], [443, 251, 485, 280], [553, 252, 595, 280]]}}