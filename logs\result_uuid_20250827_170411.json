{"res": {"input_path": "logs\\debug_uuid_screenshot_20250827_170411.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[28, 84], [92, 84], [92, 109], [28, 109]], [[140, 86], [201, 86], [201, 108], [140, 108]], [[251, 86], [311, 86], [311, 108], [251, 108]], [[361, 86], [420, 86], [420, 108], [361, 108]], [[471, 86], [530, 86], [530, 108], [471, 108]], [[577, 86], [643, 86], [643, 108], [577, 108]], [[5, 108], [69, 111], [68, 137], [4, 135]], [[116, 109], [178, 112], [177, 137], [115, 135]], [[226, 109], [288, 113], [286, 136], [224, 132]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[33, 231], [88, 231], [88, 247], [33, 247]], [[143, 231], [199, 231], [199, 247], [143, 247]], [[252, 229], [311, 229], [311, 248], [252, 248]], [[362, 231], [419, 231], [419, 247], [362, 247]], [[472, 229], [529, 229], [529, 248], [472, 248]], [[583, 229], [639, 229], [639, 248], [583, 248]], [[5, 252], [45, 255], [43, 280], [3, 276]], [[116, 251], [155, 255], [153, 280], [113, 276]], [[226, 251], [265, 255], [263, 280], [223, 276]], [[334, 250], [376, 253], [374, 281], [332, 277]], [[445, 251], [485, 254], [483, 280], [443, 276]], [[555, 250], [619, 254], [617, 279], [553, 275]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图1.jpg", "主图4.jpg", "主图3.jpg", "主图2.jpg", "5bfb960f-4d54.. 800x1200.jpg", "900x900", "900x900", "900x900", "900x900", "900x900", "800x800", "5.jpg", "4.jpg", "3.jpg", "2.jpg", "1.jpg", "主图1.jpg"], "rec_scores": [0.9532913565635681, 0.8837037086486816, 0.9811673760414124, 0.8975147604942322, 0.9595040082931519, 0.9144354462623596, 0.9172719120979309, 0.969749927520752, 0.9226723313331604, 0.9905756115913391, 0.893545925617218, 0.9332539439201355, 0.9863075613975525, 0.9621375799179077, 0.9832183122634888, 0.9650763273239136, 0.9695844650268555, 0.9966920614242554, 0.9902734756469727, 0.9921211004257202, 0.9931166768074036, 0.8986754417419434, 0.9773014187812805], "rec_polys": [[[28, 84], [92, 84], [92, 109], [28, 109]], [[140, 86], [201, 86], [201, 108], [140, 108]], [[251, 86], [311, 86], [311, 108], [251, 108]], [[361, 86], [420, 86], [420, 108], [361, 108]], [[471, 86], [530, 86], [530, 108], [471, 108]], [[577, 86], [643, 86], [643, 108], [577, 108]], [[5, 108], [69, 111], [68, 137], [4, 135]], [[116, 109], [178, 112], [177, 137], [115, 135]], [[226, 109], [288, 113], [286, 136], [224, 132]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[33, 231], [88, 231], [88, 247], [33, 247]], [[143, 231], [199, 231], [199, 247], [143, 247]], [[252, 229], [311, 229], [311, 248], [252, 248]], [[362, 231], [419, 231], [419, 247], [362, 247]], [[472, 229], [529, 229], [529, 248], [472, 248]], [[583, 229], [639, 229], [639, 248], [583, 248]], [[5, 252], [45, 255], [43, 280], [3, 276]], [[116, 251], [155, 255], [153, 280], [113, 276]], [[226, 251], [265, 255], [263, 280], [223, 276]], [[334, 250], [376, 253], [374, 281], [332, 277]], [[445, 251], [485, 254], [483, 280], [443, 276]], [[555, 250], [619, 254], [617, 279], [553, 275]]], "rec_boxes": [[28, 84, 92, 109], [140, 86, 201, 108], [251, 86, 311, 108], [361, 86, 420, 108], [471, 86, 530, 108], [577, 86, 643, 108], [4, 108, 69, 137], [115, 109, 178, 137], [224, 109, 288, 136], [333, 109, 399, 137], [446, 111, 641, 135], [33, 231, 88, 247], [143, 231, 199, 247], [252, 229, 311, 248], [362, 231, 419, 247], [472, 229, 529, 248], [583, 229, 639, 248], [3, 252, 45, 280], [113, 251, 155, 280], [223, 251, 265, 280], [332, 250, 376, 281], [443, 251, 485, 280], [553, 250, 619, 279]]}}