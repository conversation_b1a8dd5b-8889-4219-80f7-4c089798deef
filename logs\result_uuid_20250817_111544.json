{"res": {"input_path": "logs\\debug_uuid_screenshot_20250817_111544.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 87], [90, 87], [90, 106], [32, 106]], [[141, 86], [200, 86], [200, 108], [141, 108]], [[251, 86], [311, 86], [311, 108], [251, 108]], [[361, 86], [420, 86], [420, 108], [361, 108]], [[471, 86], [529, 86], [529, 108], [471, 108]], [[577, 86], [644, 86], [644, 108], [577, 108]], [[5, 109], [69, 112], [68, 137], [4, 135]], [[115, 108], [179, 111], [178, 137], [114, 135]], [[225, 108], [289, 111], [288, 137], [224, 135]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[445, 111], [641, 113], [640, 135], [445, 133]], [[32, 229], [89, 229], [89, 248], [32, 248]], [[143, 229], [199, 229], [199, 248], [143, 248]], [[253, 229], [310, 229], [310, 248], [253, 248]], [[361, 228], [420, 228], [420, 250], [361, 250]], [[472, 227], [529, 227], [529, 249], [472, 249]], [[582, 228], [640, 228], [640, 250], [582, 250]], [[5, 250], [53, 254], [51, 281], [2, 276]], [[114, 250], [156, 253], [154, 281], [112, 277]], [[224, 250], [266, 253], [264, 281], [222, 277]], [[333, 250], [376, 253], [374, 281], [331, 277]], [[444, 250], [486, 253], [484, 281], [442, 277]], [[555, 251], [595, 254], [592, 280], [553, 276]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图3.jpg", "主图4.jpg", "主图2.jpg", "主图1.jpg", "b3b2e980-ebf3. 800x1200.jpg", "900x900", "900x900", "900x900", "900x900", "900x900", "900x900", "10,jpg", "9.jpg", "8.jpg", "7.jpg", "6.jpg", "4.jpg"], "rec_scores": [0.9781094193458557, 0.9449211955070496, 0.9129158854484558, 0.9555619359016418, 0.9121618270874023, 0.9742389917373657, 0.941493809223175, 0.9814883470535278, 0.9859214425086975, 0.9488903284072876, 0.8901093602180481, 0.9617831110954285, 0.9656501412391663, 0.9369253516197205, 0.9157241582870483, 0.9444528222084045, 0.9382454752922058, 0.9369320869445801, 0.9934015274047852, 0.9855512380599976, 0.9540192484855652, 0.9798328280448914, 0.9767999649047852], "rec_polys": [[[32, 87], [90, 87], [90, 106], [32, 106]], [[141, 86], [200, 86], [200, 108], [141, 108]], [[251, 86], [311, 86], [311, 108], [251, 108]], [[361, 86], [420, 86], [420, 108], [361, 108]], [[471, 86], [529, 86], [529, 108], [471, 108]], [[577, 86], [644, 86], [644, 108], [577, 108]], [[5, 109], [69, 112], [68, 137], [4, 135]], [[115, 108], [179, 111], [178, 137], [114, 135]], [[225, 108], [289, 111], [288, 137], [224, 135]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[445, 111], [641, 113], [640, 135], [445, 133]], [[32, 229], [89, 229], [89, 248], [32, 248]], [[143, 229], [199, 229], [199, 248], [143, 248]], [[253, 229], [310, 229], [310, 248], [253, 248]], [[361, 228], [420, 228], [420, 250], [361, 250]], [[472, 227], [529, 227], [529, 249], [472, 249]], [[582, 228], [640, 228], [640, 250], [582, 250]], [[5, 250], [53, 254], [51, 281], [2, 276]], [[114, 250], [156, 253], [154, 281], [112, 277]], [[224, 250], [266, 253], [264, 281], [222, 277]], [[333, 250], [376, 253], [374, 281], [331, 277]], [[444, 250], [486, 253], [484, 281], [442, 277]], [[555, 251], [595, 254], [592, 280], [553, 276]]], "rec_boxes": [[32, 87, 90, 106], [141, 86, 200, 108], [251, 86, 311, 108], [361, 86, 420, 108], [471, 86, 529, 108], [577, 86, 644, 108], [4, 109, 69, 137], [114, 108, 179, 137], [224, 108, 289, 137], [333, 109, 399, 137], [445, 111, 641, 135], [32, 229, 89, 248], [143, 229, 199, 248], [253, 229, 310, 248], [361, 228, 420, 250], [472, 227, 529, 249], [582, 228, 640, 250], [2, 250, 53, 281], [112, 250, 156, 281], [222, 250, 266, 281], [331, 250, 376, 281], [442, 250, 486, 281], [553, 251, 595, 280]]}}