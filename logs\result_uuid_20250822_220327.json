{"res": {"input_path": "logs\\debug_uuid_screenshot_20250822_220327.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[142, 87], [200, 87], [200, 106], [142, 106]], [[251, 86], [310, 86], [310, 108], [251, 108]], [[362, 87], [420, 87], [420, 106], [362, 106]], [[472, 87], [529, 87], [529, 106], [472, 106]], [[577, 87], [643, 87], [643, 106], [577, 106]], [[6, 110], [68, 113], [67, 135], [5, 133]], [[115, 108], [179, 112], [177, 138], [113, 134]], [[225, 108], [289, 112], [287, 138], [223, 134]], [[335, 108], [399, 112], [397, 137], [333, 133]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[27, 227], [93, 227], [93, 249], [27, 249]], [[139, 229], [214, 229], [214, 248], [139, 248]], [[247, 229], [314, 229], [314, 248], [247, 248]], [[357, 229], [424, 229], [424, 248], [357, 248]], [[456, 225], [534, 228], [533, 250], [456, 247]], [[579, 229], [643, 229], [643, 248], [579, 248]], [[7, 251], [52, 255], [50, 280], [4, 275]], [[116, 251], [162, 257], [160, 280], [113, 275]], [[225, 251], [272, 255], [270, 280], [223, 275]], [[335, 251], [382, 255], [380, 280], [333, 275]], [[445, 251], [492, 255], [490, 280], [443, 275]], [[555, 251], [602, 255], [599, 280], [553, 275]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图1.jpg", "主图2.jpg", "主图3.jpg", "主图4.jpg", "cbd41800-8ef4.. 800x1200.jpg", "900x1051", "900x1058", "900x1054", "900x1057", "900x1153", "900x1008", "20.jpg", "19.jpg", "18.jpg", "17.jpg", "16.jpg", "15.jpg"], "rec_scores": [0.9747820496559143, 0.9687095284461975, 0.9163883328437805, 0.9731342196464539, 0.9778376817703247, 0.9888474345207214, 0.9624563455581665, 0.894110381603241, 0.8930421471595764, 0.9450245499610901, 0.8852106928825378, 0.9583926200866699, 0.9879860877990723, 0.9747821092605591, 0.9842852354049683, 0.9485576152801514, 0.9821810722351074, 0.9672334790229797, 0.9474053382873535, 0.9963609576225281, 0.9869281649589539, 0.9928261637687683, 0.9551813006401062], "rec_polys": [[[32, 87], [89, 87], [89, 106], [32, 106]], [[142, 87], [200, 87], [200, 106], [142, 106]], [[251, 86], [310, 86], [310, 108], [251, 108]], [[362, 87], [420, 87], [420, 106], [362, 106]], [[472, 87], [529, 87], [529, 106], [472, 106]], [[577, 87], [643, 87], [643, 106], [577, 106]], [[6, 110], [68, 113], [67, 135], [5, 133]], [[115, 108], [179, 112], [177, 138], [113, 134]], [[225, 108], [289, 112], [287, 138], [223, 134]], [[335, 108], [399, 112], [397, 137], [333, 133]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[27, 227], [93, 227], [93, 249], [27, 249]], [[139, 229], [214, 229], [214, 248], [139, 248]], [[247, 229], [314, 229], [314, 248], [247, 248]], [[357, 229], [424, 229], [424, 248], [357, 248]], [[456, 225], [534, 228], [533, 250], [456, 247]], [[579, 229], [643, 229], [643, 248], [579, 248]], [[7, 251], [52, 255], [50, 280], [4, 275]], [[116, 251], [162, 257], [160, 280], [113, 275]], [[225, 251], [272, 255], [270, 280], [223, 275]], [[335, 251], [382, 255], [380, 280], [333, 275]], [[445, 251], [492, 255], [490, 280], [443, 275]], [[555, 251], [602, 255], [599, 280], [553, 275]]], "rec_boxes": [[32, 87, 89, 106], [142, 87, 200, 106], [251, 86, 310, 108], [362, 87, 420, 106], [472, 87, 529, 106], [577, 87, 643, 106], [5, 110, 68, 135], [113, 108, 179, 138], [223, 108, 289, 138], [333, 108, 399, 137], [446, 111, 641, 135], [27, 227, 93, 249], [139, 229, 214, 248], [247, 229, 314, 248], [357, 229, 424, 248], [456, 225, 534, 250], [579, 229, 643, 248], [4, 251, 52, 280], [113, 251, 162, 280], [223, 251, 272, 280], [333, 251, 382, 280], [443, 251, 492, 280], [553, 251, 602, 280]]}}