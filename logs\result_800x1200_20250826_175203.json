{"res": {"input_path": "logs\\debug_800x1200_screenshot_20250826_175203.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[34, 91], [92, 91], [92, 110], [34, 110]], [[145, 93], [200, 93], [200, 109], [145, 109]], [[254, 91], [312, 91], [312, 110], [254, 110]], [[364, 91], [422, 91], [422, 110], [364, 110]], [[475, 93], [529, 93], [529, 109], [475, 109]], [[580, 91], [645, 91], [645, 110], [580, 110]], [[8, 114], [69, 118], [68, 140], [7, 136]], [[118, 114], [179, 117], [178, 139], [117, 137]], [[229, 114], [290, 117], [289, 139], [228, 137]], [[338, 113], [400, 117], [398, 140], [336, 136]], [[448, 115], [643, 117], [642, 139], [448, 137]], [[30, 233], [95, 233], [95, 252], [30, 252]], [[141, 233], [205, 233], [205, 252], [141, 252]], [[251, 233], [315, 233], [315, 252], [251, 252]], [[362, 235], [424, 235], [424, 251], [362, 251]], [[471, 233], [536, 233], [536, 252], [471, 252]], [[581, 233], [644, 233], [644, 252], [581, 252]], [[8, 259], [52, 259], [52, 282], [8, 282]], [[118, 256], [163, 259], [161, 282], [116, 279]], [[227, 256], [267, 259], [265, 284], [225, 280]], [[337, 256], [377, 259], [374, 284], [335, 280]], [[447, 256], [486, 259], [483, 284], [445, 280]], [[558, 259], [596, 259], [596, 282], [558, 282]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图3.jpg", "主图2.jpg", "主图4.jpg", "主图1.jpg", "466719b5-bfa4.. 800x1200.jpg", "900x1200", "900x1200", "900x1200", "900x1200", "900x1200", "900x1200", "11.jpg", "10.jpg", "9.jpg", "8.jpg", "7.jpg", "6.jpg"], "rec_scores": [0.9709130525588989, 0.973942756652832, 0.963883101940155, 0.9455442428588867, 0.9719203114509583, 0.9646090269088745, 0.9078949093818665, 0.9787489175796509, 0.9629461169242859, 0.9499945640563965, 0.8758912682533264, 0.9842796921730042, 0.9896085262298584, 0.9937488436698914, 0.9780740141868591, 0.991951584815979, 0.9918085932731628, 0.9640131592750549, 0.9182401299476624, 0.9945552945137024, 0.9969772100448608, 0.9885570406913757, 0.9901418685913086], "rec_polys": [[[34, 91], [92, 91], [92, 110], [34, 110]], [[145, 93], [200, 93], [200, 109], [145, 109]], [[254, 91], [312, 91], [312, 110], [254, 110]], [[364, 91], [422, 91], [422, 110], [364, 110]], [[475, 93], [529, 93], [529, 109], [475, 109]], [[580, 91], [645, 91], [645, 110], [580, 110]], [[8, 114], [69, 118], [68, 140], [7, 136]], [[118, 114], [179, 117], [178, 139], [117, 137]], [[229, 114], [290, 117], [289, 139], [228, 137]], [[338, 113], [400, 117], [398, 140], [336, 136]], [[448, 115], [643, 117], [642, 139], [448, 137]], [[30, 233], [95, 233], [95, 252], [30, 252]], [[141, 233], [205, 233], [205, 252], [141, 252]], [[251, 233], [315, 233], [315, 252], [251, 252]], [[362, 235], [424, 235], [424, 251], [362, 251]], [[471, 233], [536, 233], [536, 252], [471, 252]], [[581, 233], [644, 233], [644, 252], [581, 252]], [[8, 259], [52, 259], [52, 282], [8, 282]], [[118, 256], [163, 259], [161, 282], [116, 279]], [[227, 256], [267, 259], [265, 284], [225, 280]], [[337, 256], [377, 259], [374, 284], [335, 280]], [[447, 256], [486, 259], [483, 284], [445, 280]], [[558, 259], [596, 259], [596, 282], [558, 282]]], "rec_boxes": [[34, 91, 92, 110], [145, 93, 200, 109], [254, 91, 312, 110], [364, 91, 422, 110], [475, 93, 529, 109], [580, 91, 645, 110], [7, 114, 69, 140], [117, 114, 179, 139], [228, 114, 290, 139], [336, 113, 400, 140], [448, 115, 643, 139], [30, 233, 95, 252], [141, 233, 205, 252], [251, 233, 315, 252], [362, 235, 424, 251], [471, 233, 536, 252], [581, 233, 644, 252], [8, 259, 52, 282], [116, 256, 163, 282], [225, 256, 267, 284], [335, 256, 377, 284], [445, 256, 486, 284], [558, 259, 596, 282]]}}