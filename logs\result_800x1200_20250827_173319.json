{"res": {"input_path": "logs\\debug_800x1200_screenshot_20250827_173319.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[33, 90], [92, 90], [92, 109], [33, 109]], [[97, 93], [110, 93], [110, 104], [97, 104]], [[144, 91], [201, 91], [201, 110], [144, 110]], [[254, 91], [311, 91], [311, 110], [254, 110]], [[364, 91], [422, 91], [422, 110], [364, 110]], [[475, 93], [529, 93], [529, 109], [475, 109]], [[580, 91], [645, 91], [645, 110], [580, 110]], [[8, 113], [70, 117], [69, 140], [7, 136]], [[118, 113], [179, 117], [177, 140], [116, 136]], [[228, 113], [290, 117], [288, 140], [226, 136]], [[338, 113], [400, 117], [398, 140], [336, 136]], [[448, 115], [643, 117], [642, 139], [448, 137]], [[33, 233], [91, 233], [91, 252], [33, 252]], [[144, 233], [201, 233], [201, 252], [144, 252]], [[253, 233], [311, 233], [311, 252], [253, 252]], [[364, 233], [422, 233], [422, 252], [364, 252]], [[474, 233], [530, 233], [530, 252], [474, 252]], [[584, 233], [640, 233], [640, 252], [584, 252]], [[9, 256], [47, 259], [45, 284], [6, 280]], [[117, 255], [157, 258], [155, 284], [115, 280]], [[227, 256], [267, 259], [265, 284], [225, 280]], [[337, 255], [377, 258], [375, 284], [335, 280]], [[448, 256], [487, 259], [485, 284], [446, 280]], [[558, 255], [620, 259], [618, 282], [556, 278]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图4.jpg", "主图2.jpg", "主图3.jpg", "主图1.jpg", "5a41426a-c4d9.… 800x1200.jpg", "900x900", "900x900", "900x900", "900x900", "900x900", "800x800", "5.jpg", "4.jpg", "3.jpg", "2.jpg", "1.jpg", "主图1.jpg"], "rec_scores": [0.9650448560714722, 0.9816240072250366, 0.9815692901611328, 0.9705959558486938, 0.9790574312210083, 0.9715660810470581, 0.9218339920043945, 0.9792531728744507, 0.9226723313331604, 0.9499945640563965, 0.8943870663642883, 0.9503656625747681, 0.9370924234390259, 0.9540114402770996, 0.939812958240509, 0.9638210535049438, 0.9831448197364807, 0.993071436882019, 0.9806004762649536, 0.9970253705978394, 0.9762643575668335, 0.9922725558280945, 0.950182318687439], "rec_polys": [[[33, 90], [92, 90], [92, 109], [33, 109]], [[144, 91], [201, 91], [201, 110], [144, 110]], [[254, 91], [311, 91], [311, 110], [254, 110]], [[364, 91], [422, 91], [422, 110], [364, 110]], [[475, 93], [529, 93], [529, 109], [475, 109]], [[580, 91], [645, 91], [645, 110], [580, 110]], [[8, 113], [70, 117], [69, 140], [7, 136]], [[118, 113], [179, 117], [177, 140], [116, 136]], [[228, 113], [290, 117], [288, 140], [226, 136]], [[338, 113], [400, 117], [398, 140], [336, 136]], [[448, 115], [643, 117], [642, 139], [448, 137]], [[33, 233], [91, 233], [91, 252], [33, 252]], [[144, 233], [201, 233], [201, 252], [144, 252]], [[253, 233], [311, 233], [311, 252], [253, 252]], [[364, 233], [422, 233], [422, 252], [364, 252]], [[474, 233], [530, 233], [530, 252], [474, 252]], [[584, 233], [640, 233], [640, 252], [584, 252]], [[9, 256], [47, 259], [45, 284], [6, 280]], [[117, 255], [157, 258], [155, 284], [115, 280]], [[227, 256], [267, 259], [265, 284], [225, 280]], [[337, 255], [377, 258], [375, 284], [335, 280]], [[448, 256], [487, 259], [485, 284], [446, 280]], [[558, 255], [620, 259], [618, 282], [556, 278]]], "rec_boxes": [[33, 90, 92, 109], [144, 91, 201, 110], [254, 91, 311, 110], [364, 91, 422, 110], [475, 93, 529, 109], [580, 91, 645, 110], [7, 113, 70, 140], [116, 113, 179, 140], [226, 113, 290, 140], [336, 113, 400, 140], [448, 115, 643, 139], [33, 233, 91, 252], [144, 233, 201, 252], [253, 233, 311, 252], [364, 233, 422, 252], [474, 233, 530, 252], [584, 233, 640, 252], [6, 256, 47, 284], [115, 255, 157, 284], [225, 256, 267, 284], [335, 255, 377, 284], [446, 256, 487, 284], [556, 255, 620, 282]]}}