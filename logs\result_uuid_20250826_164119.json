{"res": {"input_path": "logs\\debug_uuid_screenshot_20250826_164119.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[31, 87], [90, 87], [90, 106], [31, 106]], [[143, 89], [198, 89], [198, 105], [143, 105]], [[252, 87], [310, 87], [310, 106], [252, 106]], [[361, 87], [420, 87], [420, 106], [361, 106]], [[472, 87], [529, 87], [529, 106], [472, 106]], [[578, 87], [643, 87], [643, 106], [578, 106]], [[5, 109], [69, 112], [68, 137], [4, 135]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[226, 108], [288, 112], [286, 136], [224, 132]], [[336, 108], [398, 112], [396, 137], [334, 133]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[31, 227], [90, 227], [90, 249], [31, 249]], [[143, 229], [200, 229], [200, 248], [143, 248]], [[251, 228], [310, 228], [310, 250], [251, 250]], [[362, 229], [419, 229], [419, 248], [362, 248]], [[473, 229], [528, 229], [528, 248], [473, 248]], [[582, 229], [638, 229], [638, 248], [582, 248]], [[7, 251], [51, 255], [49, 280], [4, 275]], [[115, 251], [155, 254], [153, 280], [113, 276]], [[225, 251], [265, 254], [263, 280], [223, 276]], [[335, 251], [375, 255], [373, 280], [333, 276]], [[445, 251], [485, 255], [483, 280], [443, 276]], [[556, 251], [595, 256], [592, 280], [553, 275]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图1.jpg", "主图2.jpg", "主图3.jpg", "主图4.jpg", "a57b17bf-9339.. 800x1200.jpg", "900x900", "900×900", "900×900", "900x900", "900x900", "900x900", "10.jpg", "9.jpg", "8.jpg", "7.jpg", "6.jpg", "4.jpg"], "rec_scores": [0.9078828692436218, 0.9871086478233337, 0.9759782552719116, 0.9298800230026245, 0.974819540977478, 0.9884442687034607, 0.967612624168396, 0.9690300226211548, 0.971010684967041, 0.9493737816810608, 0.9038699269294739, 0.9009187817573547, 0.9277861714363098, 0.8914684653282166, 0.9314430952072144, 0.92972332239151, 0.941215455532074, 0.9402090907096863, 0.960169792175293, 0.9813787341117859, 0.9730637669563293, 0.9893655776977539, 0.9921063184738159], "rec_polys": [[[31, 87], [90, 87], [90, 106], [31, 106]], [[143, 89], [198, 89], [198, 105], [143, 105]], [[252, 87], [310, 87], [310, 106], [252, 106]], [[361, 87], [420, 87], [420, 106], [361, 106]], [[472, 87], [529, 87], [529, 106], [472, 106]], [[578, 87], [643, 87], [643, 106], [578, 106]], [[5, 109], [69, 112], [68, 137], [4, 135]], [[115, 109], [179, 112], [178, 137], [114, 135]], [[226, 108], [288, 112], [286, 136], [224, 132]], [[336, 108], [398, 112], [396, 137], [334, 133]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[31, 227], [90, 227], [90, 249], [31, 249]], [[143, 229], [200, 229], [200, 248], [143, 248]], [[251, 228], [310, 228], [310, 250], [251, 250]], [[362, 229], [419, 229], [419, 248], [362, 248]], [[473, 229], [528, 229], [528, 248], [473, 248]], [[582, 229], [638, 229], [638, 248], [582, 248]], [[7, 251], [51, 255], [49, 280], [4, 275]], [[115, 251], [155, 254], [153, 280], [113, 276]], [[225, 251], [265, 254], [263, 280], [223, 276]], [[335, 251], [375, 255], [373, 280], [333, 276]], [[445, 251], [485, 255], [483, 280], [443, 276]], [[556, 251], [595, 256], [592, 280], [553, 275]]], "rec_boxes": [[31, 87, 90, 106], [143, 89, 198, 105], [252, 87, 310, 106], [361, 87, 420, 106], [472, 87, 529, 106], [578, 87, 643, 106], [4, 109, 69, 137], [114, 109, 179, 137], [224, 108, 288, 136], [334, 108, 398, 137], [446, 111, 641, 135], [31, 227, 90, 249], [143, 229, 200, 248], [251, 228, 310, 250], [362, 229, 419, 248], [473, 229, 528, 248], [582, 229, 638, 248], [4, 251, 51, 280], [113, 251, 155, 280], [223, 251, 265, 280], [333, 251, 375, 280], [443, 251, 485, 280], [553, 251, 595, 280]]}}