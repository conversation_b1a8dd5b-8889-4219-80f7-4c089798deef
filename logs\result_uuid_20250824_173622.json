{"res": {"input_path": "logs\\debug_uuid_screenshot_20250824_173622.png", "page_index": null, "model_settings": {"use_doc_preprocessor": true, "use_textline_orientation": false}, "doc_preprocessor_res": {"input_path": null, "page_index": null, "model_settings": {"use_doc_orientation_classify": false, "use_doc_unwarping": false}, "angle": -1}, "dt_polys": [[[33, 89], [88, 89], [88, 105], [33, 105]], [[142, 87], [199, 87], [199, 106], [142, 106]], [[252, 85], [310, 88], [309, 107], [251, 104]], [[361, 86], [421, 86], [421, 108], [361, 108]], [[471, 86], [530, 86], [530, 108], [471, 108]], [[577, 83], [644, 86], [643, 108], [577, 105]], [[6, 110], [67, 113], [66, 135], [5, 133]], [[116, 109], [178, 113], [177, 136], [114, 132]], [[226, 109], [288, 112], [287, 137], [225, 135]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[33, 231], [88, 231], [88, 247], [33, 247]], [[142, 229], [199, 229], [199, 248], [142, 248]], [[252, 229], [310, 229], [310, 248], [252, 248]], [[363, 231], [419, 231], [419, 247], [363, 247]], [[474, 231], [527, 231], [527, 247], [474, 247]], [[583, 229], [639, 229], [639, 248], [583, 248]], [[5, 253], [176, 256], [175, 277], [5, 274]], [[226, 251], [288, 255], [286, 278], [224, 274]], [[336, 251], [398, 255], [396, 278], [334, 274]], [[446, 251], [508, 255], [506, 278], [444, 274]], [[556, 251], [618, 255], [616, 278], [554, 274]]], "text_det_params": {"limit_side_len": 960, "limit_type": "max", "thresh": 0.3, "max_side_limit": 4000, "box_thresh": 0.5, "unclip_ratio": 2.0}, "text_type": "general", "textline_orientation_angles": [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "text_rec_score_thresh": 0.3, "rec_texts": ["800x800", "800x800", "800x800", "800x800", "800x800", "800x1200", "主图2.jpg", "主图4.jpg", "主图3.jpg", "主图1.jpg", "033dae52-7540. 800x1200.jpg", "800x800", "800x800", "800x800", "800x800", "800x800", "800x800", "O1CN01x5azEk.主图11.jpg", "主图1.jpg", "主图1.jpg", "主图1.jpg", "主图1.jpg"], "rec_scores": [0.9777817726135254, 0.9846199750900269, 0.963681697845459, 0.9347301125526428, 0.9249295592308044, 0.9200255870819092, 0.9787489175796509, 0.9495887160301208, 0.9670919179916382, 0.9488903284072876, 0.8975660800933838, 0.9720727205276489, 0.9712148308753967, 0.9717420935630798, 0.9847240447998047, 0.9656057953834534, 0.9808334708213806, 0.9460976719856262, 0.9499945640563965, 0.9499945640563965, 0.9499945640563965, 0.9499945640563965], "rec_polys": [[[33, 89], [88, 89], [88, 105], [33, 105]], [[142, 87], [199, 87], [199, 106], [142, 106]], [[252, 85], [310, 88], [309, 107], [251, 104]], [[361, 86], [421, 86], [421, 108], [361, 108]], [[471, 86], [530, 86], [530, 108], [471, 108]], [[577, 83], [644, 86], [643, 108], [577, 105]], [[6, 110], [67, 113], [66, 135], [5, 133]], [[116, 109], [178, 113], [177, 136], [114, 132]], [[226, 109], [288, 112], [287, 137], [225, 135]], [[335, 109], [399, 112], [398, 137], [333, 135]], [[446, 111], [641, 113], [640, 135], [446, 133]], [[33, 231], [88, 231], [88, 247], [33, 247]], [[142, 229], [199, 229], [199, 248], [142, 248]], [[252, 229], [310, 229], [310, 248], [252, 248]], [[363, 231], [419, 231], [419, 247], [363, 247]], [[474, 231], [527, 231], [527, 247], [474, 247]], [[583, 229], [639, 229], [639, 248], [583, 248]], [[5, 253], [176, 256], [175, 277], [5, 274]], [[226, 251], [288, 255], [286, 278], [224, 274]], [[336, 251], [398, 255], [396, 278], [334, 274]], [[446, 251], [508, 255], [506, 278], [444, 274]], [[556, 251], [618, 255], [616, 278], [554, 274]]], "rec_boxes": [[33, 89, 88, 105], [142, 87, 199, 106], [251, 85, 310, 107], [361, 86, 421, 108], [471, 86, 530, 108], [577, 83, 644, 108], [5, 110, 67, 135], [114, 109, 178, 136], [225, 109, 288, 137], [333, 109, 399, 137], [446, 111, 641, 135], [33, 231, 88, 247], [142, 229, 199, 248], [252, 229, 310, 248], [363, 231, 419, 247], [474, 231, 527, 247], [583, 229, 639, 248], [5, 253, 176, 277], [224, 251, 288, 278], [334, 251, 398, 278], [444, 251, 508, 278], [554, 251, 618, 278]]}}